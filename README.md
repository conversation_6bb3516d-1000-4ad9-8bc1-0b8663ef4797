# LCT Finance Backend

LCT Finance DeFi 后端服务 - 基于 Spring Boot 的区块链钱包登录系统

## 🚀 项目概述

这是一个基于 Spring Boot 2.7.18 开发的 DeFi 后端服务，主要功能包括：

- **多链钱包登录**：支持 BSC 和 TRX 网络的钱包签名验证
- **用户管理**：用户注册、登录、邀请关系管理
- **JWT 认证**：基于 JWT 的用户认证系统
- **区块链集成**：Web3j 集成，支持以太坊生态签名验证

## 📋 技术栈

- **框架**：Spring Boot 2.7.18
- **数据库**：MySQL 8.0 + MyBatis-Plus 3.5.4
- **认证**：JWT (jjwt 0.11.5)
- **区块链**：Web3j 4.10.0
- **工具**：Lombok, MapStruct

## 🛠️ 快速开始

### 1. 环境要求

- JDK 11+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库配置

```bash
# 创建数据库并导入初始化脚本
mysql -u root -p < src/main/resources/db/init.sql
```

### 3. 配置文件

修改 `src/main/resources/application.yml` 中的数据库连接信息：

```yaml
spring:
  datasource:
    url: *****************************************************************************************************************************************************
    username: your_username
    password: your_password
```

### 4. 启动项目

```bash
# 编译项目
mvn clean compile

# 启动项目
mvn spring-boot:run
```

项目启动后访问：http://localhost:8080

## 📚 API 文档

### 用户登录接口

**POST** `/api/user/login`

**请求参数：**
```json
{
  "chain": "BSC",
  "userAddress": "0x...",
  "inviteCode": "ADMIN001",
  "nativeBalance": "0",
  "tokenBalance": "100",
  "sign": "0x...",
  "message": "You are Login. Nonce:1734567890",
  "nonce": "1734567890",
  "version": 0,
  "captchaKey": ""
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "chain": "BSC",
    "level": 1,
    "address": "0x...",
    "inviteCode": "ABC12345",
    "createdAt": 1734567890,
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "expiresIn": 1734654290,
    "refreshIn": 1735172690
  }
}
```

## 🔐 签名验证流程

### 1. BSC/ETH 签名验证

```javascript
// 前端签名
const message = "You are Login. Nonce:" + nonce;
const signature = await window.ethereum.request({
  "method": "personal_sign",
  "params": [web3.utils.toHex(message), userAddress]
});
```

### 2. TRX 签名验证

```javascript
// TRX V1 签名
const signature = await tronWeb.trx.sign(tronWeb.toHex(message));

// TRX V2 签名
const signature = await tronWeb.trx.signMessageV2(message);
```

## 🏗️ 项目结构

```
src/main/java/com/lct/finance/
├── controller/          # 控制器层
│   └── UserController.java
├── service/               # 服务层
│   ├── UserService.java
│   └── impl/
├── entity/               # 实体类
│   └── User.java
├── dto/                  # 数据传输对象
│   ├── UserLoginRequest.java
│   ├── UserLoginResponse.java
│   └── ApiResponse.java
├── utils/                # 工具类
│   ├── SignatureUtils.java
│   └── JwtUtils.java
├── enums/                # 枚举类
│   └── ErrorCode.java
├── config/               # 配置类
│   ├── CorsConfig.java
│   └── MyBatisPlusConfig.java
└── exception/            # 异常处理
    └── GlobalExceptionHandler.java
```

## 🔧 核心功能

### 1. 钱包签名验证

- **BSC/ETH**：使用 Web3j 验证 personal_sign 签名
- **TRX**：支持 TronLink 的两种签名方式
- **时间窗口**：10分钟内的 nonce 有效期验证

### 2. 用户注册登录

- **自动注册**：首次登录自动创建用户
- **邀请系统**：支持邀请码注册（必需）
- **多链支持**：同一用户可在不同链上登录

### 3. JWT 认证

- **Token 生成**：基于用户地址生成 JWT
- **过期时间**：24小时有效期，7天刷新期
- **安全性**：HS512 算法签名

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 403 | 参数错误 |
| 500 | 内部服务错误 |
| 1010 | 不支持的区块链网络 |
| 1011 | 签名验证失败 |
| 1012 | 钱包地址格式错误 |
| 1014 | 需要有效的邀请码 |
| 1019 | 时间戳过期 |

## 🔄 与前端集成

前端项目需要配置 API 地址：

```javascript
// 前端配置
function getApiUrl() {
    if (host === 'localhost' || host === '127.0.0.1') {
        return 'http://localhost:8080'; // Java后端地址
    }
    return 'https://api.' + host;
}
```

## 📝 开发说明

### 1. 添加新的区块链支持

1. 在 `SignatureUtils` 中添加新的签名验证方法
2. 在 `UserController` 中添加对应的验证逻辑
3. 更新 `SUPPORTED_CHAINS` 列表

### 2. 扩展用户功能

1. 在 `User` 实体类中添加新字段
2. 更新数据库表结构
3. 在 `UserService` 中添加相应的业务逻辑

## 🐛 常见问题

### 1. 签名验证失败

- 检查前端签名格式是否正确
- 确认消息格式：`"You are Login. Nonce:" + nonce`
- 验证时间戳是否在有效期内

### 2. 数据库连接失败

- 检查 MySQL 服务是否启动
- 确认数据库连接配置是否正确
- 验证数据库用户权限

### 3. CORS 跨域问题

- 检查 `CorsConfig` 配置
- 确认前端域名是否在允许列表中

## 📄 许可证

MIT License

## 👥 贡献

欢迎提交 Issue 和 Pull Request！

---

**注意**：这是一个基础版本的实现，生产环境使用前请进行充分的安全测试和性能优化。