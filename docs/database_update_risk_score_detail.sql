-- 为用户登录日志表添加风险评分详情字段
-- 执行时间: 2024-01-01
-- 说明: 添加risk_score_detail字段，用于保存风险评估的详细计算过程

-- 添加风险评分详情字段
ALTER TABLE `lct_user_login_log` 
ADD COLUMN `risk_score_detail` TEXT COMMENT '风险评分详情(JSON格式)' AFTER `risk_level`;

-- 添加索引优化查询性能（可选）
-- CREATE INDEX idx_login_log_risk_level ON lct_user_login_log(risk_level);
-- CREATE INDEX idx_login_log_user_address ON lct_user_login_log(user_address);

-- 查看表结构确认更新
-- DESC lct_user_login_log; 