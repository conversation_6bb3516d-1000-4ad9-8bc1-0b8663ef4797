# LCT Finance DApp 前后端接口文档

## 目录

1. [概述](#概述)
2. [接口通用规范](#接口通用规范)
3. [用户模块](#用户模块)
4. [首页模块](#首页模块)
5. [销毁模块](#销毁模块)
6. [兑换模块](#兑换模块)
7. [钱包模块](#钱包模块)
8. [公告模块](#公告模块)
9. [数据库表结构](#数据库表结构)
10. [前端页面与API对应关系](#前端页面与API对应关系)

## 概述

本文档详细介绍LCT Finance DApp前后端接口规范，包括各接口的路径、参数、返回值及其操作的数据库表。适用于前端开发人员和后端开发人员进行对接和维护。

- **后端服务基地址**: `/api`
- **认证方式**: JWT Token (Bearer认证)
- **数据格式**: JSON
- **接口命名规则**: 以资源为中心，使用RESTful设计风格

**系统业务流程图：**

```mermaid
flowchart TB
    subgraph 系统业务流程
        User["用户"] -->|登录/注册| Auth["身份认证"]
        Auth -->|签名验证| JWT["获取JWT令牌"]
        
        User -->|销毁代币| Burn["代币销毁"]
        Burn -->|生成理财基数| MintBase["铸造基数"]
        Burn -->|计算奖励| DirectReward["直推奖励"]
        
        MintBase -->|每日计算收益| MintProfit["铸造收益"]
        MintProfit -->|存入钱包| Wallet["用户钱包"]
        
        User -->|兑换代币| Exchange["兑换"]
        Exchange -->|获得代币| Wallet
        
        User -->|提现申请| Withdrawal["提现"]
        Withdrawal -->|审核通过| Chain["区块链转账"]
        Wallet -->|扣除余额| Withdrawal
        
        User -->|邀请他人| Invite["邀请关系"]
        Invite -->|下级销毁| TeamProfit["团队收益"]
        TeamProfit -->|增加收益| Wallet
        
        User -->|每日签到| Checkin["签到"]
        Checkin -->|生成奖励| CheckinReward["签到奖励"]
        CheckinReward -->|增加铸造基数| MintBase
    end
```

## 接口通用规范

### 认证机制

系统使用基于区块链的身份认证:

1. 客户端通过钱包生成签名
2. 服务端验证签名有效性
3. 服务端返回JWT令牌
4. 后续请求使用JWT令牌认证

### 通用响应格式

```json
{
  "code": 200,          // 状态码: 200成功，非200表示错误
  "message": "success", // 状态信息
  "data": {}            // 响应数据
}
```

### 错误码说明

- 200: 成功
- 400: 请求参数错误
- 401: 未认证或认证失败
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误
- 1001-1999: 业务相关错误码

## 用户模块

### 1.1 用户登录/注册

**接口信息：**
- **路径**: `/api/user/login`
- **方法**: POST
- **前端调用**: `UserAPI.login(loginData)`
- **认证要求**: 无需认证

**请求参数：**
```json
{
  "chain": "BSC",              // 链名称: BSC, ETH, TRX等
  "userAddress": "0x123...",   // 用户钱包地址
  "inviteCode": "ABC123",      // 邀请码(可选)
  "nativeBalance": "1.2",      // 原生代币余额(可选)
  "tokenBalance": "100.5",     // 代币余额(可选)
  "sign": "0x...",             // 钱包签名
  "message": "You are Login. Nonce:1627893456",  // 签名消息
  "nonce": "1627893456",       // 签名nonce
  "version": "1",              // 签名版本号
  "captchaKey": "..."          // 验证码key(可选)
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "address": "0x123...",
    "level": 1,
    "isNewUser": true,
    "inviteCode": "DEF456"
  }
}
```

**操作的数据表：**
- `users`: 查询或创建用户记录
  - 字段: id, user_address, chain, invite_code, pid, pid_full_path, level, state, created_at, updated_at
- `wallets`: 查询或创建钱包记录
  - 字段: id, user_id, available, finance, burn, checkin, profit, team_profit, direct_invite, frozen, created_at, updated_at
- `invites`: 如有邀请码，创建邀请关系
  - 字段: id, inviter_user_id, invitee_user_id, created_at, updated_at
- `nonces`: 记录和验证nonce，防止重放攻击
  - 字段: id, nonce, type, expired_at, created_at

**接口详细流程图：**

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 服务端API
    participant Auth as 认证服务
    participant Nonce as Nonce服务
    participant DB as 数据库
    participant Blockchain as 区块链节点
    
    Client->>Client: 生成随机nonce
    Client->>Client: 使用钱包私钥对nonce+消息进行签名
    Client->>API: 发送登录请求 /api/user/login
    Note over Client,API: chain, userAddress, inviteCode, sign, message, nonce等参数
    
    API->>API: 验证请求必要参数
    API->>Nonce: 验证nonce是否已被使用
    
    alt nonce已使用
        Nonce-->>API: 返回nonce已使用错误
        API-->>Client: 返回错误: nonce已使用(防重放攻击)
    else nonce未使用
        Nonce-->>API: nonce验证通过
        
        API->>Blockchain: 验证钱包签名(address+message+sign)
        Blockchain-->>API: 返回签名验证结果
        
        alt 签名验证失败
            API-->>Client: 返回错误: 签名验证失败
        else 签名验证成功
            API->>Nonce: 记录已使用的nonce
            
            API->>DB: 查询用户记录(users表)
            DB-->>API: 返回查询结果
            
            alt 用户存在
                API->>DB: 更新用户信息(token_balance等)
            else 用户不存在
                API->>DB: 创建新用户记录(users表)
                API->>DB: 创建钱包记录(wallets表)
                
                alt 包含邀请码
                    API->>DB: 查询邀请码有效性
                    DB-->>API: 返回邀请人信息
                    alt 邀请码有效
                        API->>DB: 创建邀请关系(invites表)
                        API->>DB: 更新邀请路径(pid_full_path)
                    else 邀请码无效
                        API->>API: 忽略无效邀请码
                    end
                end
            end
            
            API->>Auth: 生成JWT令牌(包含用户ID和权限)
            Auth-->>API: 返回JWT令牌
            
            API->>API: 组装响应数据(token, address, level, isNewUser等)
            API-->>Client: 返回登录成功信息和Token
        end
    end
```

**接口安全机制：**

1. **nonce防重放攻击**：
   - 客户端生成随机nonce，加入签名内容
   - 服务端验证nonce是否已被使用
   - nonce使用后记录到数据库，防止重复使用

2. **区块链钱包签名验证**：
   - 使用链上公钥验证签名是否由对应钱包私钥签发
   - 验证签名内容是否包含正确的nonce和消息

3. **JWT令牌认证**：
   - 登录成功后生成JWT令牌，包含用户ID和权限信息
   - 后续请求通过令牌识别用户身份

4. **邀请码验证**：
   - 验证邀请码是否有效
   - 建立正确的邀请关系和层级结构

**错误处理：**

| 错误码 | 错误描述 | 可能原因 |
|-------|---------|---------|
| 400 | 参数错误 | 缺少必要参数(chain, userAddress, sign等) |
| 401 | 签名验证失败 | 钱包签名不匹配或格式错误 |
| 402 | Nonce已使用 | 重放攻击或客户端重复提交 |
| 403 | 邀请码无效 | 提供的邀请码不存在 |
| 500 | 系统错误 | 服务端处理异常 |

### 1.2 获取用户详细信息

**接口信息：**
- **路径**: `/api/user/info`
- **方法**: GET
- **前端调用**: `CompatAPI.getUserInfo()`
- **认证要求**: 需要认证

**请求参数：** 无

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "level": 2,                      // 用户等级
    "available": "100.5",            // 可用余额
    "finance": "1000.0",             // 可理财数量
    "total_burn": "500.0",           // 个人销毁数量
    "total_team_burn": "2500.0",     // 团队总销毁量
    "leader_total_burn": "10000.0",  // 上级总销毁量
    "total_today_profit": "10.5",    // 今日总收益
    "today_profit": "5.2",           // 今日个人收益
    "total_profit": "250.0",         // 总收益
    "today_team_profit": "3.0",      // 今日团队收益
    "today_invite_count": 2,         // 今日邀请人数
    "today_team_invite_count": 5,    // 今日团队邀请人数
    "team_total_count": 30,          // 团队总人数
    "today_team_burn": "100.0",      // 团队今日销毁量
    "roles": 1,                      // 用户角色
    "community_total_burn": "3000.0", // 社区销毁总量
    "community_total_count": 50,      // 社区总人数
    "is_out": 0,                      // 是否出局
    "remaining_profit_out": "1000.0"  // 剩余收益出局限额
  }
}
```

**操作的数据表：**
- `users`: 查询用户等级和角色信息
- `wallets`: 查询用户钱包余额数据
- `burns`: 查询用户销毁数据统计
- `mint_profit`: 查询用户每日铸造收益数据
- `mint_team_profit`: 查询团队收益数据
- `invites`: 查询团队人数和邀请情况
- `mint_base`: 计算出局相关数据
- `old_user_experience`: 查询老用户体验金数据

**字段详细说明：**

| 字段名 | 数据来源 | 计算方式 |
|-------|---------|---------|
| `level` | `users.level` | 直接获取用户等级 |
| `available` | `wallets.available` | 直接获取可用余额 |
| `finance` | `wallets.finance` | 直接获取可理财数量 |
| `total_burn` | `burns.actual_amount` + `wallets.checkin` + `old_user_experience.amount` | 用户总销毁量 + 签到奖励 + 体验金 |
| `total_team_burn` | `burns.actual_amount` | 所有团队成员的销毁总和 |
| `leader_total_burn` | `burns.actual_amount` | 直接上级的销毁总量 |
| `total_today_profit` | `today_profit` + `today_team_profit` | 今日个人收益和团队收益之和 |
| `today_profit` | `mint_profit.amount` | 当日铸造收益总和 |
| `total_profit` | `wallets.profit` + `wallets.team_profit` + `wallets.direct_invite` | 累计收益总和 |
| `today_team_profit` | `mint_team_profit.amount` | 当日团队收益总和 |
| `today_invite_count` | `invites` 记录数 | 今日直推邀请数量(level=1) |
| `today_team_invite_count` | `invites` 记录数 | 今日团队所有邀请数量 |
| `team_total_count` | `invites` 记录数 | 团队总人数 |
| `today_team_burn` | `burns.actual_amount` | 团队今日销毁总量 |
| `roles` | `users.roles` | 用户角色(0:普通用户, 1:社区长) |
| `is_out` | 计算值 | 是否达到收益上限(0:否, 1:是) |
| `remaining_profit_out` | `mint_base.profit_max_amount` - 总收益 | 剩余可获取收益额度 |

**接口流程图：**

```mermaid
sequenceDiagram
    participant 客户端
    participant API控制器
    participant 用户模型
    participant 钱包模型
    participant 销毁模型
    participant 收益模型
    participant 邀请模型
    participant 收益计算服务

    客户端->>API控制器: GET /api/user/info 携带JWT令牌
    API控制器->>用户模型: 获取认证用户
    用户模型-->>API控制器: 返回用户对象
    
    API控制器->>钱包模型: 获取钱包数据
    钱包模型-->>API控制器: 返回钱包信息
    
    API控制器->>收益模型: 查询今日收益
    收益模型-->>API控制器: 返回收益数据
    
    API控制器->>销毁模型: 获取销毁总量
    销毁模型-->>API控制器: 返回销毁数据
    
    API控制器->>邀请模型: 获取团队成员ID
    邀请模型-->>API控制器: 返回团队ID列表
    
    API控制器->>销毁模型: 计算团队销毁统计
    销毁模型-->>API控制器: 返回团队销毁数据
    
    API控制器->>邀请模型: 获取邀请统计
    邀请模型-->>API控制器: 返回邀请计数
    
    API控制器->>收益计算服务: 计算剩余出局收益
    收益计算服务-->>API控制器: 返回出局数据
    
    API控制器->>客户端: 返回完整用户信息
```

**性能优化：**
- 钱包信息缓存(10800秒)
- 销毁总量缓存(600秒)
- 特定用户的特殊日期过滤处理

### 1.3 获取用户礼包状态

**接口信息：**
- **路径**: `/api/user/gift`
- **方法**: GET
- **前端调用**: `UserAPI.getGiftStatus()`
- **认证要求**: 需要认证

**请求参数：** 无

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "isNewUser": true,           // 是否为新用户
    "status": "unclaimed",       // 礼包状态: unclaimed未领取, claimed已领取, expired已过期
    "amount": "100.0",           // 礼包金额
    "expireTime": 1628793456000  // 过期时间戳
  }
}
```

**操作的数据表：**
- `users`: 查询用户注册时间
- `user_gifts`: 查询礼包领取状态
  - 字段: id, user_id, gift_type, amount, status, claimed_at, created_at, updated_at
- `options`: 查询礼包配置信息

### 1.4 领取新用户礼包

**接口信息：**
- **路径**: `/api/user/claimGift`
- **方法**: POST
- **前端调用**: `UserAPI.claimGift()`
- **认证要求**: 需要认证

**请求参数：** 无

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "amount": "100.0",            // 礼包金额
    "status": "claimed",          // 领取状态
    "timestamp": 1628793456000    // 领取时间戳
  }
}
```

**操作的数据表：**
- `user_gifts`: 创建/更新礼包领取记录
- `wallets`: 更新用户钱包余额
- `mint_base`: 创建铸造基数记录
  - 字段: id, user_id, source, source_id, amount, is_effective, created_at, updated_at

### 1.5 更新用户授权信息

**接口信息：**
- **路径**: `/api/user/ua`
- **方法**: POST
- **前端调用**: `UserAPI.updateAllowance(allowanceData)`
- **认证要求**: 需要认证

**请求参数：**
```json
{
  "allowance": "1000.0",           // 授权数量
  "contractAddress": "0x456...",   // 合约地址
  "txhash": "0x789..."             // 交易哈希
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "updated": true
  }
}
```

**操作的数据表：**
- `users`: 更新用户授权信息字段
  - 字段: allowance, allowance_updated_at
- `blockchain_txs`: 记录区块链交易信息
  - 字段: id, user_id, tx_hash, tx_type, status, created_at, updated_at

### 1.6 获取邀请信息

**接口信息：**
- **路径**: `/api/user/invitation`
- **方法**: GET
- **前端调用**: `CompatAPI.getInvitation()`
- **认证要求**: 需要认证

**请求参数：** 无

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "chain": "BSC",                   // 用户链
    "level": 2,                       // 用户等级
    "address": "0x123...",            // 用户地址
    "invite_code": "ABC123",          // 邀请码
    "invite_link": "https://example.com/invite/ABC123",  // 邀请链接
    "created_at": 1628793456000       // 创建时间戳
  }
}
```

**操作的数据表：**
- `users`: 查询用户信息（链、等级、地址、邀请码、创建时间）
- `options`: 查询系统配置，用于生成邀请链接

**字段详细说明：**

| 字段名 | 数据来源 | 计算方式 |
|-------|---------|---------|
| `chain` | `users.chain` | 直接获取用户链 |
| `level` | `users.level` | 直接获取用户等级 |
| `address` | `users.user_address` | 直接获取用户地址 |
| `invite_code` | `users.invite_code` | 直接获取用户邀请码 |
| `invite_link` | 基于系统配置和邀请码生成 | `{app_url}?i={invite_code}` |
| `created_at` | `users.created_at` | 用户创建时间戳 |

**接口流程图：**

```mermaid
sequenceDiagram
    participant 客户端
    participant API控制器
    participant 用户模型
    participant 配置模型
    
    客户端->>API控制器: GET /api/user/invitation 携带JWT令牌
    API控制器->>用户模型: 获取认证用户信息
    用户模型-->>API控制器: 返回用户对象
    API控制器->>配置模型: 获取系统基础配置
    配置模型-->>API控制器: 返回系统配置
    API控制器->>API控制器: 生成邀请链接
    API控制器->>客户端: 返回邀请信息
```

**实现说明：**
- v1版本通过地址参数获取用户信息
- v2版本通过JWT令牌直接获取认证用户
- 邀请链接基于系统配置的app_url和用户邀请码生成

### 1.7 获取直推邀请列表

**接口信息：**
- **路径**: `/api/user/direct_invites`
- **方法**: GET
- **前端调用**: `CompatAPI.getDirectInvites()`
- **认证要求**: 需要认证

**请求参数：**
```
?page=1&pageSize=10
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "user_address": "0x456...",         // 用户地址
        "total_burn_amount": "500.0",       // 总销毁量
        "status": 1                         // 状态: 1已入账 2待结算
      }
    ]
  }
}
```

**操作的数据表：**
- `users`: 查询直接下级用户信息(pid字段关联)
- `burns`: 查询下级用户销毁数据
- `options`: 查询邀请配置信息

**字段详细说明：**

| 字段名 | 数据来源 | 计算方式 |
|-------|---------|---------|
| `user_address` | `users.user_address` | 直接获取下级用户地址 |
| `total_burn_amount` | `burns.actual_amount` | 计算下级用户的总销毁量 |
| `status` | 基于配置和销毁记录计算 | 根据有效直推用户数量判断状态 |

**状态判断逻辑：**
1. 从`options`表获取`effective_direct_user_count`配置值
2. 查询所有直推下级用户ID列表(`users`表中`pid`等于当前用户ID)
3. 查询这些用户中有成功销毁记录的用户(`burns`表中`tx_status`为成功)
4. 根据配置的`effective_direct_user_count`值对销毁用户分组
5. 判断当前用户是否在满足条件的分组中:
   - 如果在，状态为1(已入账)
   - 如果不在，状态为2(待结算)

**接口流程图：**

```mermaid
sequenceDiagram
    participant 客户端
    participant API控制器
    participant 用户模型
    participant 销毁模型
    participant 配置模型
    
    客户端->>API控制器: GET /api/user/direct_invites 携带JWT令牌
    API控制器->>用户模型: 获取认证用户信息
    用户模型-->>API控制器: 返回用户对象
    API控制器->>用户模型: 获取直接下级用户列表
    用户模型-->>API控制器: 返回下级用户列表
    API控制器->>配置模型: 获取邀请配置
    配置模型-->>API控制器: 返回邀请配置
    API控制器->>销毁模型: 获取下级销毁数据
    销毁模型-->>API控制器: 返回销毁数据
    API控制器->>API控制器: 计算每个下级的状态
    API控制器->>客户端: 返回直推邀请列表
```

**特殊处理：**
- 对特定用户(ID=2297)有日期过滤处理，不显示指定日期后的团队数据
- 2024-03-11改为下级销毁交易确认后马上发放奖励，不需要等待24小时生效

### 1.8 获取所有邀请列表(团队)

**接口信息：**
- **路径**: `/api/user/invites`
- **方法**: GET
- **前端调用**: `CompatAPI.getInvites()`
- **认证要求**: 需要认证

**请求参数：**
```
?page=1&pageSize=10
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "user_address": "0x789...",         // 用户地址
        "total_burn_amount": "300.0",       // 总销毁量
        "time": "2023-07-01 12:00:00"       // 邀请时间
      }
    ]
  }
}
```

**操作的数据表：**
- `users`: 查询团队用户信息
- `invites`: 查询团队邀请关系(包含直接和间接邀请)
- `burns`: 查询团队成员销毁数据

**字段详细说明：**

| 字段名 | 数据来源 | 计算方式 |
|-------|---------|---------|
| `user_address` | `users.user_address`通过`invites`关联 | 获取被邀请用户地址 |
| `total_burn_amount` | `burns.actual_amount` | 计算被邀请用户的总销毁量 |
| `time` | `invites.created_at` | 邀请关系创建时间 |

**接口流程图：**

```mermaid
sequenceDiagram
    participant 客户端
    participant API控制器
    participant 邀请模型
    participant 销毁模型
    
    客户端->>API控制器: GET /api/user/invites 携带JWT令牌
    API控制器->>API控制器: 获取认证用户信息
    API控制器->>邀请模型: 获取团队邀请关系(getInvites)
    邀请模型-->>API控制器: 返回邀请关系列表
    
    loop 对每个邀请关系
        API控制器->>销毁模型: 获取被邀请用户销毁总量
        销毁模型-->>API控制器: 返回销毁总量
    end
    
    API控制器->>客户端: 返回团队邀请列表
```

**实现细节：**
1. 通过`Invite::getInvites`方法获取当前用户的所有邀请关系，包含分页处理
2. 该方法会预加载被邀请用户信息(`invitee`关系)
3. 对每个被邀请用户，调用`Burn::getTotalAamount`计算其总销毁量
4. 特定用户(ID=2297)有日期过滤处理，不显示指定日期后的团队数据
5. 返回结果包含用户地址、销毁总量和邀请时间

**与直推邀请列表的区别：**
- 直推邀请列表只显示直接下级(`users.pid`等于当前用户ID)
- 团队邀请列表显示所有层级的下级(通过`invites`表关联)
- 直推邀请列表包含状态字段，团队邀请列表包含时间字段

## 首页模块

### 2.1 获取首页数据

**接口信息：**
- **路径**: `/api/index_data`
- **方法**: GET
- **前端调用**: `IndexAPI.getData()`
- **认证要求**: 无需认证

**请求参数：**
```
?language=zh
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "minBurn": "100.0",                      // 最低销毁数量
    "personalProfitRank": [                  // 个人收益排行
      {
        "address": "0x123...",
        "amount": "1000.0",
        "rank": 1
      }
    ],
    "banners": [                             // 轮播图
      {
        "picture": "/images/banner1.jpg",
        "link": "https://example.com/promo"
      }
    ],
    "notices": [                             // 公告列表
      {
        "title": "系统更新公告",
        "content": "系统将于xx日进行升级维护",
        "createdAt": 1628793456
      }
    ],
    "noticeModal": {                         // 弹窗公告
      "title": "重要通知",
      "content": "新功能上线，立即体验"
    }
  }
}
```

**操作的数据表：**
- `options`: 获取最低销毁数量配置
- `mint_profits`: 获取个人收益排行榜数据
- `banners`: 获取轮播图数据
  - 字段: id, title, picture, link, sort, status, language, created_at, updated_at
- `notices`: 获取公告列表数据
  - 字段: id, title, content, language, is_flagged, status, created_at, updated_at
- `notice_modal`: 获取公告弹窗数据
  - 字段: id, title, content, language, status, created_at, updated_at

## 销毁模块

销毁模块是系统的核心功能，用户通过销毁代币获得理财基数，进而产生每日收益。整个销毁流程包括提交销毁记录、链上交易确认、计算收益等多个环节，涉及复杂的业务逻辑和数据处理。

### 销毁业务流程概述

```mermaid
flowchart TD
    A[用户销毁代币] -->|提交交易哈希| B[创建销毁订单]
    B --> C{链上交易验证}
    C -->|验证失败| D[标记订单失败]
    C -->|验证成功| E[销毁确认]
    E --> F[创建铸造基数]
    E --> G[更新用户等级]
    E --> H[计算直推奖励]
    F --> I[等待24小时]
    I --> J[入账到理财账户]
    J --> K[每日定时计算收益]
    K --> L[个人收益]
    K --> M[团队收益]
    K --> N[社区长奖励]
```

### 3.1 提交销毁记录

**接口信息：**
- **路径**: `/api/burn/submit` 或 `/api/burn/create`(兼容旧版)
- **方法**: POST
- **前端调用**: `BurnAPI.submit(burnData)`
- **认证要求**: 需要认证

**请求参数：**
```json
{
  "amount": "100.0",                // 销毁金额
  "txhash": "0x123...",             // 交易哈希
  "address": "0x456...",            // 用户地址
  "nonce": "1628793456",            // 操作nonce
  "message": "Burn Confirm",        // 签名消息
  "sign": "0x789..."                // 签名
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": "销毁订单创建成功"
}
```

**操作的数据表：**
- `burns`: 创建销毁订单记录
  - 字段: id, user_id, order_no, amount, actual_amount, chain, txhash, tx_status, status, effective_time, created_at, updated_at
- `blockchain_txs`: 记录区块链交易信息
- `wallets`: 更新销毁相关金额
- `nonces`: 记录和验证nonce

**接口流程图：**

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as 服务端API
    participant Cache as 缓存服务
    participant DB as 数据库
    
    Client->>API: 发送销毁请求 /api/burn/submit
    API->>API: 验证请求参数和Token
    
    API->>Cache: 检查交易哈希缓存锁
    alt 缓存存在
        Cache-->>API: 返回已存在
        API-->>Client: 返回错误:交易已提交(1001)
    else 缓存不存在
        API->>DB: 验证交易哈希是否已存在
        DB-->>API: 返回验证结果
        
        alt 交易已存在
            API->>Cache: 设置交易哈希缓存(1小时)
            API-->>Client: 返回错误:交易已提交(1001)
        else 交易不存在
            API->>DB: 创建销毁订单(burns表)
            API->>Cache: 设置交易哈希缓存(1小时)
            API-->>Client: 返回销毁成功信息
        end
    end
```

**特殊处理逻辑：**
1. **并发控制**：使用Redis锁防止同一用户并发提交多个销毁请求
2. **交易哈希验证**：严格验证交易哈希格式(64位十六进制)和唯一性
3. **兑换额度检查**：如果用户配置了`burn_need_exchange_rate`，需要检查是否有足够的兑换额度
4. **版本标记**：记录销毁订单的版本号，用于区分不同处理逻辑

### 3.2 获取销毁记录

**接口信息：**
- **路径**: `/api/burn/records` 或 `/api/burn/history`(兼容旧版)
- **方法**: GET
- **前端调用**: `BurnAPI.getRecords(params)` 或 `CompatAPI.getBurnHistory()`
- **认证要求**: 需要认证

**请求参数：**
```
?address=0x123...&date=2023-07-01&page=1&size=20
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 123,
        "amount": "100.0",                  // 销毁金额
        "actual_amount": "100.0",           // 实际销毁金额
        "tx_hash": "0x123...",              // 交易哈希
        "status": 1,                        // 状态: 0待处理 1已确认 2失败
        "tx_status": 1,                     // 交易状态: 0待确认 1已确认 2失败
        "created_at": "2023-07-01 12:00:00" // 创建时间
      }
    ],
    "totalCount": 50,                       // 总记录数
    "totalPages": 3                         // 总页数
  }
}
```

**操作的数据表：**
- `burns`: 查询销毁订单记录
- `users`: 关联查询用户信息 

### 3.3 获取销毁收益记录

**接口信息：**
- **路径**: `/api/burn/profits`
- **方法**: GET
- **前端调用**: `CompatAPI.getBurnProfits()`
- **认证要求**: 需要认证

**请求参数：**
```
?address=0x123...&date=2023-07-01&page=1&pageSize=20
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "amount": "5.0",                    // 收益金额
        "rate": "5%",                       // 收益率
        "source_amount": "100.0",           // 来源金额(理财本金)
        "created_at": "2023-07-01 12:00:00" // 创建时间
      }
    ],
    "totalAmount": "500.0",                 // 总收益金额
    "totalCount": 100,                      // 总记录数
    "totalPages": 5                         // 总页数
  }
}
```

**操作的数据表：**
- `mint_profit`: 查询铸造收益记录
- `wallets`: 查询总收益数据
- `users`: 关联查询用户信息

### 3.4 获取团队收益记录

**接口信息：**
- **路径**: `/api/burn/team_profits`
- **方法**: GET
- **前端调用**: `CompatAPI.getTeamProfits()`
- **认证要求**: 需要认证

**请求参数：**
```
?address=0x123...&date=2023-07-01&page=1&size=20
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "amount": "2.5",                    // 团队收益金额
        "rate": "5%",                       // 收益率
        "team_member": "0x456...",          // 团队成员地址
        "team_level": 2,                    // 团队成员层级
        "source_amount": "50.0",            // 来源金额
        "created_at": "2023-07-01 12:00:00" // 创建时间
      }
    ],
    "totalAmount": "250.0",                 // 总团队收益金额
    "totalCount": 50,                       // 总记录数
    "totalPages": 3                         // 总页数
  }
}
```

**操作的数据表：**
- `mint_team_profit`: 查询团队收益记录
- `wallets`: 查询总收益数据
- `users`: 关联查询用户和团队成员信息

### 销毁后台处理流程

**1. 链上交易确认检查 (BurnOrderCheck.php)**
```mermaid
flowchart TD
    A[定时任务] -->|每45秒检查| B[获取待确认订单]
    B --> C{验证链上交易}
    C -->|失败| D[标记订单失败]
    C -->|成功| E[检查交易细节]
    E -->|验证通过| F[更新订单状态]
    F --> G[创建铸造基数记录]
    G --> H[计算直推奖励]
    H --> I[更新用户等级]
    I --> J[等待24小时]
    J --> K[入账到理财账户]
```

**2. 销毁收益计算 (BurnProfit.php)**
```mermaid
flowchart TD
    A[定时任务] -->|每分钟检查| B[获取需要计算收益的用户]
    B --> C[计算个人收益]
    C --> D{是否出局}
    D -->|是| E[标记出局]
    D -->|否| F[计算实际收益]
    F --> G[更新钱包余额]
    G --> H{是否有上级}
    H -->|是| I[计算团队收益]
    H -->|否| J[结束]
    I --> K{上级是否是社区长}
    K -->|是| L[计算社区长奖励]
    K -->|否| J[结束]
```

**3. 销毁基数拆分逻辑**

当用户销毁代币时，系统会根据不同的销毁金额区间计算不同的收益率，这涉及到复杂的拆单逻辑：

1. 获取用户已有的基数总量
2. 根据新销毁金额和已有基数，确定适用的收益率区间
3. 如果跨越多个收益率区间，需要将销毁金额拆分为多个基数记录
4. 每个基数记录包含：金额、收益率、最大收益倍数、最大收益金额

**收益率区间示例：**
```
区间1: 50-1000, 收益率0.8%, 最大收益倍数2
区间2: 1001-5000, 收益率1%, 最大收益倍数2
区间3: 5001+, 收益率1.2%, 最大收益倍数2
```

**4. 出局机制**

每个销毁基数都有最大收益上限，一般为销毁金额的2倍。当累计收益达到上限时，该基数将被标记为"出局"，不再产生收益。

**特殊处理：**
- 对特定用户(如ID=3537)有特殊的收益计算逻辑
- 签到奖励产生的理财金不参与团队收益计算
- 用户ID=2297有特殊的收益限制

## 兑换模块

兑换模块是系统的重要功能之一，允许用户在不同代币之间进行兑换，例如USDT和LCT之间的相互转换。该模块支持多种兑换类型，并提供完整的订单管理和交易确认流程。

### 兑换业务流程概述

```mermaid
flowchart TD
    A[用户选择兑换币种和金额] -->|提交兑换请求| B[创建兑换订单]
    B --> C[生成收款地址]
    C --> D[用户支付]
    D -->|提交交易哈希| E[更新订单状态]
    E --> F{交易验证}
    F -->|验证失败| G[标记订单失败]
    F -->|验证成功| H[完成兑换]
    H --> I[更新用户钱包余额]
```

### 4.1 获取兑换基础数据

**接口信息：**
- **路径**: `/api/exchange/basic`
- **方法**: GET
- **前端调用**: `ExchangeAPI.getBasic()`
- **认证要求**: 无需认证

**请求参数：** 无

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "supportCoins": [                       // 支持的币种
      {
        "code": "USDT",
        "name": "USDT",
        "icon": "/images/usdt.png",
        "chain": "BSC",
        "contractAddress": "0x123..."
      }
    ],
    "exchangePairs": [                      // 兑换对
      {
        "fromCoin": "USDT",
        "toCoin": "LCT",
        "rate": "10",                       // 兑换比率
        "minAmount": "10",                  // 最小兑换金额
        "maxAmount": "10000",               // 最大兑换金额
        "fee": "2%"                         // 手续费
      }
    ],
    "receiveAddresses": [                   // 收款地址
      {
        "coin": "USDT",
        "chain": "BSC",
        "address": "0x789..."
      }
    ]
  }
}
```

**操作的数据表：**
- `options`: 查询兑换配置参数和汇率
- `prices`: 查询当前币价

**字段详细说明：**
- `supportCoins`: 系统支持的可兑换币种列表，包含代码、名称、图标、所属链和合约地址
- `exchangePairs`: 可用的兑换对，包含兑换比率、最小/最大兑换金额和手续费
- `receiveAddresses`: 系统收款地址列表，用于接收用户的存款

### 4.2 创建兑换订单

**接口信息：**
- **路径**: `/api/exchange/order`
- **方法**: POST
- **前端调用**: `ExchangeAPI.createOrder(orderData)`
- **认证要求**: 需要认证

**请求参数：**
```json
{
  "depositCoinType": "USDT",              // 存入币种
  "depositAmount": "100.0",               // 存入金额
  "receiveCoinType": "LCT",               // 接收币种
  "receiveAmount": "1000.0",              // 接收金额
  "chain": "BSC"                          // 链
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "order_id": "EX2023070112345",         // 订单ID
    "pay_address": "0x789...",             // 支付地址
    "pay_amount": "100.0",                 // 支付金额
    "pay_coin": "USDT",                    // 支付币种
    "contract": "0x123...",                // 合约地址
    "decimals": 18                         // 代币精度
  }
}
```

**操作的数据表：**
- `exchanges`: 创建兑换订单记录
  - 字段: id, user_id, order_id, address, deposit_amount, deposit_coin_type, receive_amount, receive_coin_type, receive_address, status, action_type, exchange_type, txhash, created_at, updated_at
- `swft_orders`: 如果使用第三方兑换服务，记录第三方订单信息
- `options`: 查询汇率和配置信息

**特殊处理逻辑：**
1. **兑换类型**：系统支持多种兑换类型
   - `LCT2U`: LCT兑换USDT
   - `U2LCT`: USDT兑换LCT
   - 其他第三方支持的币种兑换

2. **LCT兑USDT特殊处理**：
   ```php
   if ($deposit_coin_type == WithdrawalConst::CoinTypeLct) {
       // 重新计算数量
       $receive_amount = $this->commonService->bcmul($deposit_amount, $price['lct_usdt'], 6);
       // 设置兑换类型为LCT2U
       $exchange->exchange_type = Exchange::ExchangeTypeLCT2U;
   }
   ```

3. **汇率计算**：根据当前配置的币价计算兑换金额，确保用户获得公平的兑换比例

### 4.3 提交支付交易hash

**接口信息：**
- **路径**: `/api/exchange/pay`
- **方法**: POST
- **前端调用**: `ExchangeAPI.submitPay(payData)`
- **认证要求**: 需要认证

**请求参数：**
```json
{
  "address": "0x123...",                   // 用户地址
  "order_id": "EX2023070112345",           // 订单ID
  "txhash": "0x123..."                     // 交易哈希
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": true
}
```

**操作的数据表：**
- `exchanges`: 更新订单支付状态和交易哈希
  - 更新字段: txhash, status, updated_at

**交易验证流程：**
1. 验证交易哈希格式(64位十六进制)
2. 检查订单状态是否为待支付
3. 验证用户地址与订单地址一致
4. 检查交易哈希是否已被使用
5. 更新订单状态为处理中

### 4.4 获取兑换记录

**接口信息：**
- **路径**: `/api/exchange/records` 或 `/api/exchange/history`(兼容旧版)
- **方法**: GET
- **前端调用**: `ExchangeAPI.getRecords(params)` 或 `CompatAPI.getExchangeHistory()`
- **认证要求**: 需要认证

**请求参数：**
```
?page=1&pageSize=20&startDate=2023-07-01&endDate=2023-07-31
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "orderId": "EX2023070112345",           // 订单ID
        "depositCoin": "USDT",                  // 存入币种
        "depositAmount": "100.0",               // 存入金额
        "receiveCoin": "LCT",                   // 接收币种
        "receiveAmount": "1000.0",              // 接收金额
        "rate": "10",                           // 兑换比率
        "status": 2,                            // 状态: 0待支付 1处理中 2已完成 3已取消
        "txhash": "0x123...",                   // 交易哈希
        "createdAt": "2023-07-01 12:00:00",     // 创建时间
        "completedAt": "2023-07-01 12:10:00"    // 完成时间
      }
    ],
    "totalCount": 50,                           // 总记录数
    "totalPages": 3                             // 总页数
  }
}
```

**操作的数据表：**
- `exchanges`: 查询兑换订单记录
- `users`: 关联查询用户信息

### 兑换后台处理流程

**1. 交易确认检查**
```mermaid
flowchart TD
    A[定时任务] -->|每30秒检查| B[获取处理中订单]
    B --> C{验证链上交易}
    C -->|失败| D[标记订单失败]
    C -->|成功| E[检查交易细节]
    E -->|验证通过| F[更新订单状态]
    F --> G[更新用户钱包余额]
    G --> H[记录交易日志]
```

**2. 兑换类型和处理逻辑**

系统支持多种兑换类型，每种类型有不同的处理逻辑：

1. **USDT兑换LCT**：
   - 验证USDT转账交易
   - 将LCT添加到用户钱包
   - 更新用户可销毁额度

2. **LCT兑换USDT**：
   - 验证LCT转账交易
   - 创建提现记录
   - 等待管理员审核

3. **第三方兑换服务**：
   - 调用SWFT等第三方API
   - 监控第三方订单状态
   - 完成后更新本地订单

## 钱包模块

钱包模块管理用户的资产，包括余额查询、资金转移和提现等功能。钱包模块是连接用户资产和系统功能的关键组件。

### 钱包业务流程概述

```mermaid
flowchart TD
    A[用户操作] --> B{操作类型}
    B -->|提现| C[提交提现申请]
    B -->|查询余额| D[获取钱包信息]
    B -->|查询历史| E[获取交易记录]
    
    C --> F[验证签名和参数]
    F --> G[计算手续费]
    G --> H[冻结资金]
    H --> I[创建提现记录]
    I --> J[等待管理员审核]
    J -->|审核通过| K[链上转账]
    J -->|审核拒绝| L[解冻资金]
```

### 5.1 申请提现

**接口信息：**
- **路径**: `/api/wallet/withdrawal`
- **方法**: POST
- **前端调用**: `CompatAPI.walletWithdrawal(data)`
- **认证要求**: 需要认证

**请求参数：**
```json
{
  "address": "0x123...",                   // 用户地址
  "amount": 100.0,                          // 提现金额
  "receive_address": "0x456...",            // 接收地址
  "type": 1,                                // 提现类型: 1=USDT, 2=TRX, 3=ETH
  "sign": "0x456...",                       // 签名
  "message": "Withdrawal 100.0. Nonce:1628793456", // 签名消息
  "nonce": "1628793456"                     // nonce
}
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123,                             // 提现记录ID
    "order_no": "WD2023070112345",         // 订单号
    "amount": "100.0",                     // 提现金额
    "fee": "2.0",                          // 手续费
    "actual_amount": "98.0",               // 实际到账金额
    "status": 0                            // 状态: 0待审核
  }
}
```

**操作的数据表：**
- `withdrawals`: 创建提现申请记录
  - 字段: id, user_id, order_no, amount, fee, actual_amount, receive_address, coin_type, status, txhash, created_at, updated_at
- `wallets`: 冻结提现金额(available减少,frozen增加)
- `nonces`: 记录和验证nonce
- `options`: 查询提现配置(手续费、最低金额等)

**提现处理流程：**
1. **参数验证**：验证提现金额、地址格式、签名等
2. **用户状态检查**：检查用户是否被禁止提现
3. **提现配置获取**：获取当前提现类型的配置(最低金额、手续费等)
4. **手续费计算**：根据配置计算手续费和实际到账金额
5. **余额检查**：验证用户可用余额是否足够
6. **资金冻结**：从可用余额中扣除并添加到冻结余额
7. **创建提现记录**：记录提现详情，状态为待审核
8. **管理员通知**：发送Telegram通知给管理员

### 5.2 获取提现历史

**接口信息：**
- **路径**: `/api/wallet/withdrawal/history`
- **方法**: GET
- **前端调用**: `CompatAPI.getWithdrawalHistory()`
- **认证要求**: 需要认证

**请求参数：**
```
?date=2023-07-01&page=1&size=20
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 123,                            // 提现记录ID
        "order_no": "WD2023070112345",        // 订单号
        "amount": "100.0",                    // 提现金额
        "fee": "2.0",                         // 手续费
        "actual_amount": "98.0",              // 实际到账金额
        "receive_address": "0x123...",        // 接收地址
        "coin_type": "USDT",                  // 币种类型
        "status": 1,                          // 状态: 0待审核 1已完成 2已拒绝
        "txhash": "0x456...",                 // 交易哈希(若已处理)
        "created_at": "2023-07-01 12:00:00",  // 创建时间
        "completed_at": "2023-07-01 14:00:00" // 完成时间
      }
    ],
    "totalCount": 50,                         // 总记录数
    "totalPages": 3                           // 总页数
  }
}
```

**操作的数据表：**
- `withdrawals`: 查询提现记录
- `users`: 关联查询用户信息

### 钱包安全机制

1. **签名验证**：所有提现操作都需要钱包签名验证
2. **Nonce防重放**：使用nonce防止重放攻击
3. **并发控制**：使用Redis锁防止并发提现请求
4. **可提现比例限制**：用户只能提现其可用余额的特定比例
5. **提现审核**：所有提现都需要管理员审核，防止恶意提现
6. **黑名单机制**：可以禁止特定用户提现

## 公告模块

### 6.1 获取公告列表

**接口信息：**
- **路径**: `/api/notices`
- **方法**: GET
- **前端调用**: `CompatAPI.getNotices()`
- **认证要求**: 无需认证

**请求参数：**
```
?language=zh&page=1&size=10
```

**响应数据：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 123,                           // 公告ID
        "title": "系统升级通知",              // 公告标题
        "content": "系统将于2023年7月1日升级维护...", // 公告内容
        "createdAt": "2023-06-25 12:00:00",  // 创建时间
        "isFlagged": true                    // 是否置顶
      }
    ],
    "totalCount": 50,                        // 总记录数
    "totalPages": 5                          // 总页数
  }
}
```

**操作的数据表：**
- `notices`: 查询公告记录
  - 字段: id, title, content, language, is_flagged, status, created_at, updated_at

## 数据库表结构

**数据库ER图：**

```mermaid
erDiagram
    USERS {
        int id PK
        varchar user_address
        varchar chain
        varchar invite_code
        int pid
        text pid_full_path
        int level
        tinyint state
    }
    
    WALLETS {
        int id PK
        int user_id FK
        decimal available
        decimal finance
        decimal burn
        decimal checkin
        decimal profit
        decimal team_profit
    }
    
    BURN_ORDERS {
        int id PK
        int user_id FK
        varchar order_no
        decimal amount
        varchar tx_hash
        tinyint status
    }
    
    MINT_PROFITS {
        int id PK
        int user_id FK
        date date
        decimal amount
        decimal rate
    }
    
    TEAM_PROFITS {
        int id PK
        int user_id FK
        int team_user_id FK
        int team_level
        decimal amount
    }
    
    INVITES {
        int id PK
        int inviter_user_id FK
        int invitee_user_id FK
    }
    
    USERS ||--o{ WALLETS : "has"
    USERS ||--o{ BURN_ORDERS : "creates"
    USERS ||--o{ MINT_PROFITS : "earns"
    USERS ||--o{ TEAM_PROFITS : "earns"
    USERS ||--o{ INVITES : "invites"
```

### 用户相关表

#### 1. users - 用户表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_address | varchar(255) | 用户钱包地址 |
| chain | varchar(50) | 链名称(BSC, ETH, TRX) |
| invite_code | varchar(50) | 邀请码 |
| pid | int | 上级用户ID |
| pid_full_path | text | 完整上级路径 |
| level | int | 用户等级 |
| roles | varchar(50) | 用户角色 |
| state | tinyint | 状态(0正常 1禁用) |
| profit_time | time | 收益计算时间 |
| token_balance | decimal(30,18) | 代币余额 |
| allowance | decimal(30,18) | 合约授权额度 |
| allowance_updated_at | datetime | 授权更新时间 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 2. wallets - 钱包表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| available | decimal(30,18) | 可用余额 |
| frozen | decimal(30,18) | 冻结余额 |
| finance | decimal(30,18) | 理财数量(每日铸造理财基数) |
| burn | decimal(30,18) | 销毁数量(参与理财) |
| checkin | decimal(30,18) | 签到获得(参与理财) |
| profit | decimal(30,18) | 理财收益(每日理财获得) |
| team_profit | decimal(30,18) | 团队收益(下属每日理财获得) |
| direct_invite | decimal(30,18) | 直推奖励 |
| experience | decimal(30,18) | 体验金 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 3. invites - 邀请关系表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| inviter_user_id | int | 邀请人用户ID |
| invitee_user_id | int | 被邀请人用户ID |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 销毁相关表

#### 4. burn_orders - 销毁订单表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| order_no | varchar(100) | 订单号 |
| amount | decimal(30,18) | 销毁金额 |
| actual_amount | decimal(30,18) | 实际销毁金额 |
| chain | varchar(50) | 链名称 |
| tx_hash | varchar(255) | 交易哈希 |
| tx_status | tinyint | 交易状态(0待确认 1已确认 2失败) |
| status | tinyint | 订单状态(0待处理 1已确认 2失败) |
| ip | varchar(50) | 操作IP |
| effective_time | int | 生效时间戳 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 5. mint_profits - 铸造收益表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| date | date | 收益日期 |
| amount | decimal(30,18) | 收益金额 |
| source_amount | decimal(30,18) | 来源金额 |
| rate | decimal(10,2) | 收益率 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 | 

#### 6. mint_base - 铸造基数表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| source | varchar(50) | 来源类型(burn:销毁, checkin:签到, gift:礼包, airdrop:空投) |
| source_id | int | 来源ID |
| amount | decimal(30,18) | 金额 |
| is_effective | tinyint | 是否有效(0无效 1有效) |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 7. team_profits - 团队收益表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID(收益人) |
| team_user_id | int | 团队成员用户ID |
| team_level | int | 团队成员层级 |
| date | date | 收益日期 |
| amount | decimal(30,18) | 收益金额 |
| source_amount | decimal(30,18) | 来源金额 |
| rate | decimal(10,2) | 收益率 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 8. direct_rewards - 直推奖励表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID(获得奖励的用户) |
| invitee_user_id | int | 被邀请用户ID |
| burn_order_id | int | 销毁订单ID |
| amount | decimal(30,18) | 奖励金额 |
| status | tinyint | 状态(1已入账 2待结算) |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 签到相关表

#### 9. checkin_logs - 签到日志表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| date | date | 签到日期 |
| streak | int | 连续签到天数 |
| ip | varchar(50) | 签到IP |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 10. checkin_rewards - 签到奖励表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| checkin_log_id | int | 签到日志ID |
| amount | decimal(30,18) | 奖励金额 |
| type | varchar(50) | 奖励类型(daily:每日, streak:连续, first:首次) |
| streak | int | 连续签到天数 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 交易相关表

#### 11. withdrawals - 提现表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| order_no | varchar(100) | 订单号 |
| amount | decimal(30,18) | 提现金额 |
| fee | decimal(30,18) | 手续费 |
| actual_amount | decimal(30,18) | 实际到账金额 |
| receive_address | varchar(255) | 接收地址 |
| coin_type | varchar(50) | 币种类型 |
| status | tinyint | 状态(0待审核 1已完成 2已拒绝) |
| tx_hash | varchar(255) | 交易哈希 |
| reject_reason | varchar(255) | 拒绝原因 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |
| completed_at | datetime | 完成时间 |

#### 12. exchange_orders - 兑换订单表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| order_no | varchar(100) | 订单号 |
| deposit_coin | varchar(50) | 存入币种 |
| deposit_amount | decimal(30,18) | 存入金额 |
| receive_coin | varchar(50) | 接收币种 |
| receive_amount | decimal(30,18) | 接收金额 |
| exchange_rate | decimal(30,18) | 兑换汇率 |
| chain | varchar(50) | 链 |
| tx_hash | varchar(255) | 交易哈希 |
| status | tinyint | 状态(0待支付 1处理中 2已完成 3已取消) |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |
| completed_at | datetime | 完成时间 |

#### 13. blockchain_txs - 区块链交易表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| user_id | int | 用户ID |
| tx_hash | varchar(255) | 交易哈希 |
| tx_type | varchar(50) | 交易类型(burn, withdraw, exchange, allowance) |
| source_id | int | 来源ID |
| status | tinyint | 状态(0待确认 1已确认 2失败) |
| retry_count | int | 重试次数 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

### 系统相关表

#### 14. options - 系统配置表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| key | varchar(255) | 配置键名 |
| value | text | 配置值 |
| description | varchar(255) | 描述 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 15. banners - 轮播图表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| title | varchar(255) | 标题 |
| picture | varchar(255) | 图片路径 |
| link | varchar(255) | 链接 |
| sort | int | 排序 |
| status | tinyint | 状态(0隐藏 1显示) |
| language | varchar(50) | 语言 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

#### 16. notices - 公告表
| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | int | 主键 |
| title | varchar(255) | 标题 |
| content | text | 内容 |
| language | varchar(50) | 语言 |
| is_flagged | tinyint | 是否置顶(0否 1是) |
| status | tinyint | 状态(0隐藏 1显示) |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

## 前端页面与API对应关系

**页面与API关系图：**

```mermaid
flowchart TD
    subgraph "前端页面与API关系"
        A["登录页面"] --> A1["/api/user/login"]
        
        B["首页"] --> B1["/api/index_data"]
        B --> B2["/api/user/info"]
        B --> B3["/api/notices"]
        
        C["我的页面"] --> C1["/api/user/info"]
        C --> C2["/api/user/gift"]
        C --> C3["/api/user/claimGift"]
        C --> C4["/api/user/invitation"]
        
        D["销毁页面"] --> D1["/api/user/info"]
        D --> D2["/api/user/ua"]
        D --> D3["/api/burn/submit"]
        D --> D4["/api/burn/records"]
        
        E["理财收益"] --> E1["/api/user/info"]
        E --> E2["/api/burn/profits"]
        E --> E3["/api/burn/team_profits"]
        
        F["兑换页面"] --> F1["/api/exchange/basic"]
        F --> F2["/api/exchange/order"]
        F --> F3["/api/exchange/pay"]
        F --> F4["/api/exchange/records"]
        
        G["提现页面"] --> G1["/api/user/info"]
        G --> G2["/api/wallet/withdrawal"]
        G --> G3["/api/wallet/withdrawal/history"]
        
        H["邀请页面"] --> H1["/api/user/invitation"]
        H --> H2["/api/user/direct_invites"]
        H --> H3["/api/user/invites"]
        
        I["公告页面"] --> I1["/api/notices"]
        I --> I2["/api/notices/{id}"]
    end
```

以下是前端各页面及其使用的API列表:

### 1. 登录页面 (login.html)

**接口调用：**
- `/api/user/login` - 用户登录/注册

### 2. 首页 (index.html)

**接口调用：**
- `/api/index_data` - 获取首页数据(轮播图、公告、排行榜等)
- `/api/user/info` - 获取用户信息(如已登录)
- `/api/notices` - 获取公告列表

### 3. 我的页面 (me.html)

**接口调用：**
- `/api/user/info` - 获取用户详情
- `/api/user/gift` - 获取礼包状态
- `/api/user/claimGift` - 领取礼包
- `/api/user/invitation` - 获取邀请信息

### 4. 销毁页面 (burn.html)

**接口调用：**
- `/api/user/info` - 获取用户信息
- `/api/user/ua` - 更新授权信息
- `/api/burn/submit` - 提交销毁记录
- `/api/burn/records` - 获取销毁记录

### 5. 理财收益页面 (profit.html)

**接口调用：**
- `/api/user/info` - 获取用户信息
- `/api/burn/profits` - 获取理财收益记录
- `/api/burn/team_profits` - 获取团队收益记录

### 6. 兑换页面 (exchange.html)

**接口调用：**
- `/api/exchange/basic` - 获取兑换基础数据
- `/api/exchange/order` - 创建兑换订单
- `/api/exchange/pay` - 提交支付交易
- `/api/exchange/records` - 获取兑换记录

### 7. 提现页面 (withdraw.html)

**接口调用：**
- `/api/user/info` - 获取用户信息
- `/api/wallet/withdrawal` - 申请提现
- `/api/wallet/withdrawal/history` - 获取提现历史

### 8. 邀请页面 (invite.html)

**接口调用：**
- `/api/user/invitation` - 获取邀请信息
- `/api/user/direct_invites` - 获取直推邀请列表
- `/api/user/invites` - 获取所有邀请列表(团队)

### 9. 公告页面 (notice.html)

**接口调用：**
- `/api/notices` - 获取公告列表
- `/api/notices/{id}` - 获取公告详情 