package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 公告实体类 - 兼容旧数据库结构
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("notices")
public class Notice {

    /**
     * 公告ID (主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 排序ID
     */
    @TableField("sort_id")
    private Integer sortId;

    /**
     * 语言标识 (zh:中文, en:英文等)
     */
    @TableField("language")
    private String language;

    /**
     * 公告标题
     */
    @TableField("title")
    private String title;

    /**
     * 公告简介
     */
    @TableField("intro")
    private String intro;

    /**
     * 公告内容
     */
    @TableField("content")
    private String content;

    /**
     * 封面图
     */
    @TableField("picture")
    private String picture;

    /**
     * 外链
     */
    @TableField("link")
    private String link;

    /**
     * 是否显示 (1:显示, 2:隐藏)
     */
    @TableField("`show`")
    private Integer show;

    /**
     * 推荐标志 (1:推荐, 2:普通)
     */
    @TableField("flag")
    private Integer flag;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;


} 