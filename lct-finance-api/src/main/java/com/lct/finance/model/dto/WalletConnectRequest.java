package com.lct.finance.model.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 钱包连接请求DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
public class WalletConnectRequest {

    /**
     * 区块链网络 (BSC/TRON)
     */
    @NotBlank(message = "区块链网络不能为空")
    private String chain;

    /**
     * 用户钱包地址
     */
    @NotBlank(message = "钱包地址不能为空")
    private String address;

    /**
     * 签名
     */
    private String signature;

    /**
     * 签名消息
     */
    private String message;

    /**
     * 时间戳随机数
     */
    private Long nonce;
} 