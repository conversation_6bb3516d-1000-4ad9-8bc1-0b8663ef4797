package com.lct.finance.model.dto;

import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 钱包余额响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
public class WalletBalanceResponse {

    /**
     * 用户钱包地址
     */
    private String userAddress;

    /**
     * USDT余额
     */
    private BigDecimal usdtBalance;

    /**
     * LCT余额
     */
    private BigDecimal lctBalance;

    /**
     * 冻结的USDT余额
     */
    private BigDecimal frozenUsdt;

    /**
     * 冻结的LCT余额
     */
    private BigDecimal frozenLct;

    /**
     * 可用USDT余额
     */
    private BigDecimal availableUsdt;

    /**
     * 可用LCT余额
     */
    private BigDecimal availableLct;

    /**
     * 总收益 (USDT)
     */
    private BigDecimal profit;

    /**
     * 团队收益 (USDT)
     */
    private BigDecimal teamProfit;

    /**
     * 今日收益 (USDT)
     */
    private BigDecimal todayProfit;

    /**
     * 累计铸造金额 (USDT)
     */
    private BigDecimal totalMint;

    /**
     * 累计销毁金额 (LCT)
     */
    private BigDecimal totalBurn;

    /**
     * 累计兑换金额 (USDT)
     */
    private BigDecimal totalExchange;

    /**
     * 累计提现金额 (USDT)
     */
    private BigDecimal totalWithdraw;

    /**
     * 首选区块链网络 (BSC/TRON)
     */
    private String preferredChain;

    /**
     * 钱包状态 (1:正常, 0:冻结)
     */
    private Integer status;
} 