package com.lct.finance.model.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * SWFT账户兑换请求
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
public class SwftAccountExchangeRequest {

    /**
     * 转入币种代码
     */
    private String depositCoinCode;

    /**
     * 接收币种代码
     */
    private String receiveCoinCode;

    /**
     * 转入数量
     */
    private BigDecimal depositCoinAmt;

    /**
     * 期待接收数量
     */
    private BigDecimal receiveCoinAmt;

    /**
     * 接收目标地址
     */
    private String destinationAddr;

    /**
     * 退款地址
     */
    private String refundAddr;

    /**
     * 设备唯一编号
     */
    private String equipmentNo;

    /**
     * 来源类型 (ANDROID, IOS, H5)
     */
    private String sourceType;

    /**
     * 来源标识
     */
    private String sourceFlag;

    /**
     * 是否开启免gas
     */
    private Boolean isNoGas;
}