package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 每日铸造收益基数数据表
 * <AUTHOR> Finance Team
 * @since 2024-12-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lct_mint_base")
public class MintBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 关联ID (根据source来区分不同表)
     */
    @TableField("relation_id")
    private Long relationId;

    /**
     * 来源 1:销毁,2:签到,3:体验金
     */
    @TableField("source")
    private Integer source;

    /**
     * 版本(销毁订单区分)
     */
    @TableField("version")
    private Integer version;

    /**
     * 数量
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 收益率
     */
    @TableField("profit_rate")
    private BigDecimal profitRate;

    /**
     * 收益最大倍数
     */
    @TableField("profit_max_multiple")
    private BigDecimal profitMaxMultiple;

    /**
     * 最多可得收益(出局值)
     */
    @TableField("profit_max_amount")
    private BigDecimal profitMaxAmount;

    /**
     * 已得收益
     */
    @TableField("profit_amount")
    private BigDecimal profitAmount;

    /**
     * 出局 1:是,2:否
     */
    @TableField("is_out")
    private Integer isOut;

    /**
     * 有效(等待有效期结束) 1:是,2:否
     */
    @TableField("is_effective")
    private Integer isEffective;

    /**
     * 批次号
     */
    @TableField("flow_id")
    private String flowId;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;



} 