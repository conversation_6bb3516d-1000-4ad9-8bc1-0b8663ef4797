package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 风险评估请求
 * 封装风险评估所需的参数
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskAssessmentRequest {
    /**
     * 操作类型（LOGIN, WITHDRAWAL, EXCHANGE等）
     */
    private String operation;
    
    /**
     * 用户地址
     */
    private String address;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 操作金额（可选，用于提现和兑换等操作）
     */
    private String amount;
    
    /**
     * 设备指纹（可选）
     */
    private String deviceFingerprint;
    
    /**
     * 附加信息（可选）
     * 用于传递不常用的额外信息
     */
    private Map<String, String> extraInfo;
    
    // Getter methods
    public String getOperation() {
        return operation;
    }
    
    public String getAddress() {
        return address;
    }
    
    public String getClientIp() {
        return clientIp;
    }
    
    public String getAmount() {
        return amount;
    }
    
    public String getDeviceFingerprint() {
        return deviceFingerprint;
    }
    
    public Map<String, String> getExtraInfo() {
        return extraInfo;
    }
    
    // Setter methods
    public void setOperation(String operation) {
        this.operation = operation;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
    
    public void setAmount(String amount) {
        this.amount = amount;
    }
    
    public void setDeviceFingerprint(String deviceFingerprint) {
        this.deviceFingerprint = deviceFingerprint;
    }
    
    public void setExtraInfo(Map<String, String> extraInfo) {
        this.extraInfo = extraInfo;
    }
} 