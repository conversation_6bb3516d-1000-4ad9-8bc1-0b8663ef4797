package com.lct.finance.model.constants;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 通用常量类 - 与PHP版本CommonConst.php保持完全一致
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public class CommonConstants {

    /**
     * 支持的多链列表
     */
    public static final List<String> SUPPORT_CHAINS = Arrays.asList(
            "BSC",
            "TRX");

    /**
     * 支持的多语言列表
     */
    public static final List<String> LANGUAGES = Arrays.asList(
            "zh_CN",
            "en");

    /**
     * 语言描述映射
     */
    public static final Map<String, String> LANGUAGES_DESC = Map.of(
            "zh_CN", "中文简体(zh_CN)",
            "en", "英语(en)");

    /**
     * SWFT支持的币种代码
     */
    public static final String COIN_CODE_LCT = "LCT(BSC)";
    public static final String COIN_CODE_USDT_BSC = "USDT(BSC)";
    public static final String COIN_CODE_USDT_TRON = "USDT(TRON)";

    // 常量定义
    public static final int STATUS_SUCCESS = 1;
    public static final int STATUS_REVIEWING = 2;
    public static final int STATUS_TRANSFERRING = 3;
    public static final int STATUS_FAILED = 4;



    public static final int SOURCE_TYPE_NORMAL = 1;
    public static final int SOURCE_TYPE_EXCHANGE = 2;

    /**
     * 支持的币种代码列表
     */
    public static final List<String> SUPPORT_COIN_CODE = Arrays.asList(
            COIN_CODE_LCT,
            COIN_CODE_USDT_BSC,
            COIN_CODE_USDT_TRON);

    /**
     * 币种类型常量 - 与PHP版本映射一致
     */
    public static final class CoinType {
        public static final int LCT_BSC = 1; // LCT(BSC)
        public static final int USDT_BSC = 2; // USDT(BSC)
        public static final int USDT_TRON = 3; // USDT(TRON)
    }

    /**
     * 根据币种类型获取币种代码
     */
    public static String getCoinCode(Integer coinType) {
        switch (coinType) {
            case CoinType.LCT_BSC:
                return COIN_CODE_LCT;
            case CoinType.USDT_BSC:
                return COIN_CODE_USDT_BSC;
            case CoinType.USDT_TRON:
                return COIN_CODE_USDT_TRON;
            default:
                return "UNKNOWN";
        }
    }

    /**
     * 根据币种代码获取币种类型
     */
    public static Integer getCoinType(String coinCode) {
        switch (coinCode) {
            case COIN_CODE_LCT:
                return CoinType.LCT_BSC;
            case COIN_CODE_USDT_BSC:
                return CoinType.USDT_BSC;
            case COIN_CODE_USDT_TRON:
                return CoinType.USDT_TRON;
            default:
                return null;
        }
    }

    /**
     * 兑换状态常量
     */
    public static final class ExchangeStatus {
        public static final int SUCCESS = 1; // 成功
        public static final int PAYING = 2; // 待支付
        public static final int PROCESSING = 3; // 处理中(已支付)
        public static final int FAILED = 4; // 失败
        public static final int OUTING = 5; // 出款中
    }

    /**
     * 兑换操作类型常量
     */
    public static final class ExchangeActionType {
        public static final int NORMAL = 1; // 正常
        public static final int SYSTEM = 2; // 系统
    }

    /**
     * 兑换类型常量
     */
    public static final class ExchangeType {
        public static final int U2LCT = 1; // USDT兑LCT
        public static final int LCT2U = 2; // LCT兑USDT
    }

    /**
     * 提现状态常量
     */
    public static final class WithdrawalStatus {
        public static final int SUCCESS = 1; // 提现成功
        public static final int PENDING = 2; // 审核中
        public static final int PROCESSING = 3; // 转账中
        public static final int FAILED = 4; // 提现失败
    }

    /**
     * 提现来源类型常量
     */
    public static final class WithdrawalSourceType {
        public static final int NORMAL = 1; // 正常提现
        public static final int ADMIN = 2; // 管理员操作
    }

    /**
     * 获取状态描述
     */
    public static String getExchangeStatusDescription(Integer status) {
        if (status == null)
            return "未知状态";
        switch (status) {
            case ExchangeStatus.SUCCESS:
                return "成功";
            case ExchangeStatus.PAYING:
                return "待支付";
            case ExchangeStatus.PROCESSING:
                return "处理中";
            case ExchangeStatus.FAILED:
                return "失败";
            case ExchangeStatus.OUTING:
                return "出款中";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取兑换类型描述
     */
    public static String getExchangeTypeDescription(Integer exchangeType) {
        if (exchangeType == null)
            return "未知类型";
        switch (exchangeType) {
            case ExchangeType.U2LCT:
                return "USDT兑LCT";
            case ExchangeType.LCT2U:
                return "LCT兑USDT";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取操作类型描述
     */
    public static String getActionTypeDescription(Integer actionType) {
        if (actionType == null)
            return "未知操作";
        switch (actionType) {
            case ExchangeActionType.NORMAL:
                return "SWFT跨链桥";
            case ExchangeActionType.SYSTEM:
                return "系统内部";
            default:
                return "未知操作";
        }
    }

    /**
     * 获取提现状态描述
     */
    public static String getWithdrawalStatusDescription(Integer status) {
        if (status == null)
            return "未知状态";
        switch (status) {
            case WithdrawalStatus.SUCCESS:
                return "提现成功";
            case WithdrawalStatus.PENDING:
                return "审核中";
            case WithdrawalStatus.PROCESSING:
                return "转账中";
            case WithdrawalStatus.FAILED:
                return "提现失败";
            default:
                return "未知状态";
        }
    }
}