package com.lct.finance.model.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 提现请求DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawalRequest {

    /**
     * 用户钱包地址
     */
    @NotBlank(message = "钱包地址不能为空")
    @Size(max = 255, message = "钱包地址长度不能超过255个字符")
    private String address;

    /**
     * 提现数量
     */
    @NotNull(message = "提现数量不能为空")
    @Digits(integer = 18, fraction = 6, message = "提现数量格式不正确")
    private BigDecimal amount;

    /**
     * 接收地址
     */
    @NotBlank(message = "接收地址不能为空")
    @Size(max = 255, message = "接收地址长度不能超过255个字符")
    private String receiveAddress;

    /**
     * 币种类型：1-LCT(BSC), 2-USDT(BSC), 3-USDT(TRON)
     */
    @NotNull(message = "币种类型不能为空")
    @Min(value = 1, message = "币种类型必须为1或2")
    @Max(value = 2, message = "币种类型必须为1或2")
    private Integer type;

    /**
     * 数字签名
     */
    @NotBlank(message = "数字签名不能为空")
    @Size(max = 255, message = "数字签名长度不能超过255个字符")
    private String sign;

    /**
     * 签名消息
     */
    @NotBlank(message = "签名消息不能为空")
    @Size(max = 255, message = "签名消息长度不能超过255个字符")
    private String message;

    /**
     * 随机数（防重放攻击）
     */
    @NotBlank(message = "随机数不能为空")
    @Size(max = 255, message = "随机数长度不能超过255个字符")
    private String nonce;

    /**
     * 设备指纹
     * 用于设备识别和风险评估
     */
    private String deviceFingerprint;
}