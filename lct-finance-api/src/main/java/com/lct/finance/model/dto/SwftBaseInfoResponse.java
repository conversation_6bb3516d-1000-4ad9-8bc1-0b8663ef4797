package com.lct.finance.model.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * SWFT获取汇率基础信息响应
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
public class SwftBaseInfoResponse {

    /**
     * 即时汇率
     */
    private BigDecimal instantRate;

    /**
     * 存入币种代码
     */
    private String depositCoinCode;

    /**
     * 接收币种代码
     */
    private String receiveCoinCode;

    /**
     * 存入数量
     */
    private BigDecimal depositCoinAmt;

    /**
     * 接收数量
     */
    private BigDecimal receiveCoinAmt;

    /**
     * 最小存入数量
     */
    private BigDecimal minDepositCoinAmt;

    /**
     * 最大存入数量
     */
    private BigDecimal maxDepositCoinAmt;

    /**
     * 手续费率
     */
    private BigDecimal depositCoinFeeRate;

    /**
     * 链费用
     */
    private BigDecimal chainFee;
}