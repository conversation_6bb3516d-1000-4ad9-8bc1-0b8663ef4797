package com.lct.finance.model.constants;

public class BurnConstants {

    // 常量定义
    public static final int TYPE_WITH_LCT = 1; // 有LCT
    public static final int TYPE_NO_LCT = 2; // 无LCT

    public static final int TX_STATUS_SUCCESS = 1; // 交易成功
    public static final int TX_STATUS_CONFIRMING = 2; // 交易确认中
    public static final int TX_STATUS_FAILED = 4; // 交易失败

    public static final int STATUS_RECORDED = 1; // 已记入
    public static final int STATUS_WAITING_CONFIRM = 2; // 待处理(等待交易确认)
    public static final int STATUS_WAITING_ACCOUNTING = 3; // 待入账(交易已确认,待有效时间到了入账)
    public static final int STATUS_FAILED = 4; // 失败

}
