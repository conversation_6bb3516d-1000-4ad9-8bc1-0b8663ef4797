package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销毁记录实体类
 * 对应数据库表：burns
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("lct_burns")
public class Burn {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 类型：1有lct，2无lct
     */
    @TableField("type")
    private Integer type;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 提交数量(LCT数量)
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * USDT数量
     */
    @TableField("usdt_amount")
    private BigDecimal usdtAmount;

    /**
     * 实际数量(交易检查完成后)
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 实际USDT数量
     */
    @TableField("actual_usdt_amount")
    private BigDecimal actualUsdtAmount;

    /**
     * 交易hash(用户向swft付款的hash)
     */
    @TableField("txhash")
    private String txhash;

    /**
     * 支付地址
     */
    @TableField("pay_address")
    private String payAddress;

    /**
     * 支付数量
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付币种
     */
    @TableField("pay_coin")
    private String payCoin;

    /**
     * swft订单号
     */
    @TableField("swft_order_id")
    private String swftOrderId;

    /**
     * 区块号
     */
    @TableField("block_number")
    private Long blockNumber;

    /**
     * 区块时间戳
     */
    @TableField("block_time")
    private Long blockTime;

    /**
     * 链上交易状态：1成功，2确认中，4失败
     */
    @TableField("tx_status")
    private Integer txStatus;

    /**
     * 状态：1已记入，2待处理(等待交易确认)，3待入账(交易已确认,待有效时间到了入账)，4失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 失败原因
     */
    @TableField("err_reason")
    private String errReason;

    /**
     * 生效时间戳
     */
    @TableField("effective_time")
    private Long effectiveTime;

    /**
     * 生效时间
     */
    @TableField("effective_at")
    private LocalDateTime effectiveAt;

    /**
     * IP地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 邀请人奖励：1已发放，0未发放(等待满足要求)
     */
    @TableField("inviter_reward")
    private Integer inviterReward;

    /**
     * 版本
     */
    @TableField("version")
    private Integer version;

    /**
     * 出局：1是，2否
     */
    @TableField("is_out")
    private Integer isOut;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;


} 