package com.lct.finance.model.dto;

import lombok.Data;
import lombok.Builder;

/**
 * 用户登录响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
public class UserLoginResponse {

    /**
     * 区块链网络
     */
    private String chain;

    /**
     * 用户等级
     */
    private Integer level;

    /**
     * 用户地址
     */
    private String address;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 创建时间戳
     */
    private Long createdAt;

    /**
     * JWT Token
     */
    private String token;

    /**
     * Token过期时间戳
     */
    private Long expiresIn;

    /**
     * Token刷新时间戳
     */
    private Long refreshIn;
}