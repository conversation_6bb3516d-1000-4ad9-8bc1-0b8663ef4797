package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 直推邀请响应DTO - 兼容旧PHP接口格式
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DirectInviteResponse {

    /**
     * 被邀请用户地址
     */
    @JsonProperty("user_address")
    private String userAddress;

    /**
     * 总销毁数量
     */
    @JsonProperty("total_burn_amount")
    private String totalBurnAmount;

    /**
     * 状态（1已入账，2待结算）
     */
    @JsonProperty("status")
    private Integer status;
} 