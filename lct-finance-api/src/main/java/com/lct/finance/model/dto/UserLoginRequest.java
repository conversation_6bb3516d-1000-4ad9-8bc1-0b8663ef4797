package com.lct.finance.model.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户登录请求DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
public class UserLoginRequest {

    /**
     * 区块链网络 (BSC)
     */
    @NotBlank(message = "区块链网络不能为空")
    private String chain;

    /**
     * 用户钱包地址
     */
    @NotBlank(message = "钱包地址不能为空")
    private String userAddress;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 原生代币余额
     */
    private String nativeBalance = "0";

    /**
     * 代币余额
     */
    private String tokenBalance = "-1";

    /**
     * 签名
     */
    @NotBlank(message = "签名不能为空")
    private String sign;

    /**
     * 签名消息
     */
    @NotBlank(message = "签名消息不能为空")
    private String message;

    /**
     * 时间戳随机数
     */
    @NotBlank(message = "时间戳不能为空")
    private String nonce;

    /**
     * 签名版本
     */
    private Integer version = 0;

    /**
     * 验证码key (新用户注册需要)
     */
    private String captchaKey;
    
    /**
     * 设备指纹
     * 用于设备识别和风险评估
     */
    private String deviceFingerprint;
}