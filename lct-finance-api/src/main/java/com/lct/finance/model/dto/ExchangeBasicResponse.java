package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 兑换基础数据响应DTO - 兼容旧PHP接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExchangeBasicResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * USDT兑LCT汇率
     */
    private BigDecimal usdt_lct;

    /**
     * LCT兑USDT汇率
     */
    private BigDecimal lct_usdt;

    /**
     * BSC授权地址
     */
    private String bsc_auth_address;

    /**
     * USDT兑LCT开关 (1:开启, 0:关闭)
     */
    private Integer exchange_switch_usdt2lct;

    /**
     * LCT兑USDT开关 (1:开启, 0:关闭)
     */
    private Integer exchange_switch_lct2usdt;

    /**
     * 日期列表 (近15天)
     */
    private List<String> dates;

    /**
     * LCT/USDT价格历史 (近15天)
     */
    private List<String> lctusdtList;

    /**
     * USDT/LCT价格历史 (近15天)
     */
    private List<String> usdtlctList;
} 