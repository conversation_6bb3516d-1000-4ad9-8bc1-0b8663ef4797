package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提现历史响应DTO - 兼容旧PHP接口格式
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawalHistoryResponse {

    /**
     * 币种简称
     */
    @JsonProperty("coin")
    private String coin;

    /**
     * 币种全称
     */
    @JsonProperty("coin_full")
    private String coinFull;

    /**
     * 订单号
     */
    @JsonProperty("order_no")
    private String orderNo;

    /**
     * 提现数量
     */
    @JsonProperty("amount")
    private String amount;

    /**
     * 币种类型
     */
    @JsonProperty("coin_type")
    private Integer coinType;

    /**
     * 实际到账数量
     */
    @JsonProperty("actual_amount")
    private String actualAmount;

    /**
     * 实际LCT数量
     */
    @JsonProperty("actual_lct_amount")
    private String actualLctAmount;

    /**
     * 手续费
     */
    @JsonProperty("fee")
    private String fee;

    /**
     * 交易哈希
     */
    @JsonProperty("txhash")
    private String txhash;

    /**
     * 接收地址
     */
    @JsonProperty("receive_address")
    private String receiveAddress;

    /**
     * 状态
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private String createdAt;
} 