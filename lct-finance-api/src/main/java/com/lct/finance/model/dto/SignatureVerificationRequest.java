package com.lct.finance.model.dto;

import com.lct.finance.model.constants.WithdrawalConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 签名验证请求
 * 统一封装所有签名验证相关参数
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignatureVerificationRequest {
    /**
     * 原始消息
     */
    private String message;
    
    /**
     * 签名
     */
    private String signature;
    
    /**
     * 签名地址
     */
    private String address;
    
    /**
     * 链类型（BSC/TRX，默认为BSC）
     */
    @Builder.Default
    private String chainType = WithdrawalConstants.ChainType.BSC;
    
    /**
     * 操作类型（LOGIN, WITHDRAWAL, EXCHANGE等）
     */
    private String operation;
    
    /**
     * 防重放nonce
     */
    private String nonce;
    
    /**
     * 客户端IP
     */
    private String clientIp;
    
    /**
     * 操作金额
     */
    private String amount;
    
    /**
     * 设备指纹
     */
    private String deviceFingerprint;
    
    /**
     * 是否执行风险评估
     */
    @Builder.Default
    private boolean performRiskAssessment = false;
    

} 