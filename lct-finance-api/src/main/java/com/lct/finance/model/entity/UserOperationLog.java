package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户操作日志实体类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Data
@TableName("lct_user_operation_log")
public class UserOperationLog {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户地址
     */
    private String userAddress;

    /**
     * 链类型
     */
    private String chain;

    /**
     * 操作类型（LOGIN, WITHDRAWAL, EXCHANGE 等）
     */
    private String operationType;

    /**
     * 操作金额
     */
    private BigDecimal amount;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * IP地理位置
     */
    private String ipLocation;

    /**
     * 设备指纹
     */
    private String deviceFingerprint;

    /**
     * 风险评分
     */
    private Integer riskLevel;

    /**
     * 风险评分详情 (JSON格式)
     * 记录风险评估的具体计算过程和各项得分
     */
    private String riskScoreDetail;

    /**
     * 操作状态 (1-成功, 0-失败)
     */
    private Integer status;

    /**
     * 额外信息（JSON格式）
     */
    private String extraInfo;

    /**
     * 操作时间
     */
    private LocalDateTime operationTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}