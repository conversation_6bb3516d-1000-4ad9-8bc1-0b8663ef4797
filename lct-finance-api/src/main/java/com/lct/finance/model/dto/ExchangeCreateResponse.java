package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 兑换订单创建响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExchangeCreateResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 支付地址（用户需要转账到此地址）
     */
    @JsonProperty("pay_address")
    private String payAddress;

    /**
     * 支付数量
     */
    @JsonProperty("pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付币种代码
     */
    @JsonProperty("pay_coin")
    private String payCoin;

    /**
     * 合约地址
     */
    @JsonProperty("contract")
    private String contract;

    /**
     * 币种小数位数
     */
    @JsonProperty("decimals")
    private Integer decimals;

    /**
     * 接收数量
     */
    @JsonProperty("receive_amount")
    private BigDecimal receiveAmount;

    /**
     * 接收币种代码
     */
    @JsonProperty("receive_coin")
    private String receiveCoin;

    /**
     * 接收地址
     */
    @JsonProperty("receive_address")
    private String receiveAddress;

    /**
     * 汇率
     */
    @JsonProperty("rate")
    private BigDecimal rate;

    /**
     * 手续费
     */
    @JsonProperty("fee")
    private BigDecimal fee;

    /**
     * 订单过期时间（秒）
     */
    @JsonProperty("expired_time")
    private Integer expiredTime;

    /**
     * 最小支付数量
     */
    @JsonProperty("min_amount")
    private BigDecimal minAmount;

    /**
     * 最大支付数量
     */
    @JsonProperty("max_amount")
    private BigDecimal maxAmount;

    /**
     * 兑换类型 1:USDT兑LCT, 2:LCT兑USDT
     */
    @JsonProperty("exchange_type")
    private Integer exchangeType;

    /**
     * 操作类型 1:正常(SWFT), 2:系统
     */
    @JsonProperty("action_type")
    private Integer actionType;

    /**
     * 状态描述
     */
    @JsonProperty("status_desc")
    private String statusDesc;

    /**
     * 温馨提示
     */
    @JsonProperty("tips")
    private String tips;

    /**
     * 创建成功的兑换订单响应
     */
    public static ExchangeCreateResponse success(String orderId, String payAddress, 
                                               BigDecimal payAmount, String payCoin,
                                               String contract, Integer decimals,
                                               BigDecimal receiveAmount, String receiveCoin,
                                               String receiveAddress, Integer exchangeType) {
        return ExchangeCreateResponse.builder()
                .orderId(orderId)
                .payAddress(payAddress)
                .payAmount(payAmount)
                .payCoin(payCoin)
                .contract(contract)
                .decimals(decimals)
                .receiveAmount(receiveAmount)
                .receiveCoin(receiveCoin)
                .receiveAddress(receiveAddress)
                .exchangeType(exchangeType)
                .actionType(2) // 默认系统操作
                .expiredTime(1800) // 30分钟过期
                .statusDesc("订单创建成功，请在30分钟内完成支付")
                .tips("请确保转账金额与订单金额完全一致，否则可能导致兑换失败")
                .build();
    }




} 