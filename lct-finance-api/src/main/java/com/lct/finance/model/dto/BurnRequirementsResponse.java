package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 销毁要求检查响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BurnRequirementsResponse {

    /**
     * 销毁需要的兑换比例
     * 如果为0表示不需要兑换
     */
    private BigDecimal burnNeedExchangeRate;

    /**
     * 销毁需要兑换剩余额度
     * 用户当前可用的兑换额度
     */
    private BigDecimal burnNeedExchangeRemaining;

    /**
     * 最低销毁数量
     */
    private BigDecimal minBurnAmount;

    /**
     * 用户当前状态
     * 1: 正常，2: 受限
     */
    private Integer userStatus;

    /**
     * 提示信息
     */
    private String message;
} 