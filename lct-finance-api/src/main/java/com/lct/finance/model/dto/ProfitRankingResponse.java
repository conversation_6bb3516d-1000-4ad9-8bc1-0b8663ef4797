package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 个人收益排行榜响应DTO - 兼容旧PHP接口格式
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProfitRankingResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 排名
     */
    @JsonProperty("rank")
    private Integer rank;

    /**
     * 用户地址（脱敏处理）
     */
    @JsonProperty("user_address")
    private String userAddress;

    /**
     * 收益金额（格式化后的字符串）
     */
    @JsonProperty("profit")
    private String profit;
} 