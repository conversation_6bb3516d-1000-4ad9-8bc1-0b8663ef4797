package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 用户信息响应DTO - 兼容旧PHP接口格式
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoResponse {

    /**
     * 用户等级
     */
    @JsonProperty("level")
    private Integer level;

    /**
     * 可提数量
     */
    @JsonProperty("available")
    private String available;

    /**
     * 可理财数量
     */
    @JsonProperty("finance")
    private String finance;

    /**
     * 个人销毁数量
     */
    @JsonProperty("total_burn")
    private String totalBurn;

    /**
     * 团队总计销毁数量
     */
    @JsonProperty("total_team_burn")
    private String totalTeamBurn;

    /**
     * 上级总计销毁数量
     */
    @JsonProperty("leader_total_burn")
    private String leaderTotalBurn;

    /**
     * 个人今日收益
     */
    @JsonProperty("total_today_profit")
    private String totalTodayProfit;

    /**
     * 今日个人收益
     */
    @JsonProperty("today_profit")
    private String todayProfit;

    /**
     * 个人全部收益
     */
    @JsonProperty("total_profit")
    private String totalProfit;

    /**
     * 个人今日团队收益
     */
    @JsonProperty("today_team_profit")
    private String todayTeamProfit;

    /**
     * 个人今日推广人数
     */
    @JsonProperty("today_invite_count")
    private Integer todayInviteCount;

    /**
     * 团队今日新增人数
     */
    @JsonProperty("today_team_invite_count")
    private Integer todayTeamInviteCount;

    /**
     * 团队总人数
     */
    @JsonProperty("team_total_count")
    private Integer teamTotalCount;

    /**
     * 团队今日新增销毁数量
     */
    @JsonProperty("today_team_burn")
    private String todayTeamBurn;

    /**
     * 角色
     */
    @JsonProperty("roles")
    private Integer roles;

    /**
     * 社区销毁总数量
     */
    @JsonProperty("community_total_burn")
    private String communityTotalBurn;

    /**
     * 社区总人数
     */
    @JsonProperty("community_total_count")
    private Integer communityTotalCount;

    /**
     * 是否出局
     */
    @JsonProperty("is_out")
    private Integer isOut;

    /**
     * 剩余出局收益
     */
    @JsonProperty("remaining_profit_out")
    private BigDecimal remainingProfitOut;
} 