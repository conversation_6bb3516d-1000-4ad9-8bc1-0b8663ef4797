package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包实体类 - 对应数据库wallets表
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lct_wallets")
public class Wallet {

    /**
     * 钱包ID (主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID (外键)
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 可用余额
     */
    @TableField("available")
    private BigDecimal available;

    /**
     * 冻结余额
     */
    @TableField("frozen")
    private BigDecimal frozen;

    /**
     * 理财余额
     */
    @TableField("finance")
    private BigDecimal finance;

    /**
     * 销毁余额
     */
    @TableField("burn")
    private BigDecimal burn;

    /**
     * 签到奖励
     */
    @TableField("checkin")
    private BigDecimal checkin;

    /**
     * 个人收益
     */
    @TableField("profit")
    private BigDecimal profit;

    /**
     * 团队收益
     */
    @TableField("team_profit")
    private BigDecimal teamProfit;

    /**
     * 直推奖励
     */
    @TableField("direct_invite")
    private BigDecimal directInvite;

    /**
     * 体验金
     */
    @TableField("experience")
    private BigDecimal experience;

    /**
     * 社区长返利
     */
    @TableField("community_chief_rebate")
    private BigDecimal communityChiefRebate;

    /**
     * 社区长体验金
     */
    @TableField("community_chief_experience")
    private BigDecimal communityChiefExperience;

    /**
     * 代理可用余额
     */
    @TableField("agent_available")
    private BigDecimal agentAvailable;

    /**
     * 代理冻结余额
     */
    @TableField("agent_frozen")
    private BigDecimal agentFrozen;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;



} 