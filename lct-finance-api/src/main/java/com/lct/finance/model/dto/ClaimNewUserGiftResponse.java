package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 领取新用户礼包响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2025-01-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClaimNewUserGiftResponse {

    /**
     * 礼包金额
     */
    private BigDecimal giftAmount;

    /**
     * 用户新的代币余额
     */
    private BigDecimal newBalance;

    /**
     * 领取时间
     */
    private LocalDateTime claimedAt;

    /**
     * 创建成功领取响应
     * 
     * @param giftAmount 礼包金额
     * @param newBalance 新余额
     * @param claimedAt 领取时间
     * @return 领取成功响应
     */
    public static ClaimNewUserGiftResponse success(BigDecimal giftAmount, BigDecimal newBalance, LocalDateTime claimedAt) {
        return ClaimNewUserGiftResponse.builder()
                .giftAmount(giftAmount)
                .newBalance(newBalance)
                .claimedAt(claimedAt)
                .build();
    }
}