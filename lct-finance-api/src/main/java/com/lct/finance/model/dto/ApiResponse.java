package com.lct.finance.model.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 统一API响应格式
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponse<T> {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 请求是否成功
     */
    private Boolean success;

    /**
     * 错误类型 (用于前端分类处理)
     */
    private String errorType;

    /**
     * 错误处理建议 (用于前端展示或处理)
     */
    private String suggestion;

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(200);
        response.setMsg("success");
        response.setData(data);
        response.setSuccess(true);
        return response;
    }

    /**
     * 成功响应 - 无数据
     */
    public static <T> ApiResponse<T> success() {
        return success(null);
    }

    /**
     * 错误响应
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(code);
        response.setMsg(message);
        response.setSuccess(false);
        return response;
    }

    /**
     * 错误响应 - 默认500
     */
    public static <T> ApiResponse<T> error(String message) {
        return error(500, message);
    }

    /**
     * 参数验证错误
     */
    public static <T> ApiResponse<T> validationError(String message) {
        return error(403, message);
    }

    /**
     * 错误响应 - 带错误数据
     */
    public static <T> ApiResponse<T> error(Integer code, String message, T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(code);
        response.setMsg(message);
        response.setData(data);
        response.setSuccess(false);
        return response;
    }

    /**
     * 业务错误响应 - 完整版本
     */
    public static <T> ApiResponse<T> businessError(Integer code, String message, String errorType, String suggestion) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(code);
        response.setMsg(message);
        response.setSuccess(false);
        response.setErrorType(errorType);
        response.setSuggestion(suggestion);
        return response;
    }

    /**
     * 业务错误响应 - 带数据
     */
    public static <T> ApiResponse<T> businessError(Integer code, String message, String errorType, String suggestion,
            T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setCode(code);
        response.setMsg(message);
        response.setSuccess(false);
        response.setErrorType(errorType);
        response.setSuggestion(suggestion);
        response.setData(data);
        return response;
    }

    /**
     * 认证失败响应
     */
    public static <T> ApiResponse<T> authError(String message) {
        return businessError(401, message, "AUTH_ERROR", "请重新登录或检查您的身份验证信息");
    }

    /**
     * 权限不足响应
     */
    public static <T> ApiResponse<T> permissionError(String message) {
        return businessError(403, message, "PERMISSION_ERROR", "您没有执行此操作的权限，请联系管理员");
    }

    /**
     * 参数错误响应
     */
    public static <T> ApiResponse<T> paramError(String message) {
        return businessError(400, message, "PARAM_ERROR", "请检查您的输入参数是否正确");
    }

    /**
     * 业务逻辑错误响应
     */
    public static <T> ApiResponse<T> businessLogicError(String message) {
        return businessError(400, message, "BUSINESS_ERROR", "请按照系统提示进行操作");
    }

    /**
     * 系统错误响应
     */
    public static <T> ApiResponse<T> systemError(String message) {
        return businessError(500, message, "SYSTEM_ERROR", "系统暂时出现问题，请稍后重试或联系技术支持");
    }

    /**
     * 限流错误响应
     */
    public static <T> ApiResponse<T> rateLimitError(String message) {
        return businessError(429, message, "RATE_LIMIT_ERROR", "请求过于频繁，请稍后再试");
    }
}