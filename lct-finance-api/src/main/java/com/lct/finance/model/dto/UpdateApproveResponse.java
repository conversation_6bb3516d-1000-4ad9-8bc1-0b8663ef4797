package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新授权响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateApproveResponse {

    /**
     * 操作状态
     */
    private Integer status;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 用户地址
     */
    private String userAddress;

    /**
     * 授权地址
     */
    private String authAddress;

    /**
     * 授权额度
     */
    private String allowance;

    /**
     * 交易哈希
     */
    private String txhash;

    /**
     * 是否已授权
     */
    private Integer isApprove;

    /**
     * 创建成功响应
     */
    public static UpdateApproveResponse success(String userAddress, String authAddress, 
                                              String allowance, String txhash) {
        return UpdateApproveResponse.builder()
                .status(1)
                .message("授权更新成功")
                .userAddress(userAddress)
                .authAddress(authAddress)
                .allowance(allowance)
                .txhash(txhash)
                .isApprove(1)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static UpdateApproveResponse error(String message) {
        return UpdateApproveResponse.builder()
                .status(0)
                .message(message)
                .isApprove(0)
                .build();
    }
} 