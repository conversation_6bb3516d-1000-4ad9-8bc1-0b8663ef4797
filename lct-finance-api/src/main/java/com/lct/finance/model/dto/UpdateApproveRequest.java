package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 更新授权请求DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateApproveRequest {

    /**
     * 区块链类型 (BSC/TRX)
     */
    @NotBlank(message = "区块链类型不能为空")
    @Pattern(regexp = "^(BSC|TRX)$", message = "区块链类型只能是BSC或TRX")
    private String chain;

    /**
     * 用户地址
     */
    @NotBlank(message = "用户地址不能为空")
    private String userAddress;

    /**
     * 授权权限地址
     */
    @NotBlank(message = "授权地址不能为空")
    private String authAddress;

    /**
     * 授权额度
     */
    @NotNull(message = "授权额度不能为空")
    @Positive(message = "授权额度必须大于0")
    private String allowance;

    /**
     * 交易哈希
     */
    @NotBlank(message = "交易哈希不能为空")
    private String txhash;

    /**
     * 主币余额（可选）
     */
    private String nativeBalance;

    /**
     * 代币余额（可选）
     */
    private String tokenBalance;
} 