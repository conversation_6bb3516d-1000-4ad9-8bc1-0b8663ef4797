package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 领取新用户礼包请求DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2025-01-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClaimNewUserGiftRequest {

    /**
     * 签名消息
     */
//    @NotBlank(message = "签名消息不能为空")
    private String message;

    /**
     * 签名
     */
//    @NotBlank(message = "签名不能为空")
    private String sign;
    
    /**
     * 防重放nonce
     */
    @NotBlank(message = "nonce不能为空")
    private String nonce;
    
    /**
     * 设备指纹（可选）
     */
    private String deviceFingerprint;
} 