package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 风险评估结果
 * 包含风险评分和风险因素详情
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskAssessmentResult {
    /**
     * 风险级别（0-100）
     */
    private int riskLevel;

    /**
     * 是否需要额外验证
     */
    private boolean requiresAdditionalVerification;

    /**
     * 风险因素列表
     */
    @Builder.Default
    private List<String> riskFactors = new ArrayList<>();

    /**
     * 风险评分详情
     * 记录具体的评分计算过程
     */
    private RiskScoreDetail scoreDetail;

    // Getter methods
    public int getRiskLevel() {
        return riskLevel;
    }

    public boolean isRequiresAdditionalVerification() {
        return requiresAdditionalVerification;
    }

    public List<String> getRiskFactors() {
        return riskFactors;
    }

    public RiskScoreDetail getScoreDetail() {
        return scoreDetail;
    }

    // Setter methods
    public void setRiskLevel(int riskLevel) {
        this.riskLevel = riskLevel;
    }

    public void setRequiresAdditionalVerification(boolean requiresAdditionalVerification) {
        this.requiresAdditionalVerification = requiresAdditionalVerification;
    }

    public void setRiskFactors(List<String> riskFactors) {
        this.riskFactors = riskFactors;
    }

    public void setScoreDetail(RiskScoreDetail scoreDetail) {
        this.scoreDetail = scoreDetail;
    }

    /**
     * 创建低风险结果
     */
    public static RiskAssessmentResult lowRisk() {
        RiskScoreDetail scoreDetail = RiskScoreDetail.builder()
                .baseScore(10)
                .totalScore(10)
                .build();
        // 不再添加重复的基础评分项，baseScore已经包含了基础分数

        return RiskAssessmentResult.builder()
                .riskLevel(10)
                .requiresAdditionalVerification(false)
                .scoreDetail(scoreDetail)
                .build();
    }

    /**
     * 创建中风险结果
     */
    public static RiskAssessmentResult mediumRisk(List<String> factors) {
        RiskScoreDetail scoreDetail = RiskScoreDetail.builder()
                .baseScore(25)
                .totalScore(50)
                .build();
        // 不再添加重复的基础评分项，baseScore已经包含了基础分数

        return RiskAssessmentResult.builder()
                .riskLevel(50)
                .requiresAdditionalVerification(false)
                .riskFactors(factors)
                .scoreDetail(scoreDetail)
                .build();
    }

    /**
     * 创建高风险结果
     */
    public static RiskAssessmentResult highRisk(List<String> factors) {
        RiskScoreDetail scoreDetail = RiskScoreDetail.builder()
                .baseScore(30)
                .totalScore(80)
                .build();
        // 不再添加重复的基础评分项，baseScore已经包含了基础分数

        return RiskAssessmentResult.builder()
                .riskLevel(80)
                .requiresAdditionalVerification(true)
                .riskFactors(factors)
                .scoreDetail(scoreDetail)
                .build();
    }

    /**
     * 添加风险因素
     */
    public void addRiskFactor(String factor) {
        if (this.riskFactors == null) {
            this.riskFactors = new ArrayList<>();
        }
        this.riskFactors.add(factor);
    }
}