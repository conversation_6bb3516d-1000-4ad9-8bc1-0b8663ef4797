package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 兑换订单创建请求DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExchangeCreateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户地址
     */
    @NotBlank(message = "用户地址不能为空")
    @Size(max = 255, message = "用户地址长度不能超过255个字符")
    private String address;

    /**
     * 存入数量
     */
    @NotNull(message = "存入数量不能为空")
    @DecimalMin(value = "0.000001", message = "存入数量必须大于0")
    @Digits(integer = 18, fraction = 6, message = "存入数量格式不正确")
    private BigDecimal depositAmount;

    /**
     * 存入币种类型 1:LCT(BSC), 2:USDT(BSC), 3:USDT(TRON)
     */
    @NotNull(message = "存入币种类型不能为空")
    @Min(value = 1, message = "存入币种类型必须在1-2之间")
    @Max(value = 2, message = "存入币种类型必须在1-2之间")
    private Integer depositCoinType;

    /**
     * 接收数量
     */
    @NotNull(message = "接收数量不能为空")
    @DecimalMin(value = "0.000001", message = "接收数量必须大于0")
    @Digits(integer = 18, fraction = 6, message = "接收数量格式不正确")
    private BigDecimal receiveAmount;

    /**
     * 接收币种类型 1:LCT(BSC), 2:USDT(BSC), 3:USDT(TRON)
     */
    @NotNull(message = "接收币种类型不能为空")
    @Min(value = 1, message = "接收币种类型必须在1-2之间")
    @Max(value = 2, message = "接收币种类型必须在1-2之间")
    private Integer receiveCoinType;

    /**
     * 接收地址
     */
    @NotBlank(message = "接收地址不能为空")
    @Size(max = 255, message = "接收地址长度不能超过255个字符")
    private String receiveAddress;

    /**
     * 客户端IP地址
     */
    @Size(max = 100, message = "IP地址长度不能超过100个字符")
    private String ip;

//    /**
//     * nonce值
//     * 用于防止重放攻击
//     */
//    @NotBlank(message = "nonce不能为空")
//    private String nonce;

//    /**
//     * 签名消息
//     */
//    @NotBlank(message = "签名消息不能为空")
//    private String message;
//
//    /**
//     * 签名
//     */
//    @NotBlank(message = "签名不能为空")
//    private String sign;

    /**
     * 设备指纹
     * 用于设备识别和风险评估
     */
    private String deviceFingerprint;

}