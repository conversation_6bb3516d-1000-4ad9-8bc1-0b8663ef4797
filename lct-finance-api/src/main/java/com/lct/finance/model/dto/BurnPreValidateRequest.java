package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 销毁预校验请求
 * 在执行区块链交易前进行所有必要的校验
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BurnPreValidateRequest {

    /**
     * 销毁数量
     * 必须大于0
     */
    @NotNull(message = "销毁数量不能为空")
    @DecimalMin(value = "0.000001", message = "销毁数量必须大于0")
    private BigDecimal amount;

    /**
     * 用户地址（可选，用于验证）
     */
    private String address;
}