package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 销毁记录历史响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BurnHistoryResponse {

    /**
     * 销毁记录列表
     */
    private List<BurnHistoryItem> list;

    /**
     * 销毁记录项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BurnHistoryItem {
        
        /**
         * 销毁数量
         */
        private String amount;

        /**
         * 实际销毁数量
         */
        private String actualAmount;

        /**
         * 生效时间
         */
        private String effectiveTime;

        /**
         * 生效时间戳
         */
        private String effectiveAt;

        /**
         * 交易哈希
         */
        private String txhash;

        /**
         * 区块号
         */
        private Long blockNumber;

        /**
         * 区块时间
         */
        private String blockTime;

        /**
         * 交易状态
         */
        private Integer txStatus;

        /**
         * 状态
         */
        private Integer status;

        /**
         * 创建时间
         */
        private String createdAt;
    }
} 