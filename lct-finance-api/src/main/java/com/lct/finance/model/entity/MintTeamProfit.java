package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 每日铸造团队收益实体类
 * 对应数据库表：mint_team_profit
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("lct_mint_team_profit")
public class MintTeamProfit {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID（数据库字段为int(10) unsigned）
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 来源用户ID (下属团队用户)（数据库字段为int(10) unsigned）
     */
    @TableField("source_user_id")
    private Integer sourceUserId;

    /**
     * 基数(以此值计算收益数量)
     */
    @TableField("base_amount")
    private BigDecimal baseAmount;

    /**
     * 预计数量
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 实际数量
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 利率
     */
    @TableField("rate")
    private BigDecimal rate;

    /**
     * 用户等级(发放时)
     */
    @TableField("user_level")
    private Integer userLevel;

    /**
     * 邀请等级
     */
    @TableField("invite_level")
    private Integer inviteLevel;

    /**
     * 收益日期
     */
    @TableField("profit_date")
    private LocalDate profitDate;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
} 