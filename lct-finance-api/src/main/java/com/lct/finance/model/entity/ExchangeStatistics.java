package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 兑换统计实体类
 * 对应数据库表：lct_exchange_statistics
 * 用于记录用户的兑换统计数据，包括总转入、总接收和销毁兑换余额管理
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("lct_exchange_statistics")
public class ExchangeStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 兑换类型
     * 对应数据库字段：exchange_type
     */
    @TableField("exchange_type")
    private Integer exchangeType;

    /**
     * 总转入数量
     * 精度：decimal(30,18)
     */
    @TableField("total_deposit_amount")
    @Builder.Default
    private BigDecimal totalDepositAmount = BigDecimal.ZERO;

    /**
     * 总接收数量
     * 精度：decimal(30,18)
     */
    @TableField("total_receive_amount")
    @Builder.Default
    private BigDecimal totalReceiveAmount = BigDecimal.ZERO;

    /**
     * 销毁需要兑换剩余额度
     * 用户可用于销毁时扣除的数量
     * 精度：decimal(30,18)
     */
    @TableField("burn_need_exchange_remaining")
    @Builder.Default
    private BigDecimal burnNeedExchangeRemaining = BigDecimal.ZERO;

    /**
     * 创建时间
     * 数据库默认：CURRENT_TIMESTAMP
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     * 数据库默认：CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}