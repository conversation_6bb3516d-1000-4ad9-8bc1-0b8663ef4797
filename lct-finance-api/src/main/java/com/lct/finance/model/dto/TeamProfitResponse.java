package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 团队收益记录响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TeamProfitResponse {

    /**
     * 团队收益记录列表
     */
    private List<TeamProfitItem> list;

    /**
     * 团队收益记录项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TeamProfitItem {
        
        /**
         * 收益数量
         */
        private String amount;

        /**
         * 实际收益数量
         * 同时支持驼峰命名(actualAmount)和下划线命名(actual_amount)以保持兼容性
         */
        @JsonProperty("actual_amount")
        private String actualAmount;

        /**
         * 来源用户地址
         * 同时支持驼峰命名(sourceUserAddress)和下划线命名(source_user_address)以保持兼容性
         */
        @JsonProperty("source_user_address")
        private String sourceUserAddress;

        /**
         * 创建时间
         * 同时支持驼峰命名(createdAt)和下划线命名(created_at)以保持兼容性
         */
        @JsonProperty("created_at")
        private String createdAt;
    }
} 