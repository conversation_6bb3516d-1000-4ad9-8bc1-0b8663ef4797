package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 邀请关系实体类 - 对应数据库invites表
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("lct_invites")
public class Invite {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 邀请人用户ID
     */
    @TableField("inviter_user_id")
    private Long inviterUserId;

    /**
     * 被邀请人用户ID
     */
    @TableField("invitee_user_id")
    private Long inviteeUserId;

    /**
     * 邀请层级（1为直推，2为二级，以此类推）
     */
    @TableField("level")
    private Integer level;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 常量定义
    public static final int LEVEL_DIRECT = 1; // 直推


}