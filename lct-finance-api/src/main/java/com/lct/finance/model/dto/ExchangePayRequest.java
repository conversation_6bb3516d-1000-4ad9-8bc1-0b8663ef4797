package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 兑换支付确认请求DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExchangePayRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户地址
     */
    @NotBlank(message = "用户地址不能为空")
    @Size(max = 255, message = "用户地址长度不能超过255个字符")
    private String address;

    /**
     * 订单ID
     */
    @NotBlank(message = "订单ID不能为空")
    @Size(max = 255, message = "订单ID长度不能超过255个字符")
    private String orderId;

    /**
     * 交易哈希
     */
    @NotBlank(message = "交易哈希不能为空")
    @Pattern(regexp = "^0x[a-fA-F0-9]{64}$", message = "交易哈希格式不正确")
    private String txhash;

//    /**
//     * nonce值
//     * 用于防止重放攻击
//     */
//    @NotBlank(message = "nonce不能为空")
//    private String nonce;
//
//    /**
//     * 签名消息
//     */
//    @NotBlank(message = "签名消息不能为空")
//    private String message;
//
//    /**
//     * 签名
//     */
//    @NotBlank(message = "签名不能为空")
//    private String sign;
//
    /**
     * 设备指纹
     * 用于设备识别和风险评估
     */
    private String deviceFingerprint;

}