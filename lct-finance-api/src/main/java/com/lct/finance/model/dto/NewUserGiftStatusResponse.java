package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 新用户礼包状态响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2025-01-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewUserGiftStatusResponse {

    /**
     * 是否为新用户
     */
    @JsonProperty("is_new_user")
    private Boolean isNewUser;

    /**
     * 是否已领取礼包
     */
    @JsonProperty("status")
    private Integer status;



    /**
     * 创建默认的新用户未领取状态
     *
     * @return 新用户未领取状态响应
     */
    public static NewUserGiftStatusResponse newUserNotClaimed() {
        return NewUserGiftStatusResponse.builder()
                .isNewUser(true)
                .status(0)
                .build();
    }

    /**
     * 创建新用户已领取状态
     *
     * @param amount 礼包金额
     * @return 新用户已领取状态响应
     */
    public static NewUserGiftStatusResponse newUserClaimed(BigDecimal amount) {
        return NewUserGiftStatusResponse.builder()
                .isNewUser(true)
                .status(1)
                .build();
    }

    /**
     * 创建老用户状态
     *
     * @return 老用户状态响应
     */
    public static NewUserGiftStatusResponse oldUser() {
        return NewUserGiftStatusResponse.builder()
                .isNewUser(false)
                .status(1)
                .build();
    }
}