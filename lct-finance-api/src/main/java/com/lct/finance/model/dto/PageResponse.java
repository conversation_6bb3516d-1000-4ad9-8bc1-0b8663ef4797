package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 通用分页响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2025-01-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数据列表
     */
    @JsonProperty("list")
    private List<T> list;

    /**
     * 分页信息
     */
    @JsonProperty("pagination")
    private PaginationInfo pagination;

    /**
     * 分页信息内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PaginationInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 当前页码
         */
        @JsonProperty("currentPage")
        private Integer currentPage;

        /**
         * 每页大小
         */
        @JsonProperty("pageSize")
        private Integer pageSize;

        /**
         * 总页数
         */
        @JsonProperty("totalPages")
        private Integer totalPages;

        /**
         * 总记录数
         */
        @JsonProperty("totalRecords")
        private Long totalRecords;

        /**
         * 是否有下一页
         */
        @JsonProperty("hasNext")
        private Boolean hasNext;

        /**
         * 是否有上一页
         */
        @JsonProperty("hasPrev")
        private Boolean hasPrev;
    }

    /**
     * 从MyBatis-Plus的Page对象创建分页响应
     * 
     * @param page MyBatis-Plus分页对象
     * @param <T> 数据类型
     * @return 分页响应
     */
    public static <T> PageResponse<T> fromPage(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        PaginationInfo pagination = PaginationInfo.builder()
                .currentPage((int) page.getCurrent())
                .pageSize((int) page.getSize())
                .totalPages((int) page.getPages())
                .totalRecords(page.getTotal())
                .hasNext(page.hasNext())
                .hasPrev(page.hasPrevious())
                .build();

        return PageResponse.<T>builder()
                .list(page.getRecords())
                .pagination(pagination)
                .build();
    }

    /**
     * 手动创建分页响应
     * 
     * @param list 数据列表
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param totalRecords 总记录数
     * @param <T> 数据类型
     * @return 分页响应
     */
    public static <T> PageResponse<T> create(List<T> list, Integer currentPage, Integer pageSize, Long totalRecords) {
        int totalPages = (int) Math.ceil((double) totalRecords / pageSize);
        boolean hasNext = currentPage < totalPages;
        boolean hasPrev = currentPage > 1;

        PaginationInfo pagination = PaginationInfo.builder()
                .currentPage(currentPage)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .totalRecords(totalRecords)
                .hasNext(hasNext)
                .hasPrev(hasPrev)
                .build();

        return PageResponse.<T>builder()
                .list(list)
                .pagination(pagination)
                .build();
    }

    /**
     * 创建空的分页响应
     * 
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 空的分页响应
     */
    public static <T> PageResponse<T> empty(Integer currentPage, Integer pageSize) {
        return create(List.of(), currentPage, pageSize, 0L);
    }
} 