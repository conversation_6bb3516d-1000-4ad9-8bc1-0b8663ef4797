package com.lct.finance.model.response;

import com.lct.finance.model.dto.RiskAssessmentResult;
import com.lct.finance.model.dto.SignatureVerificationResult;
import lombok.Builder;
import lombok.Data;

/**
 * 操作验证结果
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
public class OperationValidationResult {

    /**
     * 验证是否成功
     */
    private boolean success;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 签名验证结果
     */
    private SignatureVerificationResult signatureResult;

    /**
     * 风险评估结果
     */
    private RiskAssessmentResult riskResult;

    /**
     * 风险级别
     */
    private Integer riskLevel;

    /**
     * 是否需要额外验证
     */
    private boolean requiresAdditionalVerification;
}