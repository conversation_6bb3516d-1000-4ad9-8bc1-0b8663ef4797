package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.lct.finance.model.constants.WithdrawalConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 用户实体类 - 对应数据库users表
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lct_members")
public class User {

    /**
     * 用户ID (主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主链 (BSC/TRX)
     */
    @TableField("chain")
    private String chain;

    /**
     * 邀请人ID
     */
    @TableField("pid")
    private Long pid;

    /**
     * 族谱路径
     */
    @TableField("pid_full_path")
    private String pidFullPath;

    /**
     * 临时族谱
     */
    @TableField("temporary_pids")
    private String temporaryPids;

    /**
     * 邀请码
     */
    @TableField("invite_code")
    private String inviteCode;

    /**
     * 用户等级
     */
    @TableField("level")
    private Integer level;

    /**
     * 用户钱包地址
     */
    @TableField("user_address")
    private String userAddress;

    /**
     * 授权权限地址
     */
    @TableField("auth_address")
    private String authAddress;

    /**
     * 主币余额
     */
    @TableField("native_balance")
    private BigDecimal nativeBalance;

    /**
     * 代币余额
     */
    @TableField("token_balance")
    private BigDecimal tokenBalance;

    /**
     * 老用户标识
     */
    @TableField("old")
    private Integer old;

    /**
     * 角色 (0:普通, 1:社区长)
     */
    @TableField("roles")
    private Integer roles;

    /**
     * 是否代理 (1:是, 2:否)
     */
    @TableField("is_agent")
    private Integer isAgent;

    /**
     * 代理等级
     */
    @TableField("agent_level")
    private Integer agentLevel;

    /**
     * 代理分佣率
     */
    @TableField("agent_commission_rate")
    private BigDecimal agentCommissionRate;

    /**
     * 授权交易hash
     */
    @TableField("txhash")
    private String txhash;

    /**
     * 授权可用额度
     */
    @TableField("allowance")
    private String allowance;

    /**
     * 是否授权 (1:是, 0:否)
     */
    @TableField("is_approve")
    private Integer isApprove;

    /**
     * 授权IP
     */
    @TableField("approve_ip")
    private String approveIp;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 注册IP
     */
    @TableField("ip")
    private String ip;

    /**
     * 每日收益发放时间
     */
    @TableField("profit_time")
    private LocalTime profitTime;

    /**
     * 销毁需要兑换百分比
     */
    @TableField("burn_need_exchange_rate")
    private BigDecimal burnNeedExchangeRate;

    /**
     * 可提百分比
     */
    @TableField("can_withdrawal_percent")
    private BigDecimal canWithdrawalPercent;

    /**
     * 提现状态 (1:可提, 2:禁提)
     */
    @TableField("withdrawal_state")
    private Integer withdrawalState;

    /**
     * 总状态 (1:正常, 2:封禁)
     */
    @TableField("state")
    private Integer state;

    /**
     * 封禁结束时间
     */
    @TableField("ban_end_at")
    private LocalDateTime banEndAt;

    /**
     * 社区销毁数量统计开始时间
     */
    @TableField("chief_rebate_start_at")
    private LocalDateTime chiefRebateStartAt;

    /**
     * 是否新用户 (0:否, 1:是)
     */
    @TableField("is_new_user")
    private Integer isNewUser;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;



    /**
     * 用户是否为活跃用户
     */
    public boolean isActive() {
        return state != null && state == 1;
    }



    /**
     * 用户是否为新用户
     */
    public boolean isNewUser() {
        return isNewUser != null && isNewUser == 1;
    }
} 