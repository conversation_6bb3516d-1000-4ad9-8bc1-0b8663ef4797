package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 风险评分详情
 * 记录风险评估的每项得分和计算过程
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskScoreDetail {

    /**
     * 基础风险分数
     */
    private int baseScore;

    /**
     * 总风险分数
     */
    private int totalScore;

    /**
     * 各项评分明细
     */
    @Builder.Default
    private List<ScoreItem> scoreItems = new ArrayList<>();

    /**
     * 评分项详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreItem {
        /**
         * 评分项名称
         */
        private String itemName;

        /**
         * 得分
         */
        private int score;

        /**
         * 权重
         */
        private double weight;

        /**
         * 评分原因/描述
         */
        private String reason;

        /**
         * 是否为风险项
         */
        private boolean isRisk;
    }

    /**
     * 添加评分项
     */
    public void addScoreItem(String itemName, int score, double weight, String reason, boolean isRisk) {
        if (this.scoreItems == null) {
            this.scoreItems = new ArrayList<>();
        }

        ScoreItem item = ScoreItem.builder()
                .itemName(itemName)
                .score(score)
                .weight(weight)
                .reason(reason)
                .isRisk(isRisk)
                .build();

        this.scoreItems.add(item);
    }

    /**
     * 添加评分项（简化版本）
     */
    public void addScoreItem(String itemName, int score, String reason) {
        addScoreItem(itemName, score, 1.0, reason, score > 0);
    }

    /**
     * 计算总分
     */
    public void calculateTotalScore() {
        int total = baseScore;
        if (scoreItems != null) {
            for (ScoreItem item : scoreItems) {
                total += (int) (item.score * item.weight);
            }
        }
        this.totalScore = Math.max(0, Math.min(100, total)); // 限制在0-100之间
    }

    /**
     * 获取格式化的评分详情
     */
    public String getFormattedDetail() {
        StringBuilder sb = new StringBuilder();
        sb.append("基础分数: ").append(baseScore);

        if (scoreItems != null && !scoreItems.isEmpty()) {
            sb.append(", 详细评分: [");
            for (int i = 0; i < scoreItems.size(); i++) {
                ScoreItem item = scoreItems.get(i);
                if (i > 0)
                    sb.append(", ");
                sb.append(item.itemName).append(": +").append(item.score);
                if (!item.reason.isEmpty()) {
                    sb.append("(").append(item.reason).append(")");
                }
            }
            sb.append("]");
        }

        sb.append(", 总分: ").append(totalScore);
        return sb.toString();
    }

    /**
     * 转换为JSON字符串（用于数据库存储）
     */
    public String toJsonString() {
        try {
            // 简化的JSON格式，避免引入复杂的JSON库依赖
            StringBuilder json = new StringBuilder();
            json.append("{");
            json.append("\"baseScore\":").append(baseScore).append(",");
            json.append("\"totalScore\":").append(totalScore).append(",");
            json.append("\"scoreItems\":[");

            if (scoreItems != null) {
                for (int i = 0; i < scoreItems.size(); i++) {
                    if (i > 0)
                        json.append(",");
                    ScoreItem item = scoreItems.get(i);
                    json.append("{");
                    json.append("\"itemName\":\"").append(escapeJson(item.itemName)).append("\",");
                    json.append("\"score\":").append(item.score).append(",");
                    json.append("\"weight\":").append(item.weight).append(",");
                    json.append("\"reason\":\"").append(escapeJson(item.reason)).append("\",");
                    json.append("\"isRisk\":").append(item.isRisk);
                    json.append("}");
                }
            }

            json.append("]}");
            return json.toString();
        } catch (Exception e) {
            return "{\"error\":\"Failed to serialize: " + e.getMessage() + "\"}";
        }
    }

    /**
     * JSON字符串转义
     */
    private String escapeJson(String str) {
        if (str == null)
            return "";
        return str.replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }
}