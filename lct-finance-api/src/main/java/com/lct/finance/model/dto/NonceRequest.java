package com.lct.finance.model.dto;

import com.lct.finance.model.constants.WithdrawalConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * Nonce请求DTO
 * 用于请求生成特定操作的nonce
 * 
 * <AUTHOR> Finance Team
 * @since 2024-06-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NonceRequest {
    
    /**
     * 用户地址
     */
    @NotBlank(message = "用户地址不能为空")
    private String userAddress;
    
    /**
     * 操作类型
     * 例如: LOGIN, WITHDRAWAL, BURN, EXCHANGE_PAY等
     */
    @NotBlank(message = "操作类型不能为空")
    private String operation;
    
    /**
     * 链类型
     * 默认为BSC
     */
    @Builder.Default
    private String chain = WithdrawalConstants.ChainType.BSC;
    
    /**
     * 额外数据
     * 可以传递操作相关的额外参数
     */
    private Map<String, Object> extraData;
} 