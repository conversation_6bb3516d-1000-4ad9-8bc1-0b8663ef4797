package com.lct.finance.model.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * SWFT账户兑换响应
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
public class SwftAccountExchangeResponse {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 存入币种代码
     */
    private String depositCoinCode;

    /**
     * 接收币种代码
     */
    private String receiveCoinCode;

    /**
     * 存入数量
     */
    private BigDecimal depositCoinAmt;

    /**
     * 接收数量
     */
    private BigDecimal receiveCoinAmt;

    /**
     * 平台支付地址 - 用户需要向这个地址转账
     */
    private String platformAddr;

    /**
     * 存入币种状态
     */
    private String depositCoinState;

    /**
     * 存入币种手续费率
     */
    private BigDecimal depositCoinFeeRate;

    /**
     * 存入币种手续费金额
     */
    private BigDecimal depositCoinFeeAmt;

    /**
     * 订单状态
     */
    private String orderState;

    /**
     * 目标接收地址
     */
    private String destinationAddr;

    /**
     * 退款地址
     */
    private String refundAddr;

    /**
     * 即时汇率
     */
    private BigDecimal instantRate;

    /**
     * 链费用
     */
    private BigDecimal chainFee;

    /**
     * 创建时间
     */
    private String createTime;
}