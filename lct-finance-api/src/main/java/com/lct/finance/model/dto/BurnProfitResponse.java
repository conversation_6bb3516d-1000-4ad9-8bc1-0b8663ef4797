package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 销毁收益记录响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BurnProfitResponse {

    /**
     * 收益记录列表
     */
    private List<BurnProfitItem> list;

    /**
     * 收益记录项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BurnProfitItem {
        
        /**
         * 收益数量
         */
        private String amount;

        /**
         * 实际收益数量
         */
        private String actualAmount;

        /**
         * 创建时间
         */
        private String createdAt;
    }
} 