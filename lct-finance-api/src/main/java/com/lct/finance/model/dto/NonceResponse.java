package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Nonce响应DTO
 * 包含生成的nonce及其相关信息
 * 
 * <AUTHOR> Finance Team
 * @since 2024-06-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NonceResponse {
    
    /**
     * 生成的nonce值
     * 格式: timestamp:randomString:operation:userAddressPrefix
     */
    private String nonce;
    
    /**
     * 用户地址
     */
    private String userAddress;
    
    /**
     * 操作类型
     */
    private String operation;
    
    /**
     * 生成时间戳（秒）
     */
    private long timestamp;
    
    /**
     * 过期时间（秒）
     */
    private int expireIn;
    
    /**
     * 链类型
     */
    private String chain;
} 