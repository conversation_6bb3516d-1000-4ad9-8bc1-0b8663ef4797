package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 邀请信息响应DTO - 兼容旧PHP接口格式
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InvitationResponse {

    /**
     * 用户链类型
     */
    @JsonProperty("chain")
    private String chain;

    /**
     * 用户等级
     */
    @JsonProperty("level")
    private Integer level;

    /**
     * 用户地址
     */
    @JsonProperty("address")
    private String address;

    /**
     * 邀请码
     */
    @JsonProperty("invite_code")
    private String inviteCode;

    /**
     * 创建时间戳
     */
    @JsonProperty("created_at")
    private Long createdAt;

    /**
     * 邀请链接
     */
    @JsonProperty("invite_link")
    private String inviteLink;
} 