package com.lct.finance.model.constants;

/**
 * 提现相关常量
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
public class WithdrawalConstants {

    /**
     * 币种类型 - 请使用 CommonConstants.CoinType 替代
     * 
     * @deprecated 使用 CommonConstants.CoinType
     */
    @Deprecated
    public static class CoinType {
        /** LCT币种 */
        public static final int LCT = 1;
        /** USDT(BSC)币种 */
        public static final int USDT_BSC = 2;

        /** USDT(TRON)币种 */
        public static final int USDT_TRON = 3;
    }

    /**
     * 提现状态常量
     */
    public static class Status {
        /** 提现成功 */
        public static final int SUCCESS = 1;
        /** 审核中 */
        public static final int REVIEWING = 2;
        /** 转账中 */
        public static final int TRANSFERRING = 3;
        /** 提现失败 */
        public static final int FAILED = 4;
    }

    /**
     * 来源类型常量
     */
    public static class SourceType {
        /** 正常提现 */
        public static final int NORMAL = 1;
        /** LCT兑换USDT */
        public static final int LCT_TO_USDT = 2;
        /** USDT兑换LCT */
        public static final int USDT_TO_LCT = 3;
        /** 代理返佣提现 */
        public static final int AGENT_COMMISSION = 4;
    }

    /**
     * 区块链类型常量
     */
    public static class ChainType {
        /** BSC链 */
        public static final String BSC = "BSC";
    }

    /**
     * 错误码常量（与PHP版本保持一致）
     */
    public static class ErrorCode {
        /** 未登录或token无效 */
        public static final int UNAUTHORIZED = 401;
        /** 参数验证失败 */
        public static final int VALIDATION_ERROR = 403;
        /** 请求过于频繁 */
        public static final int TOO_MANY_REQUESTS = 429;
        /** 服务器内部错误 */
        public static final int INTERNAL_ERROR = 500;

        /** 余额不足 */
        public static final int INSUFFICIENT_BALANCE = 1005;
        /** 签名验证失败 */
        public static final int SIGNATURE_INVALID = 1011;
        /** 不在提现时间窗口 */
        public static final int OUTSIDE_TIME_WINDOW = 1013;
        /** 可提现百分比不足 */
        public static final int INSUFFICIENT_WITHDRAWAL_PERCENT = 1014;
        /** 用户被禁用 */
        public static final int USER_BANNED = 1018;
        /** 用户提现被禁用 */
        public static final int WITHDRAWAL_BANNED = 1020;
        /** 用户状态异常 */
        public static final int USER_STATUS_ABNORMAL = 1022;
        /** 可提现百分比限制 */
        public static final int WITHDRAWAL_PERCENT_LIMIT = 1025;
        /** 接收地址与用户地址不匹配 */
        public static final int ADDRESS_MISMATCH = 1026;
        /** 超过每日提现次数限制 */
        public static final int DAILY_LIMIT_EXCEEDED = 1029;
    }

    /**
     * 配置键常量
     */
    public static class ConfigKey {
        /** 提现配置 */
        public static final String WITHDRAWAL_CONFIG = "withdrawal";
        /** 价格配置 */
        public static final String PRICE_CONFIG = "price";
    }

    /**
     * Redis锁键前缀
     */
    public static class LockKey {
        /** 提现锁前缀 */
        public static final String WITHDRAWAL_LOCK_PREFIX = "withdrawal:";
        /** 锁超时时间（秒） */
        public static final int LOCK_TIMEOUT = 10;
    }

    /**
     * 手续费类型常量
     */
    public static class FeeType {
        /** 固定金额手续费 */
        public static final int FIXED = 1;
        /** 百分比手续费 */
        public static final int PERCENTAGE = 2;
    }

    /**
     * 默认配置值
     */
    public static class DefaultValue {
        /** 最小提现金额 */
        public static final String MIN_WITHDRAWAL_AMOUNT = "1";  // TODO  10
        /** 默认手续费率 */
        public static final String DEFAULT_FEE_RATE = "0.01";
        /** 默认每日最大提现次数 */
        public static final int DEFAULT_DAILY_MAX_COUNT = 3;
    }

    /**
     * 消息模板
     */
    public static class MessageTemplate {
        /** 签名消息模板 */
        public static final String SIGNATURE_MESSAGE_TEMPLATE = "You are withdrawing, quantity %s. Nonce:%s";
    }
}