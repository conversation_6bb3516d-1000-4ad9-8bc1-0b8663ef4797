package com.lct.finance.model.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 签名验证结果
 * 专注于签名验证的结果，不包含风险评估信息
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Data
public class SignatureVerificationResult {
    /**
     * 验证是否成功
     */
    private boolean success;
    
    /**
     * 失败原因（如果验证失败）
     */
    private String failureReason;
    
    /**
     * 默认构造函数
     */
    public SignatureVerificationResult() {
    }
    
    /**
     * 带参数的构造函数
     */
    public SignatureVerificationResult(boolean success, String failureReason) {
        this.success = success;
        this.failureReason = failureReason;
    }
    
    /**
     * 创建成功结果
     */
    public static SignatureVerificationResult success() {
        SignatureVerificationResult result = new SignatureVerificationResult();
        result.success = true;
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static SignatureVerificationResult failure(String reason) {
        SignatureVerificationResult result = new SignatureVerificationResult();
        result.success = false;
        result.failureReason = reason;
        return result;
    }
    
    /**
     * 获取风险级别（向后兼容，总是返回0）
     * @return 风险级别
     * @deprecated 风险评估功能已移至RiskAssessmentResult类
     */
    @Deprecated
    public int getRiskLevel() {
        return 0;
    }
    
    /**
     * 检查是否需要额外验证（向后兼容，总是返回false）
     * @return 是否需要额外验证
     * @deprecated 风险评估功能已移至RiskAssessmentResult类
     */
    @Deprecated
    public boolean isRequiresAdditionalVerification() {
        return false;
    }
    
    /**
     * 获取风险因素列表（向后兼容，总是返回空列表）
     * @return 风险因素列表
     * @deprecated 风险评估功能已移至RiskAssessmentResult类
     */
    @Deprecated
    public List<String> getRiskFactors() {
        return Collections.emptyList();
    }
} 