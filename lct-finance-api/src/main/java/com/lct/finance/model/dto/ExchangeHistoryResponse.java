package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.lct.finance.model.entity.Exchange;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 兑换历史记录响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExchangeHistoryResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 用户地址
     */
    @JsonProperty("address")
    private String address;

    /**
     * 存入数量
     */
    @JsonProperty("deposit_amount")
    private String depositAmount;

    /**
     * 存入币种类型
     */
    @JsonProperty("deposit_coin_type")
    private Integer depositCoinType;

    /**
     * 存入币种名称
     */
    @JsonProperty("deposit_coin_name")
    private String depositCoinName;

    /**
     * 接收数量
     */
    @JsonProperty("receive_amount")
    private String receiveAmount;

    /**
     * 接收币种类型
     */
    @JsonProperty("receive_coin_type")
    private Integer receiveCoinType;

    /**
     * 接收币种名称
     */
    @JsonProperty("receive_coin_name")
    private String receiveCoinName;

    /**
     * 接收地址
     */
    @JsonProperty("receive_address")
    private String receiveAddress;

    /**
     * 订单状态
     */
    @JsonProperty("status")
    private Integer status;

    /**
     * 状态描述
     */
    @JsonProperty("status_desc")
    private String statusDesc;

    /**
     * 兑换类型 1:USDT兑LCT, 2:LCT兑USDT
     */
    @JsonProperty("exchange_type")
    private Integer exchangeType;

    /**
     * 兑换类型描述
     */
    @JsonProperty("exchange_type_desc")
    private String exchangeTypeDesc;

    /**
     * 操作类型 1:正常(SWFT), 2:系统
     */
    @JsonProperty("action_type")
    private Integer actionType;

    /**
     * 操作类型描述
     */
    @JsonProperty("action_type_desc")
    private String actionTypeDesc;

    /**
     * 付款交易哈希
     */
    @JsonProperty("txhash")
    private String txhash;

    /**
     * 接收交易哈希
     */
    @JsonProperty("receive_txhash")
    private String receiveTxhash;

    /**
     * 汇率
     */
    @JsonProperty("rate")
    private String rate;

    /**
     * 手续费
     */
    @JsonProperty("fee")
    private String fee;

    /**
     * 失败原因
     */
    @JsonProperty("err_reason")
    private String errReason;

    /**
     * 创建时间戳
     */
    @JsonProperty("created_time")
    private Long createdTime;

    /**
     * 创建时间字符串
     */
    @JsonProperty("created_at")
    private String createdAt;

    /**
     * 更新时间字符串
     */
    @JsonProperty("updated_at")
    private String updatedAt;

    /**
     * 是否可以重新支付
     */
    @JsonProperty("can_repay")
    private Boolean canRepay;

    /**
     * 是否可以取消
     */
    @JsonProperty("can_cancel")
    private Boolean canCancel;

    /**
     * 进度百分比
     */
    @JsonProperty("progress")
    private Integer progress;

    /**
     * 预计完成时间
     */
    @JsonProperty("estimated_time")
    private String estimatedTime;

    /**
     * 区块链浏览器链接
     */
    @JsonProperty("explorer_url")
    private String explorerUrl;

    /**
     * 从Exchange实体创建历史记录响应
     */
    public static ExchangeHistoryResponse fromExchange(Exchange exchange) {
        if (exchange == null) {
            return null;
        }

        return ExchangeHistoryResponse.builder()
                .orderId(exchange.getOrderId())
                .address(exchange.getAddress())
                .depositAmount(formatAmount(exchange.getDepositAmount()))
                .depositCoinType(exchange.getDepositCoinType())
                .depositCoinName(getCoinName(exchange.getDepositCoinType()))
                .receiveAmount(formatAmount(exchange.getReceiveAmount()))
                .receiveCoinType(exchange.getReceiveCoinType())
                .receiveCoinName(getCoinName(exchange.getReceiveCoinType()))
                .receiveAddress(exchange.getReceiveAddress())
                .status(exchange.getStatus())
                .statusDesc(getStatusDescription(exchange.getStatus()))
                .exchangeType(exchange.getExchangeType())
                .exchangeTypeDesc(getExchangeTypeDescription(exchange.getExchangeType()))
                .actionType(exchange.getActionType())
                .actionTypeDesc(getActionTypeDescription(exchange.getActionType()))
                .txhash(exchange.getTxhash())
                .receiveTxhash(exchange.getReceiveTxhash())
                .errReason(exchange.getErrReason())
                .createdTime(exchange.getCreatedAt() != null
                        ? exchange.getCreatedAt().toEpochSecond(java.time.ZoneOffset.UTC)
                        : null)
                .createdAt(
                        exchange.getCreatedAt() != null
                                ? exchange.getCreatedAt()
                                        .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                                : null)
                .updatedAt(
                        exchange.getUpdatedAt() != null
                                ? exchange.getUpdatedAt()
                                        .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                                : null)
                .canRepay(canRepay(exchange.getStatus()))
                .canCancel(canCancel(exchange.getStatus()))
                .progress(getProgress(exchange.getStatus()))
                .estimatedTime(getEstimatedTime(exchange.getStatus()))
                .build();
    }

    /**
     * 格式化数量显示
     */
    private static String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0";
        }
        return amount.stripTrailingZeros().toPlainString();
    }

    /**
     * 获取币种名称
     */
    private static String getCoinName(Integer coinType) {
        if (coinType == null)
            return "未知";

        switch (coinType) {
            case 1:
                return "LCT(BSC)";
            case 2:
                return "USDT(BSC)";
            case 3:
                return "USDT(TRON)";
            default:
                return "未知";
        }
    }

    /**
     * 获取状态描述
     */
    private static String getStatusDescription(Integer status) {
        if (status == null)
            return "未知状态";

        switch (status) {
            case 1:
                return "成功";
            case 2:
                return "待支付";
            case 3:
                return "处理中";
            case 4:
                return "失败";
            case 5:
                return "出款中";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取兑换类型描述
     */
    private static String getExchangeTypeDescription(Integer exchangeType) {
        if (exchangeType == null)
            return "未知类型";

        switch (exchangeType) {
            case 1:
                return "USDT兑LCT";
            case 2:
                return "LCT兑USDT";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取操作类型描述
     */
    private static String getActionTypeDescription(Integer actionType) {
        if (actionType == null)
            return "未知操作";

        switch (actionType) {
            case 1:
                return "SWFT跨链桥";
            case 2:
                return "系统内部";
            default:
                return "未知操作";
        }
    }

    /**
     * 判断是否可以重新支付
     */
    private static Boolean canRepay(Integer status) {
        return status != null && (status == 2 || status == 4); // 待支付或失败
    }

    /**
     * 判断是否可以取消
     */
    private static Boolean canCancel(Integer status) {
        return status != null && status == 2; // 仅待支付状态可取消
    }

    /**
     * 获取进度百分比
     */
    private static Integer getProgress(Integer status) {
        if (status == null)
            return 0;

        switch (status) {
            case 1:
                return 100; // 成功
            case 2:
                return 0; // 待支付
            case 3:
                return 50; // 处理中
            case 4:
                return 0; // 失败
            case 5:
                return 80; // 出款中
            default:
                return 0;
        }
    }

    /**
     * 获取预计完成时间
     */
    private static String getEstimatedTime(Integer status) {
        if (status == null)
            return "";

        switch (status) {
            case 1:
                return "已完成";
            case 2:
                return "等待支付";
            case 3:
                return "预计10-30分钟";
            case 4:
                return "已失败";
            case 5:
                return "预计1-3个工作日";
            default:
                return "";
        }
    }

    /**
     * 是否为成功状态
     */
    public boolean isSuccess() {
        return status != null && status == 1;
    }

    /**
     * 是否为待支付状态
     */
    public boolean isPending() {
        return status != null && status == 2;
    }

    /**
     * 是否为处理中状态
     */
    public boolean isProcessing() {
        return status != null && (status == 3 || status == 5);
    }

    /**
     * 是否为失败状态
     */
    public boolean isFailed() {
        return status != null && status == 4;
    }

    /**
     * 是否为SWFT订单
     */
    public boolean isSwftOrder() {
        return actionType != null && actionType == 1;
    }

    /**
     * 是否为系统订单
     */
    public boolean isSystemOrder() {
        return actionType != null && actionType == 2;
    }

    /**
     * 获取完整的状态信息
     */
    public String getFullStatusInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(statusDesc);

        if (errReason != null && !errReason.trim().isEmpty()) {
            sb.append("（").append(errReason).append("）");
        }

        return sb.toString();
    }
}