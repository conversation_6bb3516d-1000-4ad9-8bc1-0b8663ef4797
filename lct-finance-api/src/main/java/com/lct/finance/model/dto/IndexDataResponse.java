package com.lct.finance.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 首页数据响应DTO - 兼容旧PHP接口格式
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndexDataResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 最低销毁数量
     */
    @JsonProperty("min_burn")
    private String minBurn;

    /**
     * 个人收益排行榜
     */
    @JsonProperty("personal_profit_rank")
    private List<ProfitRankingResponse> personalProfitRank;

    /**
     * Banner列表
     */
    @JsonProperty("banners")
    private List<BannerItem> banners;

    /**
     * 公告列表
     */
    @JsonProperty("notices")
    private List<NoticeItem> notices;

    /**
     * 公告弹框
     */
    @JsonProperty("notice_modal")
    private NoticeModalItem noticeModal;

    /**
     * Banner项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BannerItem {
        
        @JsonProperty("picture")
        private String picture;
        
        @JsonProperty("link")
        private String link;
    }

    /**
     * 公告项 - 兼容旧PHP接口格式
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NoticeItem {
        
        @JsonProperty("id")
        private Integer id;
        
        @JsonProperty("title")
        private String title;
        
        @JsonProperty("intro")
        private String intro;
        
        @JsonProperty("picture")
        private String picture;
        
        @JsonProperty("link")
        private String link;
        
        @JsonProperty("created_at")
        private Long createdAt;
    }

    /**
     * 公告弹框项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NoticeModalItem {
        
        @JsonProperty("title")
        private String title;
        
        @JsonProperty("content")
        private String content;
    }
} 