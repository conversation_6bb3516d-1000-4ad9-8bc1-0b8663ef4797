package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 销毁预校验响应
 * 返回校验结果和相关信息
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BurnPreValidateResponse {

    /**
     * 是否可以销毁
     */
    @Builder.Default
    private boolean canBurn = false;

    /**
     * 错误信息（当canBurn=false时）
     */
    private String errorMessage;

    /**
     * 需要的兑换余额数量
     */
    @Builder.Default
    private BigDecimal needExchangeAmount = BigDecimal.ZERO;

    /**
     * 当前可用的兑换余额
     */
    @Builder.Default
    private BigDecimal currentExchangeRemaining = BigDecimal.ZERO;

    /**
     * 最小销毁数量要求
     */
    @Builder.Default
    private BigDecimal minBurnAmount = BigDecimal.ZERO;

    /**
     * 用户的销毁兑换费率
     */
    @Builder.Default
    private BigDecimal burnNeedExchangeRate = BigDecimal.ZERO;

    /**
     * 创建成功响应
     */
    public static BurnPreValidateResponse success() {
        return BurnPreValidateResponse.builder()
                .canBurn(true)
                .build();
    }

    /**
     * 创建成功响应（带详细信息）
     */
    public static BurnPreValidateResponse success(BigDecimal needExchangeAmount,
            BigDecimal currentExchangeRemaining,
            BigDecimal minBurnAmount,
            BigDecimal burnNeedExchangeRate) {
        return BurnPreValidateResponse.builder()
                .canBurn(true)
                .needExchangeAmount(needExchangeAmount)
                .currentExchangeRemaining(currentExchangeRemaining)
                .minBurnAmount(minBurnAmount)
                .burnNeedExchangeRate(burnNeedExchangeRate)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static BurnPreValidateResponse error(String errorMessage) {
        return BurnPreValidateResponse.builder()
                .canBurn(false)
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * 创建失败响应（带详细信息）
     */
    public static BurnPreValidateResponse error(String errorMessage,
            BigDecimal needExchangeAmount,
            BigDecimal currentExchangeRemaining,
            BigDecimal minBurnAmount,
            BigDecimal burnNeedExchangeRate) {
        return BurnPreValidateResponse.builder()
                .canBurn(false)
                .errorMessage(errorMessage)
                .needExchangeAmount(needExchangeAmount)
                .currentExchangeRemaining(currentExchangeRemaining)
                .minBurnAmount(minBurnAmount)
                .burnNeedExchangeRate(burnNeedExchangeRate)
                .build();
    }
}