package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 公告弹框实体类 - 对应notice_modal表
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("notice_modal")
public class NoticeModal {

    /**
     * 公告弹框ID (主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 语言标识 (zh_CN:中文, en_US:英文)
     */
    @TableField("language")
    private String language;

    /**
     * 弹框标题
     */
    @TableField("title")
    private String title;

    /**
     * 弹框内容
     */
    @TableField("content")
    private String content;

    /**
     * 是否显示 (1:显示, 2:隐藏)
     */
    @TableField("`show`")
    private Integer show;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;


}