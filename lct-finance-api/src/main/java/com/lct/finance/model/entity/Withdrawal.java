package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现记录实体类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("withdrawal")
public class Withdrawal {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 币种类型：1-LCT(BSC), 2-USDT(BSC), 3-USDT(TRON)
     */
    @TableField("coin_type")
    private Integer coinType;

    /**
     * 状态：1-成功, 2-审核中, 3-转账中, 4-失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 提现数量
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 实际到账数量
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 实际LCT数量
     */
    @TableField("actual_lct_amount")
    private BigDecimal actualLctAmount;

    /**
     * 手续费
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 汇率
     */
    @TableField("rate")
    private BigDecimal rate;

    /**
     * 接收地址
     */
    @TableField("receive_address")
    private String receiveAddress;

    /**
     * 交易哈希
     */
    @TableField("txhash")
    private String txhash;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * IP地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 来源类型：1-正常提现, 2-LCT兑U
     */
    @TableField("source_type")
    private Integer sourceType;

    /**
     * USDT对LCT价格
     */
    @TableField("price_usdt_lct")
    private BigDecimal priceUsdtLct;

    /**
     * LCT对USDT价格
     */
    @TableField("price_lct_usdt")
    private BigDecimal priceLctUsdt;

    /**
     * SWFT桥接订单ID
     */
    @TableField("swft_bridgers_order_id")
    private String swftBridgersOrderId;

    /**
     * 最终到账数量
     */
    @TableField("final_amount")
    private BigDecimal finalAmount;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;


}