package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 新用户礼包实体类 - 对应数据库new_user_gift表
 * 
 * <AUTHOR> Finance Team
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lct_new_user_gift")
public class NewUserGift {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 礼品类型 (1:LCT, 2:USDT)
     */
    @TableField("gift_type")
    private Integer giftType;

    /**
     * 礼品数量
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 状态 (0:未领取, 1:已领取)
     */
    @TableField("status")
    private Integer status;

    /**
     * 领取时间
     */
    @TableField("claimed_at")
    private LocalDateTime claimedAt;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 检查是否已领取
     * 
     * @return true-已领取, false-未领取
     */
    public boolean isclaimed() {
        return this.status != null && this.status == 1;
    }

    /**
     * 设置为已领取
     */
    public void markAsClaimed() {
        this.status = 1;
        this.claimedAt = LocalDateTime.now();
    }


}