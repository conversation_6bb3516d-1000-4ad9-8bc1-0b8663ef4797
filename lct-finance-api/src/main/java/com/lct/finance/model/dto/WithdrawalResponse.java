package com.lct.finance.model.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现响应DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawalResponse {

    /**
     * 提现订单号
     */
    private String orderNo;

    /**
     * 提现状态：1-成功, 2-审核中, 3-转账中, 4-失败
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 提现数量
     */
    private BigDecimal amount;

    /**
     * 实际到账数量
     */
    private BigDecimal actualAmount;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 币种类型：1-LCT(BSC), 2-USDT(BSC), 3-USDT(TRON)
     */
    private Integer coinType;

    /**
     * 币种名称
     */
    private String coinName;

    /**
     * 接收地址
     */
    private String receiveAddress;

    /**
     * 交易哈希（成功后返回）
     */
    private String txHash;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 预计到账时间描述
     */
    private String estimatedArrivalTime;

    /**
     * 创建成功响应
     */
    public static WithdrawalResponse success(String orderNo, Integer status, BigDecimal amount,
            BigDecimal actualAmount, BigDecimal fee, Integer coinType,
            String receiveAddress) {
        return WithdrawalResponse.builder()
                .orderNo(orderNo)
                .status(status)
                .statusDesc(getStatusDescription(status))
                .amount(amount)
                .actualAmount(actualAmount)
                .fee(fee)
                .coinType(coinType)
                .coinName(getCoinName(coinType))
                .receiveAddress(receiveAddress)
                .createdAt(LocalDateTime.now())
                .estimatedArrivalTime("预计1-3个工作日内到账")
                .build();
    }

    /**
     * 获取状态描述
     */
    private static String getStatusDescription(Integer status) {
        switch (status) {
            case 1:
                return "提现成功";
            case 2:
                return "审核中";
            case 3:
                return "转账中";
            case 4:
                return "提现失败";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取币种名称
     */
    private static String getCoinName(Integer coinType) {
        switch (coinType) {
            case 1:
                return "LCT(BSC)";
            case 2:
                return "USDT(BSC)";
            case 3:
                return "USDT(TRON)";
            default:
                return "未知币种";
        }
    }
}