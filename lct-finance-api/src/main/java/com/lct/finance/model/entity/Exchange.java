package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.lct.finance.model.constants.CommonConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 兑换订单实体类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("lct_exchanges")
public class Exchange implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * SWFT订单号(当LCT兑换USDT时，订单号为提现表订单号)
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 状态 1:成功,2:待支付,3:处理中,4:失败,5:出款中
     */
    @TableField("status")
    private Integer status;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户地址
     */
    @TableField("address")
    private String address;

    /**
     * 转出数量
     */
    @TableField("deposit_amount")
    private BigDecimal depositAmount;

    /**
     * 转出币种类型
     */
    @TableField("deposit_coin_type")
    private Integer depositCoinType;

    /**
     * 接收数量
     */
    @TableField("receive_amount")
    private BigDecimal receiveAmount;

    /**
     * 接收币种类型
     */
    @TableField("receive_coin_type")
    private Integer receiveCoinType;

    /**
     * 接收地址
     */
    @TableField("receive_address")
    private String receiveAddress;

    /**
     * 付款交易哈希
     */
    @TableField("txhash")
    private String txhash;

    /**
     * LCT收款交易哈希(给用户转LCT的hash)
     */
    @TableField("receive_txhash")
    private String receiveTxhash;

    /**
     * USDT收款地址(系统地址)
     */
    @TableField("usdt_receive_address")
    private String usdtReceiveAddress;

    /**
     * 操作类型 1:正常(直接U兑LCT全程通过SWFT),2:系统(U兑LCT变为U兑U走SWFT,LCT通过系统转给用户)
     */
    @TableField("action_type")
    private Integer actionType;

    /**
     * 兑换类型 1:U兑LCT,2:LCT兑U
     */
    @TableField("exchange_type")
    private Integer exchangeType;

    /**
     * 客户端IP地址
     */
    @TableField("ip")
    private String ip;

    /**
     * 失败原因
     */
    @TableField("err_reason")
    private String errReason;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;


}