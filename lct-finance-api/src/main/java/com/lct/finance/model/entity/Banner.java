package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 轮播图实体类 - 对应banners表
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("banners")
public class Banner {
    private static final long serialVersionUID = 1L;

    /**
     * 轮播图ID (主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 排序ID
     */
    @TableField("sort_id")
    private Integer sortId;

    /**
     * 语言标识 (zh_CN:中文, en_US:英文)
     */
    @TableField("language")
    private String language;

    /**
     * 轮播图标题
     */
    @TableField("title")
    private String title;

    /**
     * 图片路径
     */
    @TableField("picture")
    private String picture;

    /**
     * 点击跳转链接
     */
    @TableField("link")
    private String link;

    /**
     * 是否显示 (1:显示, 2:隐藏)
     */
    @TableField("`show`")
    private Integer show;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;



} 