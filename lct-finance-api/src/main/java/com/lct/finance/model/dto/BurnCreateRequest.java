package com.lct.finance.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 销毁订单创建请求DTO
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BurnCreateRequest {

    /**
     * 用户钱包地址
     * 支持BSC(0x开头42位)格式
     */
    @NotBlank(message = "用户地址不能为空")
    @Pattern(regexp = "^0x[a-fA-F0-9]{40}$", message = "地址格式错误，请输入有效的BSC地址")
    private String address;

    /**
     * 交易哈希
     * 必须是64位十六进制字符串
     */
    @NotBlank(message = "交易哈希不能为空")
    @Pattern(regexp = "^0x[a-fA-F0-9]{64}$", message = "交易哈希格式错误，必须是0x开头的64位十六进制字符串")
    private String txhash;

    /**
     * 销毁数量
     * 必须大于等于最低销毁数量
     */
    @NotNull(message = "销毁数量不能为空")
    @DecimalMin(value = "0.01", message = "销毁数量必须大于0.01")
    private BigDecimal amount;

    /**
     * 销毁类型（可选）
     * 1: 有LCT，2: 无LCT
     * 默认为1
     */
    @Builder.Default
    private Integer type = 1;

    /**
     * 客户端IP地址（由服务端自动获取，客户端无需传递）
     */
    private String clientIp;

//    /**
//     * nonce值
//     * 用于防止重放攻击
//     */
//    @NotBlank(message = "nonce不能为空")
//    private String nonce;
//
//    /**
//     * 签名消息
//     */
//    @NotBlank(message = "签名消息不能为空")
//    private String message;
//
//    /**
//     * 签名
//     */
//    @NotBlank(message = "签名不能为空")
//    private String sign;

    /**
     * 设备指纹
     * 用于设备识别和风险评估
     */
    private String deviceFingerprint;
}