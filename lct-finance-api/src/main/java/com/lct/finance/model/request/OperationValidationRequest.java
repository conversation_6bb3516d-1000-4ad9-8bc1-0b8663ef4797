package com.lct.finance.model.request;

import lombok.Builder;
import lombok.Data;

/**
 * 操作验证请求
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Builder
public class OperationValidationRequest {

    /**
     * 用户地址
     */
    private String address;

    /**
     * 操作类型（LOGIN, BURN, WITHDRAWAL等）
     */
    private String operation;

    /**
     * 签名消息
     */
    private String message;

    /**
     * 签名
     */
    private String signature;

    /**
     * nonce值
     */
    private String nonce;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 设备指纹
     */
    private String deviceFingerprint;

    /**
     * 操作金额（可选）
     */
    private String amount;

    /**
     * 链标识（可选）
     */
    private String chain;

    /**
     * 是否执行风险评估
     */
    private boolean performRiskAssessment;

    /**
     * 是否记录操作日志
     */
    private boolean recordOperationLog;
}