package com.lct.finance.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户登录日志实体类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Data
@TableName("lct_user_login_log")
public class UserLoginLog {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户地址
     */
    private String userAddress;

    /**
     * 链类型
     */
    private String chain;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * IP地理位置
     */
    private String ipLocation;

    /**
     * 设备指纹
     */
    private String deviceFingerprint;

    /**
     * 风险评分
     */
    private Integer riskLevel;

    /**
     * 风险评分详情 (JSON格式)
     * 记录风险评估的具体计算过程和各项得分
     */
    private String riskScoreDetail;

    /**
     * 登录状态 (1-成功, 0-失败)
     */
    private Integer status;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}