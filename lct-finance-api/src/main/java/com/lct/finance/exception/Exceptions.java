package com.lct.finance.exception;

import com.lct.finance.model.enums.ErrorCode;

/**
 * 异常工具类
 * 提供便捷方法创建各种类型的BusinessException
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public class Exceptions {

    /**
     * 创建一个默认的业务异常（使用INTERNAL_ERROR错误码）
     * 
     * @param message 错误消息
     * @return 业务异常
     */
    public static BusinessException business(String message) {
        return new BusinessException(message);
    }

    /**
     * 创建一个通用业务异常
     * 
     * @param errorCode 错误码
     * @return 业务异常
     */
    public static BusinessException business(ErrorCode errorCode) {
        return new BusinessException(errorCode);
    }

    /**
     * 创建一个通用业务异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @return 业务异常
     */
    public static BusinessException business(ErrorCode errorCode, String message) {
        return new BusinessException(errorCode, message);
    }

    /**
     * 创建一个通用业务异常
     * 
     * @param errorCode 错误码
     * @param cause 原因异常
     * @return 业务异常
     */
    public static BusinessException business(ErrorCode errorCode, Throwable cause) {
        return new BusinessException(errorCode, cause);
    }

    /**
     * 创建一个通用业务异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @param cause 原因异常
     * @return 业务异常
     */
    public static BusinessException business(ErrorCode errorCode, String message, Throwable cause) {
        return new BusinessException(errorCode, message, cause);
    }

    /**
     * 创建一个通用业务异常
     * 
     * @param errorCode 错误码
     * @param errorData 错误数据
     * @return 业务异常
     */
    public static BusinessException business(ErrorCode errorCode, Object errorData) {
        return new BusinessException(errorCode, errorData);
    }

    /**
     * 创建一个通用业务异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @param errorData 错误数据
     * @return 业务异常
     */
    public static BusinessException business(ErrorCode errorCode, String message, Object errorData) {
        return new BusinessException(errorCode, message, errorData);
    }

    /**
     * 创建一个用户相关异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @return 业务异常
     */
    public static BusinessException user(ErrorCode errorCode, String message) {
        if (errorCode.getCode() < 2000 || errorCode.getCode() >= 3000) {
            throw new IllegalArgumentException("错误码必须在用户相关错误范围内(2000-2999)");
        }
        return business(errorCode, message);
    }

    /**
     * 创建一个认证相关异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @return 业务异常
     */
    public static BusinessException auth(ErrorCode errorCode, String message) {
        if (errorCode.getCode() < 3000 || errorCode.getCode() >= 4000) {
            throw new IllegalArgumentException("错误码必须在认证相关错误范围内(3000-3999)");
        }
        return business(errorCode, message);
    }

    /**
     * 创建一个兑换相关异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @return 业务异常
     */
    public static BusinessException exchange(ErrorCode errorCode, String message) {
        if (errorCode.getCode() < 4000 || errorCode.getCode() >= 5000) {
            throw new IllegalArgumentException("错误码必须在兑换相关错误范围内(4000-4999)");
        }
        return business(errorCode, message);
    }

    /**
     * 创建一个提现相关异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @return 业务异常
     */
    public static BusinessException withdrawal(ErrorCode errorCode, String message) {
        if (errorCode.getCode() < 5000 || errorCode.getCode() >= 6000) {
            throw new IllegalArgumentException("错误码必须在提现相关错误范围内(5000-5999)");
        }
        return business(errorCode, message);
    }

    /**
     * 创建一个礼包相关异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @return 业务异常
     */
    public static BusinessException gift(ErrorCode errorCode, String message) {
        if (errorCode.getCode() < 6000 || errorCode.getCode() >= 7000) {
            throw new IllegalArgumentException("错误码必须在礼包相关错误范围内(6000-6999)");
        }
        return business(errorCode, message);
    }

    /**
     * 创建一个安全相关异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @return 业务异常
     */
    public static BusinessException security(ErrorCode errorCode, String message) {
        if (errorCode.getCode() < 7000 || errorCode.getCode() >= 8000) {
            throw new IllegalArgumentException("错误码必须在安全相关错误范围内(7000-7999)");
        }
        return business(errorCode, message);
    }

    /**
     * 创建一个限流相关异常
     * 
     * @param errorCode 错误码
     * @param message 自定义错误消息
     * @return 业务异常
     */
    public static BusinessException rateLimit(ErrorCode errorCode, String message) {
        if (errorCode.getCode() < 8000 || errorCode.getCode() >= 9000) {
            throw new IllegalArgumentException("错误码必须在限流相关错误范围内(8000-8999)");
        }
        return business(errorCode, message);
    }

    /**
     * 从旧的异常转换为BusinessException
     * 
     * @param e 旧异常
     * @param legacyErrorCode 旧错误码
     * @return 业务异常
     */
    public static BusinessException fromLegacy(RuntimeException e, String legacyErrorCode) {
        ErrorCode errorCode = ErrorCode.mapLegacyStringCode(legacyErrorCode);
        return new BusinessException(errorCode, e.getMessage());
    }

    /**
     * 从旧的异常转换为BusinessException
     * 
     * @param e 旧异常
     * @param legacyCode 旧错误码
     * @return 业务异常
     */
    public static BusinessException fromLegacy(RuntimeException e, int legacyCode) {
        ErrorCode errorCode = ErrorCode.mapLegacyCode(legacyCode);
        return new BusinessException(errorCode, e.getMessage());
    }
} 