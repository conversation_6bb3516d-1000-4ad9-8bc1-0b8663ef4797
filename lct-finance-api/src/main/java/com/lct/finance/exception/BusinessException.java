package com.lct.finance.exception;

import com.lct.finance.model.enums.ErrorCode;

/**
 * 统一业务异常类
 * 所有业务异常都使用此类，通过ErrorCode区分不同类型
 */
public class BusinessException extends RuntimeException {

    private final ErrorCode errorCode;
    private final Object errorData;
    private final String suggestion;

    /**
     * 构造函数 - 仅错误消息，使用INTERNAL_ERROR作为默认错误码
     * 
     * @param message 错误消息
     */
    public BusinessException(String message) {
        super(message);
        this.errorCode = ErrorCode.INTERNAL_ERROR;
        this.errorData = null;
        this.suggestion = null;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     */
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.errorData = null;
        this.suggestion = null;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message   自定义错误消息
     */
    public BusinessException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorData = null;
        this.suggestion = null;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param cause     原因异常
     */
    public BusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.errorData = null;
        this.suggestion = null;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message   自定义错误消息
     * @param cause     原因异常
     */
    public BusinessException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorData = null;
        this.suggestion = null;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param errorData 错误数据
     */
    public BusinessException(ErrorCode errorCode, Object errorData) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.errorData = errorData;
        this.suggestion = null;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message   自定义错误消息
     * @param errorData 错误数据
     */
    public BusinessException(ErrorCode errorCode, String message, Object errorData) {
        super(message);
        this.errorCode = errorCode;
        this.errorData = errorData;
        this.suggestion = null;
    }

    /**
     * 构造函数 - 带建议
     * 
     * @param errorCode  错误码
     * @param message    自定义错误消息
     * @param suggestion 错误处理建议
     */
    public BusinessException(ErrorCode errorCode, String message, String suggestion) {
        super(message);
        this.errorCode = errorCode;
        this.errorData = null;
        this.suggestion = suggestion;
    }

    /**
     * 构造函数 - 完整版本
     * 
     * @param errorCode  错误码
     * @param message    自定义错误消息
     * @param errorData  错误数据
     * @param suggestion 错误处理建议
     */
    public BusinessException(ErrorCode errorCode, String message, Object errorData, String suggestion) {
        super(message);
        this.errorCode = errorCode;
        this.errorData = errorData;
        this.suggestion = suggestion;
    }

    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public ErrorCode getErrorCode() {
        return errorCode;
    }

    /**
     * 获取错误码值
     * 
     * @return 错误码值
     */
    public int getCode() {
        return errorCode.getCode();
    }

    /**
     * 获取HTTP状态码
     * 
     * @return HTTP状态码
     */
    public int getHttpStatus() {
        return errorCode.getHttpStatus();
    }

    /**
     * 获取错误数据
     * 
     * @return 错误数据
     */
    public Object getErrorData() {
        return errorData;
    }

    /**
     * 获取错误处理建议
     * 
     * @return 错误处理建议
     */
    public String getSuggestion() {
        return suggestion;
    }

    /**
     * 获取错误类型
     * 
     * @return 错误类型
     */
    public String getErrorType() {
        return getBusinessDomain() + "_ERROR";
    }

    /**
     * 获取业务领域
     * 
     * @return 业务领域
     */
    public String getBusinessDomain() {
        int code = errorCode.getCode();
        if (code >= 1000 && code < 2000)
            return "COMMON";
        if (code >= 2000 && code < 3000)
            return "USER";
        if (code >= 3000 && code < 4000)
            return "AUTH";
        if (code >= 4000 && code < 5000)
            return "EXCHANGE";
        if (code >= 5000 && code < 6000)
            return "WITHDRAWAL";
        if (code >= 6000 && code < 7000)
            return "GIFT";
        if (code >= 7000 && code < 8000)
            return "SECURITY";
        if (code >= 8000 && code < 9000)
            return "RATE_LIMIT";
        return "UNKNOWN";
    }

    /**
     * 获取默认建议
     * 
     * @return 默认建议
     */
    public String getDefaultSuggestion() {
        if (suggestion != null && !suggestion.isEmpty()) {
            return suggestion;
        }

        // 根据错误类型提供默认建议
        switch (getBusinessDomain()) {
            case "AUTH":
                return "请重新登录或检查您的身份验证信息";
            case "USER":
                return "请检查用户信息或联系客服";
            case "EXCHANGE":
                return "请检查兑换参数或稍后重试";
            case "WITHDRAWAL":
                return "请检查提现条件或联系客服";
            case "GIFT":
                return "请确认礼包领取条件";
            case "SECURITY":
                return "请完成安全验证或联系客服";
            case "RATE_LIMIT":
                return "请求过于频繁，请稍后再试";
            case "COMMON":
            default:
                return "请检查操作或稍后重试";
        }
    }

    /**
     * 从旧式异常转换
     * 
     * @param e          原始异常
     * @param legacyCode 旧错误码
     * @return 业务异常
     */
    public static BusinessException fromLegacyException(RuntimeException e, int legacyCode) {
        ErrorCode errorCode = ErrorCode.mapLegacyCode(legacyCode);
        return new BusinessException(errorCode, e.getMessage());
    }

    /**
     * 从旧式异常转换
     * 
     * @param e               原始异常
     * @param legacyErrorCode 旧错误码字符串
     * @return 业务异常
     */
    public static BusinessException fromLegacyException(RuntimeException e, String legacyErrorCode) {
        ErrorCode errorCode = ErrorCode.mapLegacyStringCode(legacyErrorCode);
        return new BusinessException(errorCode, e.getMessage());
    }

    @Override
    public String toString() {
        return String.format("BusinessException[code=%d, domain=%s, message=%s, httpStatus=%d, suggestion=%s]",
                getCode(), getBusinessDomain(), getMessage(), getHttpStatus(), getDefaultSuggestion());
    }
}