package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.dto.*;
import com.lct.finance.model.entity.Burn;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 销毁服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IBurnService extends IService<Burn> {

    /**
     * 创建销毁订单
     * 
     * @param request  销毁请求
     * @param userId   用户ID
     * @param clientIp 客户端IP
     */
    void createBurn(BurnCreateRequest request, Long userId, String clientIp);

    /**
     * 获取销毁要求
     * 
     * @param userId 用户ID
     * @return 销毁要求信息
     */
    BurnRequirementsResponse getBurnRequirements(Long userId);

    /**
     * 销毁预校验
     * 在执行区块链交易前进行所有必要的校验，确保数据一致性
     * 
     * @param request 预校验请求
     * @param userId  用户ID
     * @return 预校验结果
     */
    BurnPreValidateResponse preValidateBurn(BurnPreValidateRequest request, Long userId);

    /**
     * 验证交易哈希格式
     * 
     * @param txhash 交易哈希
     * @return 是否有效
     */
    boolean validateTxHash(String txhash);

    /**
     * 检查交易哈希是否重复
     * 
     * @param txhash 交易哈希
     * @return 是否重复
     */
    boolean checkDuplicate(String txhash);

    /**
     * 获取用户销毁记录历史
     * 
     * @param address  用户地址
     * @param date     日期筛选（可选）
     * @param page     页码
     * @param pageSize 每页大小
     * @return 销毁记录历史
     */
    BurnHistoryResponse getBurnHistory(String address, String date, Integer page, Integer pageSize);

    /**
     * 获取用户销毁收益记录
     * 
     * @param address  用户地址
     * @param date     日期筛选（可选）
     * @param page     页码
     * @param pageSize 每页大小
     * @return 销毁收益记录
     */
    BurnProfitResponse getBurnProfits(String address, String date, Integer page, Integer pageSize);

    /**
     * 获取用户团队收益记录
     * 
     * @param address  用户地址
     * @param date     日期筛选（可选）
     * @param page     页码
     * @param pageSize 每页大小
     * @return 团队收益记录
     */
    TeamProfitResponse getTeamProfits(String address, String date, Integer page, Integer pageSize);

    /**
     * 获取用户总销毁数量
     * 
     * @param userId 用户ID
     * @return 总销毁数量
     */
    BigDecimal getTotalAmount(Long userId);

    /**
     * 获取用户总销毁数量（支持时间限制）
     * 参考PHP代码中的特殊用户时间限制逻辑
     * 
     * @param userId  用户ID
     * @param endTime 时间限制，格式：yyyy-MM-dd HH:mm:ss，为null表示无限制
     * @return 总销毁数量
     */
    BigDecimal getTotalAmountWithTimeLimit(Long userId, String endTime);

    /**
     * 获取多个用户的总销毁数量
     * 
     * @param userIds   用户ID列表
     * @param startDate 开始日期（可选）
     * @param endDate   结束日期（可选）
     * @return 总销毁数量
     */
    BigDecimal getTotalAmountByUserIds(List<Long> userIds, String startDate, String endDate);

    /**
     * 获取多个用户今日销毁数量
     * 
     * @param userIds 用户ID列表
     * @param date    日期（YYYY-MM-DD格式）
     * @return 今日销毁总数量
     */
    BigDecimal getTodayAmountByUserIds(List<Long> userIds, String date);

    /**
     * 计算用户出局状态
     * 
     * @param userId 用户ID
     * @return 出局计算结果 [0: 是否出局(0否1是), 1: 剩余收益出局数量]
     */
    Object[] calculateOutStatus(Long userId);

    /**
     * 获取用户预估总收益
     * 
     * @param userId 用户ID
     * @return 预估总收益
     */
    BigDecimal getEstimateTotalProfit(Long userId);

    /**
     * 获取用户已获得总收益
     * 
     * @param userId 用户ID
     * @return 已获得总收益
     */
    BigDecimal getUserTotalProfit(Long userId);

    /**
     * 批量获取多个用户的销毁数量映射
     * 优化direct_invites接口性能，避免N+1查询问题
     * 
     * @param userIds 用户ID列表
     * @return 用户ID到销毁数量的映射
     */
    Map<Long, BigDecimal> getBatchTotalAmountMap(List<Long> userIds);

    /**
     * 批量获取多个用户的销毁数量映射（支持时间限制）
     * 优化direct_invites接口性能，避免N+1查询问题，支持特殊用户时间限制
     * 
     * @param userIds 用户ID列表
     * @param endTime 时间限制，格式：yyyy-MM-dd HH:mm:ss，为null表示无限制
     * @return 用户ID到销毁数量的映射
     */
    Map<Long, BigDecimal> getBatchTotalAmountMapWithTimeLimit(List<Long> userIds, String endTime);

    /**
     * 更新用户等级和临时族谱
     * 根据用户的销毁量更新用户等级和临时族谱关系
     * 
     * @param userId 用户ID
     * @return 是否更新成功
     */
    boolean updateUserLevelAndTemporaryPids(Long userId);

    /**
     * 销毁提交（包含完整验证流程）
     * 
     * @param request  销毁请求
     * @param clientIp 客户端IP
     * @return 提交结果
     */
    ApiResponse<String> burn(BurnCreateRequest request, String clientIp);
}