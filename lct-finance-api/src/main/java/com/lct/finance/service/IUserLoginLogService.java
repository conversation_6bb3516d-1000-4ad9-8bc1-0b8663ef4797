package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.UserLoginLog;

/**
 * 用户登录日志服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
public interface IUserLoginLogService extends IService<UserLoginLog> {

    /**
     * 获取用户最近一次登录记录
     * 
     * @param userAddress 用户地址
     * @return 登录记录
     */
    UserLoginLog getLastLoginByAddress(String userAddress);
    
    /**
     * 获取用户最后登录的IP地址
     * 
     * @param userAddress 用户地址
     * @return IP地址
     */
    String getLastLoginIp(String userAddress);
    
    /**
     * 获取用户最后登录的位置信息
     * 
     * @param userAddress 用户地址
     * @return 位置信息
     */
    String getLastLoginLocation(String userAddress);
    
    /**
     * 获取用户最后使用的设备指纹
     * 
     * @param userAddress 用户地址
     * @return 设备指纹
     */
    String getLastDeviceFingerprint(String userAddress);
    
    /**
     * 记录用户登录日志
     * 
     * @param address 用户地址
     * @param chainType 链类型
     * @param clientIp 客户端IP
     * @param deviceFingerprint 设备指纹
     * @param riskLevel 风险级别
     * @param status 状态（0失败，1成功）
     */
    void recordLoginLog(String address, String chainType, String clientIp, 
                        String deviceFingerprint, int riskLevel, int status);
    

} 