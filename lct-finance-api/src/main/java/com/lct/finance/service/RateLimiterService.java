package com.lct.finance.service;

/**
 * 限流器服务接口
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-20
 */
public interface RateLimiterService {
    
    /**
     * 尝试获取访问令牌
     *
     * @param key   限流的key
     * @param count 桶容量（最大请求数）
     * @param time  过期时间(秒)
     * @return 是否获取到令牌，true表示允许访问，false表示被限流
     */
    boolean tryAcquire(String key, int count, int time);
    
    /**
     * 尝试获取访问令牌（指定漏水速率）
     *
     * @param key   限流的key
     * @param count 桶容量（最大请求数）
     * @param rate  漏水速率（每秒处理多少请求）
     * @param time  过期时间(秒)
     * @return 是否获取到令牌，true表示允许访问，false表示被限流
     */
    boolean tryAcquireWithRate(String key, int count, int rate, int time);
} 