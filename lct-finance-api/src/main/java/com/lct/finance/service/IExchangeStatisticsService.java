package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.ExchangeStatistics;

import java.math.BigDecimal;

/**
 * 兑换统计服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-01
 */
public interface IExchangeStatisticsService extends IService<ExchangeStatistics> {

    /**
     * 获取用户指定类型的兑换统计数据
     * 
     * @param userId 用户ID
     * @param exchangeType 兑换类型 (1: USDT兑LCT, 2: LCT兑USDT)
     * @return 兑换统计数据，如果不存在则返回null
     */
    ExchangeStatistics getUserExchangeStat(Long userId, Integer exchangeType);



    /**
     * 扣除销毁兑换余额
     * 使用原子操作确保数据一致性
     * 
     * @param statId 统计记录ID
     * @param amount 扣除金额
     * @return 是否扣除成功
     */
    boolean deductBurnExchangeRemaining(Long statId, BigDecimal amount);



    /**
     * 获取用户的销毁兑换余额
     * 
     * @param userId 用户ID
     * @param exchangeType 兑换类型
     * @return 可用余额，如果不存在返回0
     */
    BigDecimal getBurnExchangeRemaining(Long userId, Integer exchangeType);


}