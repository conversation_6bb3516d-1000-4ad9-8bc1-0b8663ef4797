package com.lct.finance.service;

import com.lct.finance.model.dto.SwftAccountExchangeRequest;
import com.lct.finance.model.dto.SwftAccountExchangeResponse;
import com.lct.finance.model.dto.SwftBaseInfoResponse;

/**
 * SWFT跨链兑换服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface ISwftService {

    /**
     * 获取兑换汇率基础信息
     * 
     * @param depositCoinCode 存入币种代码
     * @param receiveCoinCode 接收币种代码
     * @param depositCoinAmt  存入数量
     * @return 汇率基础信息
     */
    SwftBaseInfoResponse getBaseInfo(String depositCoinCode, String receiveCoinCode, String depositCoinAmt);

    /**
     * 创建账户兑换订单
     * 
     * @param request 兑换请求参数
     * @return 兑换订单响应
     */
    SwftAccountExchangeResponse accountExchange(SwftAccountExchangeRequest request);
}