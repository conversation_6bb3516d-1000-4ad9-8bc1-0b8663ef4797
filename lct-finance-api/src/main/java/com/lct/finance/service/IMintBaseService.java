package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.MintBase;

import java.math.BigDecimal;

/**
 * 铸造基础数据服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IMintBaseService extends IService<MintBase> {

    /**
     * 获取用户预估总收益（profit_max_amount总和）
     * 
     * @param userId 用户ID
     * @return 预估总收益
     */
    BigDecimal getEstimateTotalProfit(Long userId);
}