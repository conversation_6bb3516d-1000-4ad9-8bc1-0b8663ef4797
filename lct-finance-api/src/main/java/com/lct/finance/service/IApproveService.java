package com.lct.finance.service;

import com.lct.finance.model.dto.UpdateApproveRequest;
import com.lct.finance.model.dto.UpdateApproveResponse;
import com.lct.finance.model.dto.ApiResponse;

/**
 * 授权服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IApproveService {

    /**
     * 更新用户授权信息
     * 
     * @param request  授权更新请求
     * @param clientIp 客户端IP
     * @return 授权更新响应
     */
    UpdateApproveResponse updateApprove(UpdateApproveRequest request, String clientIp);

    /**
     * 更新用户授权信息（包含完整验证流程）
     * 
     * @param request  授权更新请求
     * @param clientIp 客户端IP
     * @return 授权更新结果
     */
    ApiResponse<UpdateApproveResponse> updateApproveWithValidation(UpdateApproveRequest request, String clientIp);

}