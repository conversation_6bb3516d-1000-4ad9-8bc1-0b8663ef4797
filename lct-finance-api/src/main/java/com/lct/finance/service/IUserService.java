package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.dto.*;
import com.lct.finance.model.entity.User;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IUserService extends IService<User> {

    /**
     * 根据地址查找用户
     */
    User findByAddress(String address);

    /**
     * 查找或创建用户
     */
    User findOrCreateUser(UserLoginRequest request, String clientIp);

    /**
     * 更新用户最后登录信息
     */
    void updateLastLogin(Long userId, String clientIp);

    /**
     * 根据用户ID查找用户
     */
    User findById(Long userId);

    /**
     * 保存用户
     */
    User saveUser(User user);

    /**
     * 生成邀请码
     */
    String generateInviteCode();

    /**
     * 验证邀请码
     */
    User validateInviteCode(String inviteCode);

    /**
     * 获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息响应
     */
    UserInfoResponse getUserInfo(Long userId);

    /**
     * 获取用户邀请信息
     * 
     * @param userId 用户ID
     * @return 邀请信息响应
     */
    InvitationResponse getInvitationInfo(Long userId);

    /**
     * 获取直推邀请列表
     * 
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 直推邀请列表
     */
    PageResponse<DirectInviteResponse> getDirectInvites(Long userId, Integer page, Integer size);

    /**
     * 获取所有邀请列表（团队）
     * 
     * @param userId 用户ID
     * @param page   页码
     * @param size   每页大小
     * @return 邀请列表
     */
    PageResponse<InviteListResponse> getInvites(Long userId, Integer page, Integer size);

    /**
     * 处理用户登录业务逻辑
     * 
     * @param request            登录请求
     * @param clientIp           客户端IP
     * @param verificationResult 签名验证结果
     * @return 登录响应
     */
    UserLoginResponse handleLogin(UserLoginRequest request, String clientIp,
            SignatureVerificationResult verificationResult);

    /**
     * 用户登录（包含完整验证流程）
     * 
     * @param request  登录请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    ApiResponse<UserLoginResponse> login(UserLoginRequest request, String clientIp);

    /**
     * 检查用户是否被封禁
     * 
     * @param user 用户对象
     * @return true=被封禁, false=正常
     */
    boolean isBanned(User user);

    /**
     * 检查用户是否被禁止提现
     * 
     * @param user 用户对象
     * @return true=禁止提现, false=允许提现
     */
    boolean isBannedWithdrawal(User user);
}