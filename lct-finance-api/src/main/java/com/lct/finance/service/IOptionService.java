package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.Option;
import java.util.Map;

/**
 * 系统配置服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IOptionService extends IService<Option> {

    /**
     * 根据键获取配置值
     * 
     * @param key 配置键
     * @return 配置值
     */
    String getOption(String key);

    /**
     * 根据键获取配置值，如果不存在则返回默认值
     * 
     * @param key          配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getOption(String key, String defaultValue);

    /**
     * 获取销毁配置
     * 
     * @return 销毁配置Map
     */
    Map<String, Object> getBurnConfig();

    /**
     * 获取最低销毁数量
     * 
     * @return 最低销毁数量
     */
    String getMinBurnAmount();

}