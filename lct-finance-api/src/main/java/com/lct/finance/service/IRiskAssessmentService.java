package com.lct.finance.service;

import com.lct.finance.model.dto.RiskAssessmentRequest;
import com.lct.finance.model.dto.RiskAssessmentResult;

/**
 * 风险评估服务接口
 * 负责评估用户操作风险
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
public interface IRiskAssessmentService {
    
    /**
     * 评估操作风险
     * 
     * @param request 风险评估请求
     * @return 风险评估结果
     */
    RiskAssessmentResult evaluateRisk(RiskAssessmentRequest request);
    
    /**
     * 记录用户登录日志
     * 
     * @param address 用户地址
     * @param chainType 链类型
     * @param clientIp 客户端IP
     * @param deviceFingerprint 设备指纹
     * @param riskLevel 风险级别
     * @param status 状态（0失败，1成功）
     */
    void recordLoginLog(String address, String chainType, String clientIp, String deviceFingerprint, int riskLevel, int status);
    
    /**
     * 记录用户操作日志
     * 
     * @param address 用户地址
     * @param operation 操作类型
     * @param amount 操作金额
     * @param clientIp 客户端IP
     * @param riskLevel 风险级别
     * @param status 状态（0失败，1成功）
     */
    void recordOperationLog(String address, String operation, String amount, String clientIp, int riskLevel, int status);
} 