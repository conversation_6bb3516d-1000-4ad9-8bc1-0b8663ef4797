package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.Banner;

import java.util.List;

/**
 * 轮播图服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IBannerService extends IService<Banner> {

    /**
     * 获取可显示的轮播图列表
     * 
     * @param language 语言标识
     * @param limit 限制数量
     * @return 轮播图列表
     */
    List<Banner> getVisibleBanners(String language, Integer limit);


} 