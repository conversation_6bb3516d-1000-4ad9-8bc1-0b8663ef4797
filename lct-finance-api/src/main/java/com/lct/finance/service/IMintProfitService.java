package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.MintProfit;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 每日铸造个人收益服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IMintProfitService extends IService<MintProfit> {
    
    /**
     * 获取用户指定日期的个人收益金额
     * 
     * @param userId 用户ID
     * @param profitDate 收益日期
     * @return 收益金额，如果没有记录则返回0
     */
    BigDecimal getTodayPersonalProfit(Long userId, LocalDate profitDate);
} 