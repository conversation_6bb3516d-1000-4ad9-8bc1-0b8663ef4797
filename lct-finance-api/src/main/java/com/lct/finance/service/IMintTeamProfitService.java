package com.lct.finance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.MintTeamProfit;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 每日铸造团队收益服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IMintTeamProfitService extends IService<MintTeamProfit> {

    /**
     * 获取用户指定日期的团队收益金额
     * 
     * @param userId     用户ID
     * @param profitDate 收益日期
     * @return 团队收益金额，如果没有记录则返回0
     */
    BigDecimal getTodayTeamProfit(Long userId, LocalDate profitDate);

    /**
     * 分页查询用户团队收益记录（性能优化版本，不连表）
     * 
     * @param page   分页对象
     * @param userId 用户ID
     * @param date   日期筛选（可选）
     * @return 分页结果
     */
    IPage<MintTeamProfit> selectTeamProfitHistoryOptimized(Page<MintTeamProfit> page,
            Long userId,
            String date);

    /**
     * 分页查询用户团队收益记录，包含来源用户地址
     * 注意：这个方法返回的数据包含额外的源用户地址信息
     * 
     * @param page   分页对象
     * @param userId 用户ID
     * @param date   日期筛选（可选）
     * @return 分页结果，每个对象都包含源用户地址
     */
    IPage<Map<String, Object>> selectTeamProfitHistoryWithSource(Page<Map<String, Object>> page,
            Long userId,
            String date);

    /**
     * 批量查询用户地址映射
     * 
     * @param userIds 用户ID列表
     * @return 用户信息列表，包含id和user_address字段
     */
    List<Map<String, Object>> selectUserAddressBatch(List<Integer> userIds);
}