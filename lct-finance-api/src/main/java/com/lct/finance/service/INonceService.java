package com.lct.finance.service;

import com.lct.finance.model.dto.NonceResponse;

/**
 * Nonce服务接口
 * 用于防止重放攻击，管理nonce的存储、验证和清理
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface INonceService {

    /**
     * 验证并存储nonce
     * 如果nonce已存在，则验证失败；如果不存在，则存储并返回成功
     * 
     * @param userAddress 用户地址
     * @param nonce nonce值（通常是时间戳）
     * @param timeWindow 时间窗口（秒），用于验证nonce的时效性
     * @return true验证成功 false验证失败
     */
    boolean validateAndStoreNonce(String userAddress, String nonce, long timeWindow);

    /**
     * 检查nonce是否已存在
     * 
     * @param userAddress 用户地址
     * @param nonce nonce值
     * @return true已存在 false不存在
     */
    boolean isNonceExists(String userAddress, String nonce);

    /**
     * 存储nonce
     * 
     * @param userAddress 用户地址
     * @param nonce nonce值
     * @param expireSeconds 过期时间（秒）
     * @return true存储成功 false存储失败
     */
    boolean storeNonce(String userAddress, String nonce, long expireSeconds);


    /**
     * 清理指定用户的所有过期nonce
     * 
     * @param userAddress 用户地址
     * @return 清理的nonce数量
     */
    long cleanExpiredNonces(String userAddress);


    /**
     * 获取用户当前有效的nonce数量
     * 
     * @param userAddress 用户地址
     * @return nonce数量
     */
    long getUserNonceCount(String userAddress);

    /**
     * 验证nonce的时间有效性
     * 
     * @param nonce nonce值（时间戳）
     * @param timeWindow 时间窗口（秒）
     * @return true时间有效 false时间无效
     */
    boolean isNonceTimeValid(String nonce, long timeWindow);
    
    /**
     * 生成操作特定的nonce
     * 
     * @param userAddress 用户地址
     * @param operation 操作类型
     * @param chain 链类型
     * @return nonce响应对象
     */
    NonceResponse generateNonce(String userAddress, String operation, String chain);
    

}