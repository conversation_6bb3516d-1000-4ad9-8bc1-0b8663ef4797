package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.Prices;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格历史数据服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IPricesService extends IService<Prices> {

    /**
     * 获取指定时间范围内每日最新价格记录
     * 
     * @param symbol    交易对符号
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 价格记录列表
     */
    List<Prices> getDailyLatestPrices(String symbol, LocalDateTime startDate, LocalDateTime endDate);
}