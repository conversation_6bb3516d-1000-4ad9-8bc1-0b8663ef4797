package com.lct.finance.service;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.Map;

/**
 * 配置管理服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
public interface IConfigService {

    /**
     * 获取提现配置
     * 
     * @return 提现配置信息
     */
    WithdrawalConfig getWithdrawalConfig();

    /**
     * 获取价格配置
     * 
     * @return 价格配置信息
     */
    PriceConfig getPriceConfig();



    /**
     * 获取邀请相关配置
     * @return 邀请配置映射
     */
    Map<String, Object> getInviteConfig();
    
    /**
     * 获取有效直推用户数配置
     * @return 有效直推用户数，-1表示不限制
     */
    Integer getEffectiveDirectUserCount();


    /**
     * 提现配置信息
     */
    @Data
    class WithdrawalConfig {
        /** 提现开始时间 */
        private LocalTime timeStart;
        /** 提现结束时间 */
        private LocalTime timeEnd;
        /** LCT手续费类型：1-固定金额，2-百分比 */
        private Integer feeType;
        /** LCT手续费值 */
        private BigDecimal fee;
        /** USDT手续费类型：1-固定金额，2-百分比 */
        private Integer usdtFeeType;
        /** USDT手续费值 */
        private BigDecimal usdtFee;
        /** 每日最大LCT提现次数 */
        private Integer dailyMaxCountLct;
        /** 每日最大USDT提现次数 */
        private Integer dailyMaxCountUsdt;

        }

    /**
     * 价格配置信息
     */
    @Data
    class PriceConfig {
        /** LCT对USDT汇率 */
        private BigDecimal lctUsdt;
        /** USDT对LCT汇率 */
        private BigDecimal usdtLct;
        /** LCT价格 */
        private BigDecimal lctPrice;
        /** USDT价格 */
        private BigDecimal usdtPrice;


    }

} 