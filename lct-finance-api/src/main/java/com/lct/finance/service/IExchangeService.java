package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.dto.*;
import com.lct.finance.model.entity.Exchange;

import java.util.List;

/**
 * 兑换服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IExchangeService extends IService<Exchange> {

    /**
     * 获取兑换基础数据
     * 
     * @return 兑换基础数据
     */
    ExchangeBasicResponse getBasicData();

    /**
     * 创建兑换订单
     * 
     * @param request     兑换创建请求
     * @param userAddress 用户地址
     * @return 兑换创建响应
     */
    ExchangeCreateResponse createExchange(ExchangeCreateRequest request, String userAddress);

    /**
     * 创建兑换订单（包含签名验证和风险评估）
     * 
     * @param request     兑换创建请求
     * @param userAddress 用户地址
     * @param clientIp    客户端IP
     * @return 兑换创建响应
     */
    ApiResponse<ExchangeCreateResponse> createExchangeWithValidation(ExchangeCreateRequest request, String userAddress,
            String clientIp);

    /**
     * 支付确认
     * 
     * @param request     支付确认请求
     * @param userAddress 用户地址
     * @return 支付确认结果
     */
    boolean payExchange(ExchangePayRequest request, String userAddress);

    /**
     * 兑换支付确认（包含完整验证流程）
     * 
     * @param request  支付确认请求
     * @param clientIp 客户端IP
     * @return 支付确认结果
     */
    ApiResponse<Boolean> payExchangeWithValidation(ExchangePayRequest request, String clientIp);

    /**
     * 获取兑换历史记录
     * 
     * @param userAddress 用户地址
     * @param page        页码
     * @param pageSize    页大小
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return 兑换历史记录列表
     */
    List<ExchangeHistoryResponse> getExchangeHistory(int page, int pageSize, String startDate, String endDate);

    /**
     * 根据ID获取兑换订单
     * 
     * @param exchangeId 兑换订单ID
     * @return 兑换订单
     */
    Exchange getExchangeById(Long exchangeId);

    /**
     * 根据订单号获取兑换订单
     * 
     * @param orderNo 订单号
     * @return 兑换订单
     */
    Exchange getExchangeByOrderNo(String orderNo);

    /**
     * 检查交易哈希是否已使用
     * 
     * @param txHash 交易哈希
     * @return 是否已使用
     */
    boolean isTransactionHashUsed(String txHash);

    /**
     * 验证兑换参数
     * 
     * @param request 兑换请求
     * @return 验证结果消息，null表示验证通过
     */
    String validateExchangeRequest(ExchangeCreateRequest request);

}