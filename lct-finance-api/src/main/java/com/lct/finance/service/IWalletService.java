package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.dto.WithdrawalHistoryResponse;
import com.lct.finance.model.dto.WithdrawalRequest;
import com.lct.finance.model.dto.WithdrawalResponse;
import com.lct.finance.model.entity.Wallet;
import com.lct.finance.model.entity.Withdrawal;
import com.lct.finance.model.response.OperationValidationResult;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 钱包服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IWalletService extends IService<Wallet> {

        /**
         * 根据用户ID查找钱包
         */
        Wallet findByUserId(Long userId);

        /**
         * 创建新钱包
         */
        Wallet createWallet(Long userId);

        /**
         * 带行锁查找钱包（类似PHP版本的lockForUpdate）
         */
        Wallet findByUserIdWithLock(Long userId);

        /**
         * 批量更新钱包字段（类似PHP版本的up方法）
         */
        void updateWalletFields(Long walletId, Map<String, Object> updates);

        /**
         * 获取或创建钱包（类似PHP版本的getWallet方法）
         */
        Wallet getOrCreateWallet(Long userId, boolean withLock);

        /**
         * 用户提现（包含完整验证流程）
         * 
         * @param request  提现请求
         * @param clientIp 客户端IP
         * @return 提现结果
         */
        ApiResponse<WithdrawalResponse> withdrawalWithValidation(WithdrawalRequest request, String clientIp);

        /**
         * 获取提现历史记录
         * 
         * @param date     日期筛选（可选，格式：YYYY-MM-DD）
         * @param page     页码（可选，默认1）
         * @param size     每页大小（可选，默认20）
         * @param pageSize 每页大小（兼容前端参数名）
         * @return 提现历史记录列表
         */
        List<WithdrawalHistoryResponse> getWithdrawalHistory(String date, Integer page, Integer size, Integer pageSize);

        /**
         * 处理用户提现请求
         * 
         * @param userId      用户ID
         * @param request     提现请求
         * @param userAddress 用户地址
         * @param userChain   用户链类型
         * @return 提现响应
         */
        WithdrawalResponse processWithdrawal(Long userId, WithdrawalRequest request, String userAddress,
                                             String userChain, OperationValidationResult validationResult);

        /**
         * 计算手续费
         * 
         * @param amount   金额
         * @param feeType  手续费类型：1-固定金额，2-百分比
         * @param feeValue 手续费值
         * @return 手续费金额
         */
        BigDecimal calculateFee(BigDecimal amount, Integer feeType, BigDecimal feeValue);

        /**
         * 检查用户每日提现次数限制
         * 
         * @param userId   用户ID
         * @param coinType 币种类型
         * @param maxCount 最大次数
         * @return 是否超过限制
         */
        boolean checkDailyWithdrawalLimit(Long userId, Integer coinType, Integer maxCount);

        /**
         * 生成提现订单号
         * 
         * @return 订单号
         */
        String generateWithdrawalOrderNo();

        /**
         * 创建提现记录
         * 
         * @param userId          用户ID
         * @param request         提现请求
         * @param actualAmount    实际金额
         * @param actualLctAmount 实际LCT金额
         * @param fee             手续费
         * @param rate            汇率
         * @param orderNo         订单号
         * @param ip              IP地址
         * @param priceConfig     价格配置
         * @return 提现记录
         */
        @Transactional(rollbackFor = Exception.class)
        Withdrawal createWithdrawalRecord(Long userId, WithdrawalRequest request,
                        BigDecimal actualAmount, BigDecimal actualLctAmount,
                        BigDecimal fee, BigDecimal rate, String orderNo,
                        String ip, IConfigService.PriceConfig priceConfig);

        /**
         * 处理提现历史查询业务逻辑
         * 
         * @param userId   用户ID
         * @param date     日期字符串（可选）
         * @param page     页码
         * @param size     每页大小
         * @param pageSize 每页大小（兼容参数）
         * @return 提现历史记录列表
         */
        List<WithdrawalHistoryResponse> handleWithdrawalHistory(Long userId, String date, Integer page, Integer size,
                        Integer pageSize);

        /**
         * 处理提现请求业务逻辑
         * 
         * @param userId         用户ID
         * @param userAddress    用户地址
         * @param amount         提现金额字符串
         * @param receiveAddress 接收地址
         * @param type           币种类型字符串
         * @param sign           签名
         * @param message        签名消息
         * @param nonce          随机数
         * @param clientIp       客户端IP
         * @return 提现响应
         */
        WithdrawalResponse handleWithdrawal(Long userId, String userAddress,
                        BigDecimal amount, String receiveAddress, String type,
                        String sign, String message, String nonce, String clientIp,OperationValidationResult validationResult);
}