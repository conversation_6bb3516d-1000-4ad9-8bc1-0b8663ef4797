package com.lct.finance.service;

import com.lct.finance.model.request.OperationValidationRequest;
import com.lct.finance.model.response.OperationValidationResult;

/**
 * 操作验证服务接口
 * 提供通用的签名验证、风险评估和日志记录功能
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IOperationValidationService {

    /**
     * 验证操作（签名验证 + 风险评估 + 日志记录）
     * 
     * @param request 验证请求
     * @return 验证结果
     */
    OperationValidationResult validateOperation(OperationValidationRequest request);
}