package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.Notice;
import com.lct.finance.model.entity.NoticeModal;
import com.lct.finance.model.dto.NoticeListResponse;

import java.util.List;

/**
 * 公告服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface INoticeService extends IService<Notice> {

    /**
     * 获取公告列表 - 支持分页和筛选
     * 
     * @param language 语言标识
     * @param page     页码
     * @param size     每页大小
     * @param date     日期筛选
     * @return 公告列表
     */
    List<Notice> getNoticesList(String language, Integer page, Integer size, String date);

    /**
     * 获取首页推荐公告列表
     * 
     * @param language 语言标识
     * @param limit    限制数量
     * @return 公告列表
     */
    List<Notice> getFlaggedNotices(String language, Integer limit);

    /**
     * 根据语言获取公告弹框
     *
     * @param language 语言标识
     * @return 公告弹框实体
     */
    NoticeModal getNoticeModal(String language);

    /**
     * 获取公告列表 - 处理控制器请求
     * 
     * @param page     页码
     * @param size     每页大小
     * @param date     日期筛选
     * @param language 语言标识
     * @return 公告响应列表
     */
    List<NoticeListResponse> getNotices(Integer page, Integer size, String date, String language);
}