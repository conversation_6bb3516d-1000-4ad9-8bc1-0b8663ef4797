package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.Invite;
import com.lct.finance.model.entity.User;

import java.time.LocalDate;
import java.util.List;

/**
 * 邀请关系服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public interface IInviteService extends IService<Invite> {

    /**
     * 建立邀请关系
     * 
     * @param inviter 邀请人
     * @param invitee 被邀请人
     * @return 是否成功
     */
    boolean createInviteRelation(User inviter, User invitee);

    /**
     * 构建用户的临时族谱
     *
     * @param userId  用户ID
     * @param inviter 邀请人
     * @return 临时族谱字符串
     */
    String buildTemporaryPids(Long userId, User inviter);

    /**
     * 统计用户的直推人数
     * 
     * @param userId 用户ID
     * @return 直推人数
     */
    Long countDirectInvitees(Long userId);

    /**
     * 统计用户的总邀请人数
     * 
     * @param userId 用户ID
     * @return 总邀请人数
     */
    Long countAllInvitees(Long userId);

    /**
     * 更新用户的邀请统计
     * 
     * @param userId 用户ID
     */
    void updateInviteStats(Long userId);

    /**
     * 构建用户的族谱路径
     * 
     * @param userId 用户ID
     * @return 族谱路径
     */
    String buildPidFullPath(Long userId);

    /**
     * 获取用户的所有邀请关系（作为邀请人）
     * 
     * @param inviterUserId 邀请人用户ID
     * @return 邀请关系列表
     */
    List<Invite> getInvitesByInviterUserId(Long inviterUserId);

    /**
     * 获取用户被邀请的关系链（作为被邀请人）
     * 
     * @param inviteeUserId 被邀请人用户ID
     * @return 邀请关系列表
     */
    List<Invite> getInvitesByInviteeUserId(Long inviteeUserId);

    /**
     * 获取指定层级的邀请关系
     * 
     * @param inviterUserId 邀请人用户ID
     * @param level 邀请层级
     * @return 邀请关系列表
     */
    List<Invite> getDirectInvitesByInviterUserId(Long inviterUserId, Integer level);

    /**
     * 获取用户今日直推邀请人数
     * 
     * @param userId 用户ID
     * @param date 日期
     * @return 今日直推邀请人数
     */
    Integer getTodayDirectInviteCount(Long userId, LocalDate date);

    /**
     * 获取用户今日团队新增人数
     * 
     * @param userId 用户ID
     * @param date 日期
     * @return 今日团队新增人数
     */
    Integer getTodayTeamInviteCount(Long userId, LocalDate date);

    /**
     * 获取用户所有层级的下级用户ID列表
     * 
     * @param userId 用户ID
     * @return 所有下级用户ID列表
     */
    List<Long> getAllInviteeUserIds(Long userId);
}