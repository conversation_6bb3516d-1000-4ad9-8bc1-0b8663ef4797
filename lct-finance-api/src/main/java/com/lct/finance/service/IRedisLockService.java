package com.lct.finance.service;

import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
public interface IRedisLockService {

    /**
     * 尝试获取锁
     * 
     * @param key 锁的键
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否获取成功
     */
    boolean tryLock(String key, long timeout, TimeUnit unit);



    /**
     * 释放锁
     * 
     * @param key 锁的键
     * @return 是否释放成功
     */
    boolean releaseLock(String key);


} 