package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.entity.UserOperationLog;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户操作日志服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
public interface IUserOperationLogService extends IService<UserOperationLog> {

    /**
     * 获取用户最近操作次数
     * 
     * @param userAddress   用户地址
     * @param operationType 操作类型
     * @param minutes       分钟数
     * @return 操作次数
     */
    int countRecentOperations(String userAddress, String operationType, int minutes);

    /**
     * 获取用户最近操作次数（别名）
     * 
     * @param userAddress 用户地址
     * @param operation   操作类型
     * @param minutes     分钟数
     * @return 操作次数
     */
    int getRecentOperationCount(String userAddress, String operation, int minutes);

    /**
     * 获取用户操作的平均金额
     * 
     * @param userAddress   用户地址
     * @param operationType 操作类型
     * @return 平均金额
     */
    BigDecimal getAverageOperationAmount(String userAddress, String operationType);

    /**
     * 获取用户最近的设备指纹
     * 
     * @param userAddress 用户地址
     * @return 设备指纹
     */
    String getLastDeviceFingerprint(String userAddress);

    /**
     * 获取用户最近的操作记录
     * 
     * @param userAddress 用户地址
     * @param limit       记录数
     * @return 操作记录列表
     */
    List<UserOperationLog> getRecentOperations(String userAddress, int limit);

    /**
     * 记录用户操作日志
     * 
     * @param address   用户地址
     * @param operation 操作类型
     * @param amount    操作金额
     * @param clientIp  客户端IP
     * @param riskLevel 风险级别
     * @param status    状态（0失败，1成功）
     */
    void recordOperationLog(String address, String operation, String amount,
            String clientIp, int riskLevel, int status);

    /**
     * 记录用户操作日志（包含评分详情）
     * 
     * @param address           用户地址
     * @param operation         操作类型
     * @param amount            操作金额
     * @param clientIp          客户端IP
     * @param deviceFingerprint 设备指纹
     * @param riskLevel         风险级别
     * @param status            状态（0失败，1成功）
     * @param riskScoreDetail   风险评分详情
     */
    void recordOperationLog(String address, String operation, String amount,
            String clientIp, String deviceFingerprint, int riskLevel,
            int status, String riskScoreDetail);
}