package com.lct.finance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lct.finance.model.dto.ClaimNewUserGiftResponse;
import com.lct.finance.model.dto.NewUserGiftStatusResponse;
import com.lct.finance.model.entity.NewUserGift;
import com.lct.finance.model.dto.ClaimNewUserGiftRequest;
import com.lct.finance.model.dto.ApiResponse;

/**
 * 新用户礼包服务接口
 * 
 * <AUTHOR> Finance Team
 * @since 2025-01-17
 */
public interface INewUserGiftService extends IService<NewUserGift> {

    /**
     * 检查用户的新用户礼包状态
     * 
     * @param userId 用户ID
     * @return 礼包状态响应
     */
    NewUserGiftStatusResponse checkGiftStatus(Long userId);

    /**
     * 领取新用户礼包
     * 
     * @param userId 用户ID
     * @return 领取结果响应
     * @throws RuntimeException 当用户不符合领取条件时抛出异常
     */
    ClaimNewUserGiftResponse claimGift(Long userId);

    /**
     * 领取新用户礼包（包含完整验证流程）
     * 
     * @param request  领取礼包请求
     * @param clientIp 客户端IP
     * @return 领取结果
     */
    ApiResponse<ClaimNewUserGiftResponse> claimGiftWithValidation(ClaimNewUserGiftRequest request, String clientIp);

    /**
     * 创建新用户礼包记录
     * 
     * @param userId 用户ID
     * @return 创建的礼包记录
     */
    NewUserGift createGiftRecord(Long userId);

    /**
     * 检查用户是否可以领取新用户礼包
     * 
     * @param userId 用户ID
     * @return true-可以领取, false-不可以领取
     */
    boolean canClaimGift(Long userId);

    /**
     * 标记礼包为已领取
     * 
     * @param giftId 礼包ID
     * @return 更新后的礼包记录
     */
    NewUserGift markGiftAsClaimed(Long giftId);
}