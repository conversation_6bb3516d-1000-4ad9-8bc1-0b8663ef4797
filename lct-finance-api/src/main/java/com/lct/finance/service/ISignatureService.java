package com.lct.finance.service;

import com.lct.finance.model.dto.SignatureVerificationRequest;
import com.lct.finance.model.dto.SignatureVerificationResult;

import java.util.Map;

/**
 * 数字签名验证服务接口
 * 专注于签名验证功能，不包含风险评估
 *
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
public interface ISignatureService {

    /**
     * 验证签名
     *
     * @param request 签名验证请求
     * @return 签名验证结果
     * @deprecated 请使用 verifySignature(String, String, String, String) 或 verifyOperationSignature 替代
     */
    SignatureVerificationResult verifySignature(SignatureVerificationRequest request);

    /**
     * 生成操作消息模板
     *
     * @param operation 操作类型
     * @param params 参数映射
     * @return 消息模板
     */
    String generateMessageTemplate(String operation, Map<String, String> params);

    /**
     * 验证签名
     *
     * @param message 消息内容
     * @param signature 签名
     * @param address 地址
     * @param chainType 链类型
     * @return 是否验证通过
     */
    boolean verifySignature(String message, String signature, String address, String chainType);
} 