

-- 2. 为用户表添加新用户标识字段（如果不存在）
ALTER TABLE `users` 
ADD COLUMN `is_new_user` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否新用户 0-否 1-是';

-- 3. 为现有用户设置为老用户（可选，根据业务需求决定）
-- UPDATE `users` SET `is_new_user` = 0 WHERE `created_at` < '2025-01-17 00:00:00';

-- 4. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_users_is_new_user` ON `users` (`is_new_user`);

-- 5. 插入初始配置数据（如果需要）
-- INSERT INTO `options` (`option_name`, `option_value`, `description`) VALUES 
-- ('new_user_gift_amount', '5.000000000000000000', '新用户礼包金额'),
-- ('new_user_gift_enabled', '1', '新用户礼包功能是否启用');