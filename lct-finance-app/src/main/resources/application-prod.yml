# 生产环境配置
spring:
  # 数据库配置
  datasource:
    url: ********************************************************************************************************************************************************
    username: root
    password: lxl123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      pool-name: HikariCP
      minimum-idle: 10
      maximum-pool-size: 50
      auto-commit: true
      idle-timeout: 30000
      max-lifetime: 900000
      connection-timeout: 10000
      connection-test-query: SELECT 1
  
  # Redis配置 - Spring Boot 2.7.x兼容的配置路径
  redis:
    host: ************
    port: 6379
    password: lxl123
    database: 0
    timeout: 10s
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 5

# 日志配置 - 生产环境特定
logging:
  level:
    com.lct.finance: INFO
    org.springframework.web: WARN
    org.springframework.security: WARN
  
# 区块链配置 - 生产环境
blockchain:
  # BSC 主网
  bsc:
    rpc-url: https://bsc-dataseed1.binance.org/
    chain-id: 56
    lct-contract-address: "0x2B8F0750C7849f7311967f4Faa4d48F694195E58"
    usdt-contract-address: "0x55d398326f99059fF775485246999027B3197955"

  # TRON 主网
  tron:
    rpc-url: https://api.trongrid.io
    # 无用
    lct-contract-address: "TLrEaH7cANBo3fKz1RLPyLw5kUNtEBhz4L"
    usdt-contract-address: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"


# 应用配置 - 生产环境特定
app:
  # 跨域配置 - 生产环境特定
  cors:
    allowed-origins: 
      # TODO
      - "https://www.lctweb.xyz"
      - "https://www.lctweb.info"
      - "https://www.lctweb.me"
      - "https://www.lctweb.club"
  
  # 区块链网络配置 - 生产环境
  blockchain:
    # 生产环境支持的网络
    supported-networks:
      - BSC

    # 网络映射配置
    network-mappings:
      BSC:
        name: "Binance Smart Chain"
        address-type: "ETH"
        signature-type: "ETH"
