<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lct.finance.mapper.ExchangeMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.lct.finance.model.entity.Exchange">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="deposit_amount" property="depositAmount" jdbcType="DECIMAL"/>
        <result column="deposit_coin_type" property="depositCoinType" jdbcType="TINYINT"/>
        <result column="receive_amount" property="receiveAmount" jdbcType="DECIMAL"/>
        <result column="receive_coin_type" property="receiveCoinType" jdbcType="TINYINT"/>
        <result column="receive_address" property="receiveAddress" jdbcType="VARCHAR"/>
        <result column="txhash" property="txhash" jdbcType="VARCHAR"/>
        <result column="receive_txhash" property="receiveTxhash" jdbcType="VARCHAR"/>
        <result column="usdt_receive_address" property="usdtReceiveAddress" jdbcType="VARCHAR"/>
        <result column="action_type" property="actionType" jdbcType="TINYINT"/>
        <result column="exchange_type" property="exchangeType" jdbcType="TINYINT"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="err_reason" property="errReason" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通用查询字段 -->
    <sql id="Base_Column_List">
        id, order_id, status, user_id, address, deposit_amount, deposit_coin_type,
        receive_amount, receive_coin_type, receive_address, txhash, receive_txhash,
        usdt_receive_address, action_type, exchange_type, ip, err_reason,
        created_at, updated_at
    </sql>

    <!-- 分页查询用户兑换记录（复杂查询：分页+动态SQL） -->
    <select id="selectUserExchangeHistory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM lct_lct_exchanges
        WHERE user_id = #{userId}
        <if test="date != null">
            AND DATE(created_at) = #{date}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 查询用户今日兑换记录数量（复杂查询：DATE函数） -->
    <select id="countTodayExchangeByUserId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM lct_exchanges
        WHERE user_id = #{userId} 
        AND DATE(created_at) = #{date}
    </select>

    <!-- 查询待处理的兑换订单（复杂查询：多状态IN查询） -->
    <select id="selectPendingOrders" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM lct_exchanges
        WHERE status IN (2, 3, 5)
        ORDER BY created_at ASC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新订单状态（复杂查询：批量操作） -->
    <update id="batchUpdateStatus">
        UPDATE lct_exchanges
        SET status = #{status}
        <if test="errReason != null and errReason != ''">
            , err_reason = #{errReason}
        </if>
        , updated_at = NOW()
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>

    <!-- 统计用户兑换总金额（复杂查询：CASE WHEN聚合） -->
    <select id="sumExchangeAmountByUser" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(
            CASE 
                WHEN #{coinType} = 1 THEN 
                    CASE WHEN deposit_coin_type = 1 THEN deposit_amount ELSE receive_amount END
                ELSE 
                    CASE WHEN deposit_coin_type != 1 THEN deposit_amount ELSE receive_amount END
            END
        ), 0)
        FROM lct_exchanges
        WHERE user_id = #{userId} AND status = 1
    </select>

    <!-- 查询指定时间范围内的兑换记录（复杂查询：时间范围+动态SQL） -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM lct_exchanges
        WHERE DATE(created_at) BETWEEN #{startDate} AND #{endDate}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 更新兑换订单的交易哈希（复杂查询：复合更新） -->
    <update id="updateTxhashAndStatus">
        UPDATE lct_exchanges
        SET txhash = #{txhash}, 
            status = #{status}, 
            updated_at = NOW()
        WHERE order_id = #{orderId}
    </update>

    <!-- 更新兑换订单的接收交易哈希（复杂查询：复合更新） -->
    <update id="updateReceiveTxhashAndStatus">
        UPDATE lct_exchanges
        SET receive_txhash = #{receiveTxhash}, 
            status = #{status}, 
            updated_at = NOW()
        WHERE order_id = #{orderId}
    </update>

    <!-- 查询用户最近的兑换记录（复杂查询：排序+限制） -->
    <select id="selectRecentExchangesByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM lct_burns
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计交易哈希数量(用于事务验证服务)（复杂查询：业务统计） -->
    <select id="countByTxHash" resultType="int">
        SELECT COUNT(*)
        FROM lct_burns
        WHERE txhash = #{txHash}
    </select>

    <!-- 统计所有交易哈希数量（复杂查询：全表统计） -->
    <select id="countAllTxHash" resultType="int">
        SELECT COUNT(*)
        FROM lct_burns
        WHERE txhash IS NOT NULL AND txhash != ''
    </select>

    <!-- 统计今日交易哈希数量（复杂查询：时间统计） -->
    <select id="countTodayTxHash" resultType="int">
        SELECT COUNT(*)
        FROM lct_burns
        WHERE txhash IS NOT NULL AND txhash != ''
        AND DATE(created_at) = CURDATE()
    </select>

    <!-- 
    简单查询已移除，现在在Service层使用LambdaQueryWrapper实现：
    - selectByOrderId
    - selectByTxhash
    - countByUserIdAndStatus
    - existsByTxhash
    -->

</mapper> 