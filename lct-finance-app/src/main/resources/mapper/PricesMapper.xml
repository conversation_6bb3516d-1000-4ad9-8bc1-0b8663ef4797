<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lct.finance.mapper.PricesMapper">

    <!-- 获取指定时间范围内每日最新价格记录 -->
    <select id="getDailyLatestPrices" resultType="com.lct.finance.model.entity.Prices">
        SELECT * FROM prices WHERE symbol = #{symbol} 
        AND id IN (
            SELECT MAX(id) FROM prices 
            WHERE symbol = #{symbol} 
            AND created_at BETWEEN #{startDate} AND #{endDate} 
            GROUP BY DATE(created_at)
        ) 
        ORDER BY created_at ASC
    </select>

</mapper> 