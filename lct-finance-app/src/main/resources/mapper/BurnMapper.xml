<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lct.finance.mapper.BurnMapper">

    <!-- 获取所有用户钱包数据，按销毁量降序排序 -->
    <select id="getUserWalletsByBurn" resultType="java.util.Map">
        SELECT u.id as user_id, COALESCE(SUM(b.actual_amount), 0) as burn 
        FROM lct_members u 
        LEFT JOIN lct_burns b ON u.id = b.user_id AND b.tx_status = 1 
        WHERE u.state = 1 
        GROUP BY u.id 
        HAVING burn > 0 
        ORDER BY burn DESC
    </select>

</mapper> 