<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lct.finance.mapper.WalletMapper">

    <!-- 根据用户ID查询钱包（带行锁） - 复杂查询，需要保留 -->
    <select id="selectByUserIdWithLock" parameterType="java.lang.Long" resultType="com.lct.finance.model.entity.Wallet">
        SELECT * FROM lct_wallets WHERE user_id = #{userId} FOR UPDATE
    </select>

    <!-- 更新可用余额（USDT） -->
    <update id="updateAvailableBalance">
        UPDATE lct_wallets
        SET available = available + #{amount}, updated_at = NOW() 
        WHERE id = #{walletId}
    </update>

    <!-- 更新理财余额（LCT） -->
    <update id="updateFinanceBalance">
        UPDATE lct_wallets
        SET finance = finance + #{amount}, updated_at = NOW() 
        WHERE id = #{walletId}
    </update>

    <!-- 更新收益 -->
    <update id="updateProfit">
        UPDATE lct_wallets
        SET profit = profit + #{amount}, updated_at = NOW() 
        WHERE id = #{walletId}
    </update>

    <!-- 更新团队收益 -->
    <update id="updateTeamProfit">
        UPDATE lct_wallets
        SET team_profit = team_profit + #{amount}, updated_at = NOW() 
        WHERE id = #{walletId}
    </update>

    <!-- 统计总资产 -->
    <select id="getTotalAssets" resultType="java.math.BigDecimal">
        SELECT SUM(available + frozen + finance + burn + checkin + profit + team_profit) 
        FROM lct_wallets
    </select>

    <!-- 批量更新钱包字段（类似PHP版本的up方法） -->
    <update id="updateWalletFields" parameterType="com.lct.finance.model.entity.Wallet">
        UPDATE lct_wallets SET
            available = #{available}, 
            frozen = #{frozen}, 
            finance = #{finance}, 
            burn = #{burn}, 
            checkin = #{checkin}, 
            profit = #{profit}, 
            team_profit = #{teamProfit}, 
            direct_invite = #{directInvite}, 
            experience = #{experience}, 
            community_chief_rebate = #{communityChiefRebate}, 
            community_chief_experience = #{communityChiefExperience}, 
            agent_available = #{agentAvailable}, 
            agent_frozen = #{agentFrozen}, 
            updated_at = NOW() 
        WHERE id = #{id}
    </update>

</mapper> 