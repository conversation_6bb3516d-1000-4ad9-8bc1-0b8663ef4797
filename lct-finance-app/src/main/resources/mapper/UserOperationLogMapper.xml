<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lct.finance.mapper.UserOperationLogMapper">


    
    <!-- 获取用户最近操作次数（复杂查询：时间计算） -->
    <select id="countRecentOperations" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM lct_user_operation_log
        WHERE user_address = #{userAddress} 
        AND operation_type = #{operationType} 
        AND operation_time > DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
    </select>
    
    <!-- 获取用户操作的平均金额（复杂查询：聚合计算） -->
    <select id="getAverageOperationAmount" resultType="java.math.BigDecimal">
        SELECT AVG(amount) 
        FROM lct_user_operation_log
        WHERE user_address = #{userAddress} 
        AND operation_type = #{operationType} 
        AND status = 1
    </select>
    

    
</mapper> 