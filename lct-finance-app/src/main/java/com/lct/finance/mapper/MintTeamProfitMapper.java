package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.MintTeamProfit;
import org.apache.ibatis.annotations.Mapper;

/**
 * 每日铸造团队收益数据访问接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface MintTeamProfitMapper extends BaseMapper<MintTeamProfit> {
    // 所有查询方法都已在Service层中使用MyBatis-Plus实现
    // BaseMapper提供了基础的CRUD操作
}