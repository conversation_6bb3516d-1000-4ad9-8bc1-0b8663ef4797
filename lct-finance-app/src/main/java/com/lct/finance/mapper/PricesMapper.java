package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.Prices;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格历史数据访问接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface PricesMapper extends BaseMapper<Prices> {

    /**
     * 获取指定时间范围内每日最新价格记录
     * 复杂查询，在mapper.xml中实现
     * 
     * @param symbol 交易对符号
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 价格记录列表
     */
    List<Prices> getDailyLatestPrices(@Param("symbol") String symbol, 
                                     @Param("startDate") LocalDateTime startDate, 
                                     @Param("endDate") LocalDateTime endDate);
} 