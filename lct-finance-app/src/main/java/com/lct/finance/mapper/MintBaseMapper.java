package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.MintBase;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * MintBase 数据访问层
 * <AUTHOR> Finance Team
 * @since 2024-12-16
 */
@Mapper
public interface MintBaseMapper extends BaseMapper<MintBase> {

    /**
     * 获取用户预估总收益（profit_max_amount总和）
     * @param userId 用户ID
     * @return 预估总收益
     */
    @Select("SELECT COALESCE(SUM(profit_max_amount), 0) FROM lct_mint_base WHERE user_id = #{userId}")
    BigDecimal getEstimateTotalProfit(@Param("userId") Long userId);
} 