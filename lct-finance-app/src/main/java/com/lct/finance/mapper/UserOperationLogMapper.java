package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.UserOperationLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * 用户操作日志Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Mapper
public interface UserOperationLogMapper extends BaseMapper<UserOperationLog> {

    /**
     * 获取用户最近操作次数（复杂查询：时间计算）
     * 
     * @param userAddress   用户地址
     * @param operationType 操作类型
     * @param minutes       分钟数
     * @return 操作次数
     */
    int countRecentOperations(@Param("userAddress") String userAddress,
            @Param("operationType") String operationType,
            @Param("minutes") int minutes);

    /**
     * 获取用户操作的平均金额（复杂查询：聚合计算）
     * 
     * @param userAddress   用户地址
     * @param operationType 操作类型
     * @return 平均金额
     */
    BigDecimal getAverageOperationAmount(@Param("userAddress") String userAddress,
            @Param("operationType") String operationType);

    // 简单查询已移除，请在Service层使用LambdaQueryWrapper：
    //
    // 获取用户最近的设备指纹：
    // lambdaQuery().eq(UserOperationLog::getUserAddress, userAddress)
    // .isNotNull(UserOperationLog::getDeviceFingerprint)
    // .orderByDesc(UserOperationLog::getOperationTime)
    // .last("LIMIT 1").one()
    //
    // 获取用户最近的操作记录：
    // lambdaQuery().eq(UserOperationLog::getUserAddress, userAddress)
    // .orderByDesc(UserOperationLog::getOperationTime)
    // .last("LIMIT " + limit).list()
}