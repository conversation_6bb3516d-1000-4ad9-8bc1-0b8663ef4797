package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.ExchangeStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

/**
 * 兑换统计数据访问层
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-01
 */
@Mapper
public interface ExchangeStatisticsMapper extends BaseMapper<ExchangeStatistics> {

    /**
     * 扣除销毁兑换余额
     * 使用原子操作确保数据一致性
     * 
     * @param id     统计记录ID
     * @param amount 扣除金额
     * @return 影响行数
     */
    @Update("UPDATE lct_exchange_statistics SET " +
            "burn_need_exchange_remaining = burn_need_exchange_remaining - #{amount}, " +
            "updated_at = NOW() " +
            "WHERE id = #{id} AND burn_need_exchange_remaining >= #{amount}")
    int deductBurnExchangeRemaining(@Param("id") Long id, @Param("amount") BigDecimal amount);

}