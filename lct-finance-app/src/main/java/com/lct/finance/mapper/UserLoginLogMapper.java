package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.UserLoginLog;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户登录日志Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Mapper
public interface UserLoginLogMapper extends BaseMapper<UserLoginLog> {

    // 简单查询已移除，请在Service层使用LambdaQueryWrapper：
    //
    // 获取用户最近一次登录记录：
    // lambdaQuery().eq(UserLoginLog::getUserAddress, userAddress)
    // .orderByDesc(UserLoginLog::getLoginTime)
    // .last("LIMIT 1").one()
    //
    // 根据IP获取地理位置信息：
    // lambdaQuery().eq(UserLoginLog::getClientIp, ip)
    // .isNotNull(UserLoginLog::getIpLocation)
    // .orderByDesc(UserLoginLog::getLoginTime)
    // .last("LIMIT 1").one()
}