package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.Invite;
import org.apache.ibatis.annotations.Mapper;

/**
 * 邀请关系Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface InviteMapper extends BaseMapper<Invite> {
    // 简单查询使用MyBatis Plus的LambdaQueryWrapper：
    // 例如：lambdaQuery().eq(Invite::getInviterId, inviterId).list()
    // lambdaQuery().eq(Invite::getInviteeId, inviteeId).one()
}
