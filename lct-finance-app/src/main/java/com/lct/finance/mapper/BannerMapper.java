package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.Banner;
import org.apache.ibatis.annotations.Mapper;

/**
 * 轮播图Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface BannerMapper extends BaseMapper<Banner> {
    // 使用MyBatis-Plus的LambdaQueryWrapper进行查询
    // 例如：lambdaQuery().eq(Banner::getShow, 1).eq(Banner::getLanguage, language).list()
} 