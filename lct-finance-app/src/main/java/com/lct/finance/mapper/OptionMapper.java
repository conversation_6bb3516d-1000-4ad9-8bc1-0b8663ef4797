package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.Option;
import org.apache.ibatis.annotations.Mapper;

/**
 * 系统配置数据访问接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface OptionMapper extends BaseMapper<Option> {
    // 使用MyBatis-Plus的LambdaQueryWrapper进行查询
    // 例如：lambdaQuery().eq(Option::getOptionName, name).one()
} 