package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lct.finance.model.entity.Exchange;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 兑换订单数据访问接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Mapper
public interface ExchangeMapper extends BaseMapper<Exchange> {

        /**
         * 分页查询用户兑换记录（复杂查询：分页+动态SQL）
         * 
         * @param page   分页参数
         * @param userId 用户ID
         * @param date   日期筛选（可选）
         * @return 兑换记录分页结果
         */
        IPage<Exchange> selectUserExchangeHistory(
                        Page<Exchange> page,
                        @Param("userId") Long userId,
                        @Param("date") LocalDate date);

        /**
         * 查询用户今日兑换记录数量（复杂查询：DATE函数）
         * 
         * @param userId 用户ID
         * @param date   日期
         * @return 记录数量
         */
        Long countTodayExchangeByUserId(@Param("userId") Long userId, @Param("date") LocalDate date);

        /**
         * 查询待处理的兑换订单（复杂查询：多状态IN查询）
         * 
         * @param limit 限制数量
         * @return 待处理订单列表
         */
        List<Exchange> selectPendingOrders(@Param("limit") Integer limit);

        /**
         * 批量更新订单状态（复杂查询：批量操作）
         * 
         * @param orderIds  订单ID列表
         * @param status    新状态
         * @param errReason 错误原因（可选）
         * @return 更新的记录数
         */
        Integer batchUpdateStatus(
                        @Param("orderIds") List<String> orderIds,
                        @Param("status") Integer status,
                        @Param("errReason") String errReason);

        /**
         * 统计用户兑换总金额（复杂查询：CASE WHEN聚合）
         * 
         * @param userId   用户ID
         * @param coinType 币种类型
         * @return 总金额
         */
        java.math.BigDecimal sumExchangeAmountByUser(
                        @Param("userId") Long userId,
                        @Param("coinType") Integer coinType);

        /**
         * 查询指定时间范围内的兑换记录（复杂查询：时间范围+动态SQL）
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param status    状态筛选（可选）
         * @return 兑换记录列表
         */
        List<Exchange> selectByDateRange(
                        @Param("startDate") LocalDate startDate,
                        @Param("endDate") LocalDate endDate,
                        @Param("status") Integer status);

        /**
         * 更新兑换订单的交易哈希（复杂查询：复合更新）
         * 
         * @param orderId 订单ID
         * @param txhash  交易哈希
         * @param status  新状态
         * @return 更新的记录数
         */
        Integer updateTxhashAndStatus(
                        @Param("orderId") String orderId,
                        @Param("txhash") String txhash,
                        @Param("status") Integer status);

        /**
         * 更新兑换订单的接收交易哈希（复杂查询：复合更新）
         * 
         * @param orderId       订单ID
         * @param receiveTxhash 接收交易哈希
         * @param status        新状态
         * @return 更新的记录数
         */
        Integer updateReceiveTxhashAndStatus(
                        @Param("orderId") String orderId,
                        @Param("receiveTxhash") String receiveTxhash,
                        @Param("status") Integer status);

        /**
         * 查询用户最近的兑换记录（复杂查询：排序+限制）
         * 
         * @param userId 用户ID
         * @param limit  限制数量
         * @return 最近的兑换记录
         */
        List<Exchange> selectRecentExchangesByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

        /**
         * 根据交易哈希统计记录数量（复杂查询：业务统计）
         * 
         * @param txHash 交易哈希
         * @return 记录数量
         */
        int countByTxHash(@Param("txHash") String txHash);

        /**
         * 统计所有交易哈希数量（复杂查询：全表统计）
         * 
         * @return 总数量
         */
        int countAllTxHash();

        /**
         * 统计今日交易哈希数量（复杂查询：时间统计）
         * 
         * @return 今日数量
         */
        int countTodayTxHash();

        // 简单查询已移除，请在Service层使用LambdaQueryWrapper：
        //
        // 根据订单ID查询：
        // lambdaQuery().eq(Exchange::getOrderId, orderId).one()
        //
        // 根据交易哈希查询：
        // lambdaQuery().eq(Exchange::getTxhash, txhash).one()
        //
        // 用户状态统计：
        // lambdaQuery().eq(Exchange::getUserId, userId).eq(Exchange::getStatus,
        // status).count()
        //
        // 交易哈希存在性检查：
        // lambdaQuery().eq(Exchange::getTxhash, txhash).exists()
}