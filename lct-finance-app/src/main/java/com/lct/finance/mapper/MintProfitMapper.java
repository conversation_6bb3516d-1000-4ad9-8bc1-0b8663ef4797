package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.MintProfit;
import org.apache.ibatis.annotations.Mapper;

/**
 * 每日铸造个人收益数据访问接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface MintProfitMapper extends BaseMapper<MintProfit> {
    // 使用MyBatis-Plus的LambdaQueryWrapper进行查询
    // 例如：lambdaQuery().eq(MintProfit::getUserId, userId).list()
} 