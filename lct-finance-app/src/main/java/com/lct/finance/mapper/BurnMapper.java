package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.Burn;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 销毁记录Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface BurnMapper extends BaseMapper<Burn> {
    /**
     * 获取所有用户钱包数据，按销毁量降序排序
     * 用于更新临时族谱功能
     * 
     * @return 用户钱包数据列表，包含user_id和burn字段
     */
    List<Map<String, Object>> getUserWalletsByBurn();
}