package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.Wallet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface WalletMapper extends BaseMapper<Wallet> {

    /**
     * 根据用户ID查询钱包（带行锁） - 复杂查询，保留
     */
    Wallet selectByUserIdWithLock(@Param("userId") Long userId);

    // 简单查询已移除，使用 lambdaQuery().eq(Wallet::getUserId, userId).one()
    // 排行榜查询已移除，使用 lambdaQuery().orderByDesc(Wallet::getProfit).last("LIMIT " + limit).list()

    /**
     * 更新可用余额（USDT）
     */
    int updateAvailableBalance(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    /**
     * 更新理财余额（LCT）
     */
    int updateFinanceBalance(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    /**
     * 更新收益
     */
    int updateProfit(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    /**
     * 更新团队收益
     */
    int updateTeamProfit(@Param("walletId") Long walletId, @Param("amount") BigDecimal amount);

    /**
     * 统计总资产
     */
    BigDecimal getTotalAssets();

    /**
     * 批量更新钱包字段（类似PHP版本的up方法）
     */
    int updateWalletFields(Wallet wallet);
} 