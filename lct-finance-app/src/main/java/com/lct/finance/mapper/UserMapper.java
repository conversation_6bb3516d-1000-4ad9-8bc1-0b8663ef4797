package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.User;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    // 使用MyBatis-Plus的LambdaQueryWrapper进行查询
    // 例如：lambdaQuery().eq(User::getUserAddress, address).one()
} 