package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.NewUserGift;
import org.apache.ibatis.annotations.Mapper;

/**
 * 新用户礼品Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface NewUserGiftMapper extends BaseMapper<NewUserGift> {
    // 简单查询使用MyBatis Plus的LambdaQueryWrapper：
    // 例如：lambdaQuery().eq(NewUserGift::getUserId, userId).one()
    // lambdaQuery().eq(NewUserGift::getStatus, status).list()
}