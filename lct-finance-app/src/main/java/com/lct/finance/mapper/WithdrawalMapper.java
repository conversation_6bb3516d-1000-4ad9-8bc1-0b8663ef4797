package com.lct.finance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lct.finance.model.entity.Withdrawal;
import org.apache.ibatis.annotations.Mapper;

/**
 * 提现记录Mapper接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Mapper
public interface WithdrawalMapper extends BaseMapper<Withdrawal> {
    // 简单查询使用MyBatis Plus的LambdaQueryWrapper：
    // 例如：lambdaQuery().eq(Withdrawal::getUserId, userId).list()
    // lambdaQuery().eq(Withdrawal::getStatus, status).count()
}