package com.lct.finance.filter;

import com.lct.finance.utils.SensitiveDataFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 简化的请求响应日志过滤器
 * 只记录核心信息：请求路径、请求内容、响应内容、响应时间
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 1)
public class LoggingFilter extends OncePerRequestFilter {

    @Value("${logging.request-response.enabled:true}")
    private boolean loggingEnabled;

    @Value("${logging.request-response.max-body-size:1024}")
    private int maxBodySize;

    @Value("${logging.request-response.exclude-paths:/actuator,/favicon.ico}")
    private String excludePaths;

    /**
     * 需要排除的路径列表
     */
    private List<String> getExcludePaths() {
        if (excludePaths == null || excludePaths.trim().isEmpty()) {
            return Arrays.asList();
        }
        return Arrays.asList(excludePaths.split(","));
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        if (!loggingEnabled || shouldSkipLogging(request)) {
            filterChain.doFilter(request, response);
            return;
        }

        long startTime = System.currentTimeMillis();

        // 包装请求和响应以支持重复读取
        ContentCachingRequestWrapper wrappedRequest = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper wrappedResponse = new ContentCachingResponseWrapper(response);

        try {
            // 继续过滤器链
            filterChain.doFilter(wrappedRequest, wrappedResponse);

        } finally {
            // 记录请求响应信息
            long duration = System.currentTimeMillis() - startTime;
            logRequestResponse(wrappedRequest, wrappedResponse, duration);

            // 将响应内容写回原始响应
            wrappedResponse.copyBodyToResponse();
        }
    }

    /**
     * 判断是否应该跳过日志记录
     */
    private boolean shouldSkipLogging(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        return getExcludePaths().stream().anyMatch(path -> requestURI.startsWith(path.trim()));
    }

    /**
     * 记录请求响应信息
     */
    private void logRequestResponse(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response,
            long duration) {
        String method = request.getMethod();
        String uri = request.getRequestURI();
        String queryString = request.getQueryString();
        int status = response.getStatus();

        // 构建完整URL
        String fullUrl = uri;
        if (queryString != null && !queryString.isEmpty()) {
            String filteredQuery = SensitiveDataFilter.filterQueryString(queryString);
            fullUrl = uri + "?" + filteredQuery;
        }

        // 获取请求体
        String requestBody = getRequestBody(request);
        if (requestBody != null && !requestBody.trim().isEmpty()) {
            requestBody = SensitiveDataFilter.filterSensitiveData(requestBody);
            requestBody = SensitiveDataFilter.truncateContent(requestBody, maxBodySize);
        } else {
            requestBody = "";
        }

        // 获取响应体
        String responseBody = getResponseBody(response);
        if (responseBody != null && !responseBody.trim().isEmpty()) {
            responseBody = SensitiveDataFilter.filterSensitiveData(responseBody);
            responseBody = SensitiveDataFilter.truncateContent(responseBody, maxBodySize);
        } else {
            responseBody = "";
        }

        // 记录日志 - 修复参数对应关系
        log.info("API调用 | {} {} | 状态:{} | 耗时:{}ms", method, fullUrl, status, duration);
        log.info("请求体: {}", requestBody);
        log.info("响应体: {}", responseBody);
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(ContentCachingRequestWrapper request) {
        try {
            byte[] content = request.getContentAsByteArray();
            if (content.length > 0) {
                return new String(content, request.getCharacterEncoding());
            }
        } catch (Exception e) {
            log.debug("获取请求体失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取响应体内容
     */
    private String getResponseBody(ContentCachingResponseWrapper response) {
        try {
            byte[] content = response.getContentAsByteArray();
            if (content.length > 0) {
                return new String(content, response.getCharacterEncoding());
            }
        } catch (Exception e) {
            log.debug("获取响应体失败: {}", e.getMessage());
        }
        return null;
    }
}