package com.lct.finance.filter;

import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

/**
 * TraceId 过滤器
 * 为每个请求生成唯一的 TraceId 并放入 MDC 中
 * 
 * <AUTHOR> Finance Team
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TraceIdFilter extends OncePerRequestFilter {

    private static final String TRACE_ID = "TraceId";
    private static final String TRACE_ID_HEADER = "X-Trace-Id";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hai<PERSON> filterChain)
            throws ServletException, IOException {
        try {
            // 首先尝试从请求头中获取 TraceId
            String traceId = request.getHeader(TRACE_ID_HEADER);
            
            // 如果请求头中没有 TraceId，则生成一个新的
            if (traceId == null || traceId.isEmpty()) {
                traceId = generateTraceId();
            }
            
            // 将 TraceId 放入 MDC
            MDC.put(TRACE_ID, traceId);
            
            // 将 TraceId 添加到响应头，方便客户端进行跟踪
            response.addHeader(TRACE_ID_HEADER, traceId);
            
            // 继续执行过滤器链
            filterChain.doFilter(request, response);
        } finally {
            // 请求结束后清理 MDC
            MDC.remove(TRACE_ID);
        }
    }

    /**
     * 生成 TraceId
     * 使用 UUID 的简化版本，去掉了连字符并截取一部分
     */
    private String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
} 