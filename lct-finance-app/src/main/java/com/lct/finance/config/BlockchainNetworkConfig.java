package com.lct.finance.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * 区块链网络配置类
 * 根据不同环境加载对应的支持网络列表和合约地址
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "app.blockchain")
public class BlockchainNetworkConfig {

    /**
     * 支持的网络列表
     */
    private List<String> supportedNetworks;

    /**
     * 网络映射配置
     */
    private Map<String, NetworkMapping> networkMappings;
    
    /**
     * BSC链LCT合约地址
     */
    @Value("${blockchain.bsc.lct-contract-address}")
    private String bscLctContractAddress;

    /**
     * BSC链USDT合约地址
     */
    @Value("${blockchain.bsc.usdt-contract-address}")
    private String bscUsdtContractAddress;

    /**
     * TRON链LCT合约地址
     */
    @Value("${blockchain.tron.lct-contract-address:}")
    private String tronLctContractAddress;

    /**
     * TRON链USDT合约地址
     */
    @Value("${blockchain.tron.usdt-contract-address:}")
    private String tronUsdtContractAddress;

    @PostConstruct
    public void init() {
        log.info("区块链网络配置初始化完成:");
        log.info("支持的网络: {}", supportedNetworks);
        if (networkMappings != null) {
            networkMappings.forEach((network, mapping) -> 
                log.info("网络 {} -> 名称: {}, 地址类型: {}, 签名类型: {}", 
                    network, mapping.getName(), mapping.getAddressType(), mapping.getSignatureType())
            );
        }
        
        // 输出合约地址信息
        log.info("BSC LCT合约地址: {}", bscLctContractAddress);
        log.info("BSC USDT合约地址: {}", bscUsdtContractAddress);
        log.info("TRON LCT合约地址: {}", tronLctContractAddress);
        log.info("TRON USDT合约地址: {}", tronUsdtContractAddress);
    }

    /**
     * 检查网络是否被支持
     * 
     * @param network 网络名称
     * @return 是否支持
     */
    public boolean isSupportedNetwork(String network) {
        if (network == null || supportedNetworks == null) {
            return false;
        }
        return supportedNetworks.contains(network.toUpperCase());
    }

    /**
     * 获取网络映射信息
     * 
     * @param network 网络名称
     * @return 网络映射信息
     */
    public NetworkMapping getNetworkMapping(String network) {
        if (network == null || networkMappings == null) {
            return null;
        }
        return networkMappings.get(network.toUpperCase());
    }

    /**
     * 获取网络的地址类型
     * 
     * @param network 网络名称
     * @return 地址类型 (ETH/TRX)
     */
    public String getAddressType(String network) {
        NetworkMapping mapping = getNetworkMapping(network);
        return mapping != null ? mapping.getAddressType() : null;
    }

    /**
     * 获取网络的签名类型
     * 
     * @param network 网络名称
     * @return 签名类型 (ETH/TRX)
     */
    public String getSignatureType(String network) {
        NetworkMapping mapping = getNetworkMapping(network);
        return mapping != null ? mapping.getSignatureType() : null;
    }

    /**
     * 获取网络的显示名称
     * 
     * @param network 网络名称
     * @return 显示名称
     */
    public String getNetworkDisplayName(String network) {
        NetworkMapping mapping = getNetworkMapping(network);
        return mapping != null ? mapping.getName() : network;
    }
    
    /**
     * 根据币种类型获取合约地址
     * 
     * @param coinType 币种类型 (1=LCT, 2=USDT-BSC, 3=USDT-TRC20)
     * @return 合约地址
     */
    public String getContractAddress(Integer coinType) {
        if (coinType == null) {
            return "";
        }
        
        switch (coinType) {
            case 1: // LCT
                return bscLctContractAddress;
            case 2: // USDT-BSC
                return bscUsdtContractAddress;
            case 3: // USDT-TRC20
                return tronUsdtContractAddress;
            default:
                return "";
        }
    }

    /**
     * 网络映射配置
     */
    @Data
    public static class NetworkMapping {
        /**
         * 网络显示名称
         */
        private String name;
        
        /**
         * 地址类型 (ETH/TRX)
         */
        private String addressType;
        
        /**
         * 签名类型 (ETH/TRX)
         */
        private String signatureType;
    }
} 