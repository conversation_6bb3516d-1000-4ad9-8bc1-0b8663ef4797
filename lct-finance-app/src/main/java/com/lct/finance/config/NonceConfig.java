package com.lct.finance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Nonce配置类
 * 用于配置nonce相关的参数，防止重放攻击
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.nonce")
public class NonceConfig {

    /**
     * nonce缓存key前缀
     */
    private String keyPrefix = "lct:nonce:";

    /**
     * nonce默认过期时间（秒）
     * 建议设置为时间窗口的2-3倍，确保在时间窗口内的nonce都能被正确缓存
     */
    private long defaultExpire = 1200; // 20分钟

    /**
     * 默认时间窗口（秒）
     * 用于验证nonce时间戳的有效性，防止过期的请求
     */
    private long defaultTimeWindow = 600; // 10分钟

    /**
     * 单个用户最大nonce数量
     * 防止恶意用户大量发送请求占用缓存空间
     */
    private long maxPerUser = 100;

    /**
     * 是否启用nonce验证
     * 开发环境可以设置为false，生产环境必须为true
     */
    private boolean enabled = true;

    /**
     * 是否启用严格模式
     * 严格模式下，任何nonce验证失败都会记录日志并可能触发安全告警
     */
    private boolean strictMode = true;

    /**
     * nonce清理任务执行间隔（秒）
     * 定期清理过期的nonce，释放缓存空间
     */
    private long cleanupInterval = 3600; // 1小时

    /**
     * 是否启用nonce统计
     * 启用后会统计nonce的使用情况，用于监控和分析
     */
    private boolean enableStatistics = true;

    /**
     * 统计数据保留时间（秒）
     */
    private long statisticsRetention = 86400; // 24小时

    /**
     * 获取完整的nonce key
     * 
     * @param userAddress 用户地址
     * @param nonce nonce值
     * @return 完整的Redis key
     */
    public String buildNonceKey(String userAddress, String nonce) {
        return keyPrefix + userAddress.toLowerCase() + ":" + nonce;
    }

    /**
     * 获取用户nonce统计key
     * 
     * @param userAddress 用户地址
     * @return 统计key
     */
    public String buildStatisticsKey(String userAddress) {
        return keyPrefix + "stats:" + userAddress.toLowerCase();
    }

    /**
     * 获取全局nonce统计key
     * 
     * @return 全局统计key
     */
    public String buildGlobalStatisticsKey() {
        return keyPrefix + "stats:global";
    }

    /**
     * 验证配置参数的合理性
     * 
     * @return 验证结果
     */
    public boolean validateConfig() {
        if (defaultExpire <= 0) {
            return false;
        }
        if (defaultTimeWindow <= 0) {
            return false;
        }
        if (maxPerUser <= 0) {
            return false;
        }
        if (defaultExpire < defaultTimeWindow) {
            // 过期时间应该大于等于时间窗口
            return false;
        }
        if (cleanupInterval <= 0) {
            return false;
        }
        return true;
    }

    /**
     * 获取配置摘要信息
     * 
     * @return 配置摘要
     */
    public String getConfigSummary() {
        return String.format(
            "NonceConfig{enabled=%s, keyPrefix='%s', defaultExpire=%ds, defaultTimeWindow=%ds, " +
            "maxPerUser=%d, strictMode=%s, cleanupInterval=%ds, enableStatistics=%s}",
            enabled, keyPrefix, defaultExpire, defaultTimeWindow, maxPerUser, 
            strictMode, cleanupInterval, enableStatistics
        );
    }
}