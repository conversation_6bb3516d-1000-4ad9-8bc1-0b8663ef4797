package com.lct.finance.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.HashMap;

/**
 * 特殊用户配置
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.special-user")
public class SpecialUserConfig {

    /**
     * 特殊用户的时间限制映射
     * 格式：用户ID -> 截止时间字符串 (yyyy-MM-dd HH:mm:ss)
     * 
     * 对应PHP代码：
     * if ($user->id == 2297) {
     *     $end_time = '2024-09-01 00:00:00';
     * }
     */
    private Map<Long, String> endTimeMap = new HashMap<>();

    /**
     * 获取指定用户的时间限制
     * 
     * @param userId 用户ID
     * @return 时间限制字符串，如果没有特殊限制则返回null
     */
    public String getEndTime(Long userId) {
        return endTimeMap.get(userId);
    }

    /**
     * 检查用户是否有特殊时间限制
     * 
     * @param userId 用户ID
     * @return true表示有特殊限制
     */
    public boolean hasEndTimeLimit(Long userId) {
        return endTimeMap.containsKey(userId);
    }
}