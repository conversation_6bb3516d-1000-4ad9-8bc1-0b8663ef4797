package com.lct.finance.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;

/**
 * Web3j配置类
 * 配置BSC链的Web3j实例，用于区块链交互和签名验证
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Configuration
public class Web3jConfig {

    /**
     * BSC链RPC节点URL
     */
    @Value("${web3j.bsc.rpc-url:https://bsc-dataseed1.binance.org/}")
    private String bscRpcUrl;

    /**
     * 连接超时时间（毫秒）
     */
    @Value("${web3j.connection-timeout:30000}")
    private long connectionTimeout;

    /**
     * 读取超时时间（毫秒）
     */
    @Value("${web3j.read-timeout:60000}")
    private long readTimeout;

    /**
     * 配置BSC链的Web3j实例
     * 
     * @return BSC链Web3j实例
     */
    @Bean("bscWeb3j")
    public Web3j bscWeb3j() {
        HttpService httpService = new HttpService(bscRpcUrl);
        return Web3j.build(httpService);
    }

    /**
     * 默认Web3j实例（使用BSC链）
     * 
     * @return 默认Web3j实例
     */
    @Bean
    public Web3j web3j() {
        return bscWeb3j();
    }

    /**
     * 获取BSC链RPC URL
     * 
     * @return BSC链RPC URL
     */
    public String getBscRpcUrl() {
        return bscRpcUrl;
    }

    /**
     * 获取连接超时时间
     * 
     * @return 连接超时时间（毫秒）
     */
    public long getConnectionTimeout() {
        return connectionTimeout;
    }

    /**
     * 获取读取超时时间
     * 
     * @return 读取超时时间（毫秒）
     */
    public long getReadTimeout() {
        return readTimeout;
    }
} 