package com.lct.finance.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lct.finance.model.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 自定义认证入口点
 * 处理认证失败的情况，返回统一的JSON错误响应
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public void commence(HttpServletRequest request, 
                        HttpServletResponse response,
                        AuthenticationException authException) throws IOException {
        
        String requestUri = request.getRequestURI();
        log.warn("认证失败访问: {} - {}", requestUri, authException.getMessage());

        // 设置响应状态和内容类型
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        // 构建错误响应
        ApiResponse<Object> errorResponse = ApiResponse.error(401, "用户未登录或token无效");
        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        
        // 写入响应
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }
}