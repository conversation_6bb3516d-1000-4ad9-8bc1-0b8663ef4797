package com.lct.finance.config;

import com.lct.finance.utils.MdcUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.util.*;
import java.util.regex.Matcher;

/**
 * SQL拦截器 - 优雅地记录SQL执行过程
 * 1. 记录SQL执行时间
 * 2. 关联TraceId，便于追踪
 * 3. 识别慢SQL
 * 4. 格式化SQL和参数
 * 5. 控制结果集大小
 * 
 * <AUTHOR> Finance Team
 */
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
@Slf4j
@Component
public class SqlInterceptor implements Interceptor {


    // 慢SQL阈值，单位毫秒
    private static final long SLOW_SQL_THRESHOLD = 500;
    // 最大结果集输出条数
    private static final int MAX_RESULT_SIZE = 5;
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 计算SQL执行前后的时间
        long startTime = System.currentTimeMillis();
        Object proceed = invocation.proceed();
        long endTime = System.currentTimeMillis();
        long costTime = endTime - startTime;

        String sqlId = null;
        String printSql = null;
        try {
            // 获取SQL语句ID（Mapper方法）
            MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
            sqlId = ms.getId();
            
            // 生成可读的SQL语句
            printSql = generateSql(invocation);
        } catch (Exception e) {
            log.error("获取SQL信息异常", e);
        } finally {
            // 获取TraceId
            String traceId = MdcUtil.getTraceId();
            traceId = StringUtils.hasText(traceId) ? traceId : "无";

            // 判断是否为慢SQL
            boolean isSlowSql = costTime >= SLOW_SQL_THRESHOLD;
            
            // 输出SQL执行信息
            if (isSlowSql) {
                log.warn("\n===== 慢SQL[{}ms] TraceId[{}] =====\n方法: {}\nSQL: {}", 
                        costTime, traceId, sqlId, printSql);
            } else {
                log.debug("\n===== SQL[{}ms] TraceId[{}] =====\n方法: {}\nSQL: {}", 
                        costTime, traceId, sqlId, printSql);
            }

            // 打印查询结果（有限制地）
            if (proceed != null) {
                if (proceed instanceof List) {
                    List<?> resultList = (List<?>) proceed;
                    int total = resultList.size();
                    
                    // 限制输出结果集大小
                    if (total > 0) {
                        if (total <= MAX_RESULT_SIZE) {
                            log.debug("\n结果集({})条: {}", total, toSimpleString(resultList));
                        } else {
                            // 只输出前几条和总数
                            List<?> subList = resultList.subList(0, MAX_RESULT_SIZE);
                            log.debug("\n结果集(showing {}/{})条: {}", MAX_RESULT_SIZE, total, toSimpleString(subList));
                        }
                    } else {
                        log.debug("查询数据为空");
                    }
                } else {
                    log.debug("\n单条结果: {}", toSimpleString(proceed));
                }
            } else {
                log.debug("\n无结果返回");
            }
        }
        return proceed;
    }

    /**
     * 将对象转换为简单字符串表示
     */
    private String toSimpleString(Object obj) {
        if (obj == null) {
            return "null";
        }
        
        if (obj instanceof Collection) {
            StringBuilder sb = new StringBuilder("[");
            Collection<?> collection = (Collection<?>) obj;
            int count = 0;
            for (Object item : collection) {
                if (count > 0) {
                    sb.append(", ");
                }
                sb.append(toSimpleString(item));
                count++;
            }
            sb.append("]");
            return sb.toString();
        }
        
        return obj.toString();
    }

    /**
     * 生成可读的SQL语句，将占位符替换为实际参数值
     */
    private static String generateSql(Invocation invocation) {
        MappedStatement statement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = null;
        if (invocation.getArgs().length > 1) {
            parameter = invocation.getArgs()[1];
        }
        Configuration configuration = statement.getConfiguration();
        BoundSql boundSql = statement.getBoundSql(parameter);

        // 获取参数对象和参数映射
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        
        // 获取SQL语句并格式化
        String sql = boundSql.getSql();
        sql = sql.replaceAll("[\\s]+", " ");

        // 检查是否为 COUNT 查询
        boolean isCountQuery = sql.trim().toUpperCase().startsWith("SELECT COUNT(");
        
        // 处理分页
        if (invocation.getArgs().length > 2) {
            RowBounds rowBounds = (RowBounds) invocation.getArgs()[2];
            if (rowBounds != null && rowBounds != RowBounds.DEFAULT) {
                sql += String.format(" /* LIMIT %d,%d */", rowBounds.getOffset(), rowBounds.getLimit());
            }
        }

        // 替换SQL中的参数占位符
        if (!ObjectUtils.isEmpty(parameterMappings) && !ObjectUtils.isEmpty(parameterObject)) {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            
            // 处理单一参数
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(getParameterValue(parameterObject)));
            } else {
                // 处理多个参数
                for (ParameterMapping parameterMapping : parameterMappings) {
                    String propertyName = parameterMapping.getProperty();
                    Object value = null;
                    
                    // 从对象中获取属性值
                    if (boundSql.hasAdditionalParameter(propertyName)) {
                        value = boundSql.getAdditionalParameter(propertyName);
                    } else if (parameterObject != null) {
                        MetaObject metaObject = configuration.newMetaObject(parameterObject);
                        if (metaObject.hasGetter(propertyName)) {
                            value = metaObject.getValue(propertyName);
                        }
                    }
                    
                    // 替换占位符
                    sql = sql.replaceFirst("\\?", value != null ? Matcher.quoteReplacement(getParameterValue(value)) : "NULL");
                }
            }
        }
        
        return sql;
    }

    /**
     * 格式化参数值，对不同类型做特殊处理
     */
    private static String getParameterValue(Object obj) {
        if (obj == null) {
            return "NULL";
        }
        
        // 字符串类型加引号
        if (obj instanceof String) {
            // 处理可能包含敏感信息的字段
            String value = obj.toString();
            if (isSensitiveField(value)) {
                return "'******'"; // 脱敏处理
            }
            return "'" + value.replace("'", "''") + "'"; // 转义单引号
        }
        
        // 日期类型格式化
        if (obj instanceof Date) {
            DateFormat dateFormat = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            return "'" + dateFormat.format((Date) obj) + "'";
        }
        
        // 集合类型
        if (obj instanceof Collection || obj.getClass().isArray()) {
            return "'[COLLECTION]'"; // 简化集合输出
        }
        
        return obj.toString();
    }
    
    /**
     * 判断是否是敏感字段（需要脱敏）
     */
    private static boolean isSensitiveField(String value) {
        // 根据实际情况判断是否是敏感字段，如密码、手机号等
        return value.toLowerCase().contains("password") || 
               value.toLowerCase().contains("pwd") ||
               value.toLowerCase().contains("secret");
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以通过properties配置插件参数
    }
} 