package com.lct.finance.aspect;

import com.lct.finance.annotation.RateLimit;
import org.aspectj.lang.JoinPoint;

/**
 * 限流Key生成器接口
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-20
 */
public interface RateLimitKeyGenerator {
    
    /**
     * 根据注解参数和切点信息生成限流的key
     *
     * @param rateLimit 限流注解信息
     * @param joinPoint 切点信息
     * @return 生成的限流key
     */
    String generate(RateLimit rateLimit, JoinPoint joinPoint);
} 