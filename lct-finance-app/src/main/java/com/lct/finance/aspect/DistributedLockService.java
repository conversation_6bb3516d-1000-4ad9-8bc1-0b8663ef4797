package com.lct.finance.aspect;

import com.lct.finance.annotation.PreventDuplicateSubmit;
import com.lct.finance.context.UserContext;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.model.entity.User;
import com.lct.finance.utils.ClientIpUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 防重复提交切面
 * 基于Redis实现的分布式锁，用于防止前端重复提交
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-30
 */
@Slf4j
@Aspect
@Component
public class DistributedLockService {

    @Autowired
    @Qualifier("lockRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    private static final String LOCK_PREFIX = "prevent_duplicate:";
    private final ExpressionParser parser = new SpelExpressionParser();

    /**
     * 防重复提交切面
     */
    @Around("@annotation(preventDuplicateSubmit)")
    public Object preventDuplicateSubmit(ProceedingJoinPoint joinPoint, PreventDuplicateSubmit preventDuplicateSubmit) throws Throwable {
        // 生成锁的key
        String lockKey = generateLockKey(joinPoint, preventDuplicateSubmit);
        String lockValue = generateLockValue();
        
        log.debug("尝试获取防重复提交锁: {}", lockKey);
        
        // 尝试获取锁
        Boolean success = redisTemplate.opsForValue().setIfAbsent(
            lockKey, 
            lockValue, 
            preventDuplicateSubmit.expireTime(), 
            TimeUnit.SECONDS
        );
        
        if (!Boolean.TRUE.equals(success)) {
            log.warn("检测到重复提交，拒绝执行: {}", lockKey);
            throw new BusinessException(preventDuplicateSubmit.message());
        }
        
        try {
            log.debug("成功获取防重复提交锁，开始执行业务逻辑: {}", lockKey);
            return joinPoint.proceed();
        } finally {
            // 释放锁
            try {
                redisTemplate.delete(lockKey);
                log.debug("释放防重复提交锁: {}", lockKey);
            } catch (Exception e) {
                log.error("释放防重复提交锁失败: {}", lockKey, e);
            }
        }
    }

    /**
     * 生成锁的key
     */
    private String generateLockKey(ProceedingJoinPoint joinPoint, PreventDuplicateSubmit annotation) {
        StringBuilder keyBuilder = new StringBuilder(LOCK_PREFIX);
        
        // 如果有自定义SpEL表达式
        if (!annotation.keyExpression().isEmpty()) {
            String customKey = parseSpelExpression(annotation.keyExpression(), joinPoint);
            keyBuilder.append(customKey);
        } else {
            // 使用默认key生成策略
            
            // 1. key前缀（如果没有指定则使用方法名）
            String prefix = annotation.keyPrefix();
            if (prefix.isEmpty()) {
                MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                Method method = signature.getMethod();
                prefix = method.getDeclaringClass().getSimpleName() + "." + method.getName();
            }
            keyBuilder.append(prefix);
            
            // 2. 包含用户信息
            if (annotation.includeUser()) {
                try {
                    User currentUser = UserContext.getCurrentUser();
                    if (currentUser != null) {
                        keyBuilder.append(":user:").append(currentUser.getId());
                    }
                } catch (Exception e) {
                    log.debug("获取当前用户信息失败，跳过用户信息: {}", e.getMessage());
                }
            }
            
            // 3. 包含IP信息
            if (annotation.includeIp()) {
                String clientIp = getClientIp();
                if (clientIp != null) {
                    keyBuilder.append(":ip:").append(clientIp);
                }
            }
        }
        
        return keyBuilder.toString();
    }

    /**
     * 解析SpEL表达式
     */
    private String parseSpelExpression(String spelExpression, ProceedingJoinPoint joinPoint) {
        try {
            Expression expression = parser.parseExpression(spelExpression);
            EvaluationContext context = new StandardEvaluationContext();
            
            // 设置方法参数
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            String[] paramNames = signature.getParameterNames();
            Object[] args = joinPoint.getArgs();
            
            if (paramNames != null && args != null) {
                for (int i = 0; i < paramNames.length && i < args.length; i++) {
                    context.setVariable(paramNames[i], args[i]);
                }
            }
            
            // 设置当前用户
            try {
                User currentUser = UserContext.getCurrentUser();
                context.setVariable("currentUser", currentUser);
                if (currentUser != null) {
                    context.setVariable("userId", currentUser.getId());
                    context.setVariable("userAddress", currentUser.getUserAddress());
                }
            } catch (Exception e) {
                log.debug("设置当前用户到SpEL上下文失败: {}", e.getMessage());
            }
            
            // 设置客户端IP
            String clientIp = getClientIp();
            context.setVariable("clientIp", clientIp);
            
            Object result = expression.getValue(context);
            return result != null ? result.toString() : "";
            
        } catch (Exception e) {
            log.error("解析SpEL表达式失败: {}", spelExpression, e);
            return spelExpression; // 如果解析失败，直接返回原表达式
        }
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return ClientIpUtils.getClientIp(request);
            }
        } catch (Exception e) {
            log.debug("获取客户端IP失败: {}", e.getMessage());
        }
        return null;
    }



    /**
     * 生成锁值
     */
    private String generateLockValue() {
        return Thread.currentThread().getId() + ":" + System.currentTimeMillis();
    }

    /**
     * 手动清理过期的锁（可用于定时任务）
     */
    public void cleanExpiredLocks() {
        try {
            // 这里可以实现清理逻辑，但由于Redis会自动过期，通常不需要
            log.debug("清理过期锁任务执行完成");
        } catch (Exception e) {
            log.error("清理过期锁失败", e);
        }
    }

    /**
     * 获取当前锁的统计信息
     */
    public java.util.Map<String, Object> getLockStats() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        
        try {
            java.util.Set<String> lockKeys = redisTemplate.keys(LOCK_PREFIX + "*");
            int lockCount = lockKeys != null ? lockKeys.size() : 0;
            
            stats.put("currentLockCount", lockCount);
            stats.put("lockPrefix", LOCK_PREFIX);
            
            if (lockKeys != null && !lockKeys.isEmpty()) {
                stats.put("activeLocks", lockKeys);
            }
            
        } catch (Exception e) {
            log.error("获取锁统计信息异常", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
} 