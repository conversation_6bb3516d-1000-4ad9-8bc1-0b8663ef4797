package com.lct.finance.aspect;

import com.lct.finance.annotation.RateLimit;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.service.impl.LeakyBucketRateLimiterService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 限流切面，拦截带有@RateLimit注解的方法
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-20
 */
@Slf4j
@Aspect
@Component
public class RateLimitAspect {
    

    private final RateLimitKeyGenerator keyGenerator;
    private final LeakyBucketRateLimiterService leakyBucketRateLimiterService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 构造函数，注入依赖
     */
    public RateLimitAspect(
            RateLimitKeyGenerator keyGenerator, 
            LeakyBucketRateLimiterService leakyBucketRateLimiterService,
            RedisTemplate<String, Object> redisTemplate) {
        this.keyGenerator = keyGenerator;
        this.leakyBucketRateLimiterService = leakyBucketRateLimiterService;
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 限流处理
     * 拦截带有@RateLimit注解的方法，进行限流判断
     */
    @Before("@annotation(com.lct.finance.annotation.RateLimit)")
    public void doBefore(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取限流注解
        RateLimit rateLimit = method.getAnnotation(RateLimit.class);
        if (rateLimit == null) {
            return;
        }
        
        // 生成限流key
        String key = keyGenerator.generate(rateLimit, joinPoint);
        
        // 转换时间单位到秒
        int seconds = (int) rateLimit.timeUnit().toSeconds(rateLimit.time());
        
        // 根据限流算法选择不同的限流服务
        boolean allowed;
        switch (rateLimit.algorithm()) {
            case LEAKY_BUCKET:
                // 使用漏桶算法限流
                int rate = rateLimit.rate() > 0 ? rateLimit.rate() : Math.max(1, rateLimit.count() / 10);
                allowed = leakyBucketRateLimiterService.tryAcquireWithRate(key, rateLimit.count(), rate, seconds);
                log.debug("使用漏桶算法限流 - key: {}, 桶容量: {}, 漏水速率: {}/s, 过期时间: {}s", 
                        key, rateLimit.count(), rate, seconds);
                break;
            case TOKEN_BUCKET:
                // 目前Token桶算法尚未实现，暂时使用计数器
                log.warn("Token桶算法尚未实现，将使用计数器算法 - key: {}", key);
                // 后续可以扩展
            case COUNTER:
            default:
                // 使用计数器算法限流
                allowed = counterRateLimiter(key, rateLimit.count(), seconds);
                log.debug("使用计数器算法限流 - key: {}, 最大请求数: {}, 时间窗口: {}s", 
                        key, rateLimit.count(), seconds);
                break;
        }
        
        // 如果被限流，则抛出限流异常
        if (!allowed) {
            log.warn("接口被限流: {}", key);
            throw new BusinessException(rateLimit.message());
        }
    }
    
    /**
     * 简单的计数器算法实现，使用Redis
     * 
     * @param key 限流的key
     * @param count 最大请求数
     * @param time 时间窗口（秒）
     * @return 是否允许请求
     */
    private boolean counterRateLimiter(String key, int count, int time) {
        try {
            // 获取当前计数
            Long currentCount = redisTemplate.opsForValue().increment(key, 1);
            
            // 如果是第一次请求，设置过期时间
            if (currentCount != null && currentCount == 1) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            
            // 判断是否超过限制
            return currentCount != null && currentCount <= count;
        } catch (Exception e) {
            // Redis出错，允许访问
            log.error("计数器限流器错误: {}", e.getMessage(), e);
            return true;
        }
    }
} 