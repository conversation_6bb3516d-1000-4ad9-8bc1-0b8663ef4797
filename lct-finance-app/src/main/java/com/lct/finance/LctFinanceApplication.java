package com.lct.finance;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * LCT Finance 区块链金融应用主启动类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableTransactionManagement
@EnableCaching
@EnableScheduling
@EnableAsync
public class LctFinanceApplication {

    public static void main(String[] args) {
        SpringApplication.run(LctFinanceApplication.class, args);
        System.out.println("\n" +
            "=======================================================\n" +
            "  LCT Finance Backend Service Started Successfully\n" +
            "=======================================================\n" +
            "  Version: 1.0.0\n" +
            "  Author: LCT Team\n" +
            "=======================================================\n");
    }
} 