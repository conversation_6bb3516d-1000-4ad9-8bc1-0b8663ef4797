package com.lct.finance.security;

import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * 自定义Bearer Token解析器：
 * - Authorization: Bearer xxx
 * - token 头（可带/不带 Bearer 前缀）
 * - token 查询参数（可带/不带 Bearer 前缀）
 */
public class HeaderOrParamBearerTokenResolver implements BearerTokenResolver {
    @Override
    public String resolve(HttpServletRequest request) {
        String authorization = request.getHeader("Authorization");
        if (StringUtils.hasText(authorization) && authorization.startsWith("Bearer ")) {
            return authorization.substring(7);
        }

        String tokenHeader = request.getHeader("token");
        if (StringUtils.hasText(tokenHeader)) {
            String v = tokenHeader.trim();
            return v.startsWith("Bearer ") ? v.substring(7) : v;
        }

        String tokenParam = request.getParameter("token");
        if (StringUtils.hasText(tokenParam)) {
            String v = tokenParam.trim();
            return v.startsWith("Bearer ") ? v.substring(7) : v;
        }

        return null;
    }
}


