package com.lct.finance.security;

import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Base64;

/**
 * 统一的JWT密钥提供器
 * - 支持明文密钥与 base64: 前缀密钥
 * - 确保HS512所需的最小长度（64字节）
 */
@Slf4j
@Component
public class JwtKeyProvider {

    @Value("${jwt.secret:lct-finance-secret-key-2024-very-long-secret-for-security-must-be-at-least-512-bits}")
    private String jwtSecret;

    private static final String DEFAULT_FALLBACK_SECRET =
            "lct-finance-secret-key-2024-very-long-secret-for-security-must-be-at-least-512-bits-long-to-meet-hs512-requirements";

    public byte[] getKeyBytes() {
        String configured = jwtSecret != null ? jwtSecret.trim() : "";
        byte[] keyBytes;
        if (configured.startsWith("base64:")) {
            String b64 = configured.substring("base64:".length());
            keyBytes = Base64.getDecoder().decode(b64);
        } else {
            keyBytes = configured.getBytes();
        }

        if (keyBytes.length < 64) {
            log.warn("JWT密钥长度不足，使用默认安全密钥");
            keyBytes = DEFAULT_FALLBACK_SECRET.getBytes();
        }

        return keyBytes;
    }

    public SecretKey getSecretKeyHmacSha512() {
        return Keys.hmacShaKeyFor(getKeyBytes());
    }
}


