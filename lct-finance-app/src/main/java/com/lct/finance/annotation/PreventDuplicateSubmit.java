package com.lct.finance.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 防重复提交注解
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-30
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PreventDuplicateSubmit {
    
    /**
     * 锁的过期时间（秒），默认5秒
     */
    int expireTime() default 5;
    
    /**
     * 锁的key前缀，默认为方法名
     */
    String keyPrefix() default "";
    
    /**
     * 是否包含用户信息在key中，默认true
     */
    boolean includeUser() default true;
    
    /**
     * 是否包含IP信息在key中，默认false
     */
    boolean includeIp() default false;
    
    /**
     * 自定义错误消息
     */
    String message() default "请勿重复提交";
    
    /**
     * SpEL表达式，用于自定义key
     */
    String keyExpression() default "";
} 