package com.lct.finance.annotation;

import com.lct.finance.model.enums.LimitType;
import com.lct.finance.model.enums.LimitAlgorithm;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 限流注解
 * 用于控制接口访问频率，防止接口被恶意频繁调用
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-20
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimit {

    /**
     * 限流的key前缀
     */
    String prefix() default "rate_limit:";

    /**
     * 限流的key
     * 如果不指定，默认使用请求的URI作为key
     */
    String key() default "";

    /**
     * 限流时间窗口，默认60秒
     */
    int time() default 60;

    /**
     * 限流时间单位，默认秒
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 限流次数，默认100次
     * 对于漏桶算法，这表示桶的容量
     */
    int count() default 100;

    /**
     * 漏水速率（每秒处理多少请求）
     * 只在漏桶算法中使用，默认为0表示使用默认速率（count/10）
     */
    int rate() default 0;

    /**
     * 限流类型，默认通过请求URI限流
     */
    LimitType limitType() default LimitType.DEFAULT;
    
    /**
     * 限流算法，默认使用计数器算法
     */
    LimitAlgorithm algorithm() default LimitAlgorithm.COUNTER;

    /**
     * 限流提示消息
     */
    String message() default "操作频率过快，请稍后再试";
} 