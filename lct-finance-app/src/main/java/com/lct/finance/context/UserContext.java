package com.lct.finance.context;

import com.lct.finance.exception.BusinessException;
import com.lct.finance.model.entity.User;
import com.lct.finance.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

/**
 * 用户上下文管理器
 * 兼容旧版本API，内部使用Spring Security JWT认证
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class UserContext {

    private static ApplicationContext applicationContext;
    
    @Autowired
    public void setApplicationContext(ApplicationContext context) {
        UserContext.applicationContext = context;
    }

    /**
     * 获取用户服务
     */
    private static IUserService getUserService() {
        if (applicationContext == null) {
            throw new IllegalStateException("ApplicationContext未初始化");
        }
        return applicationContext.getBean(IUserService.class);
    }

    /**
     * ThreadLocal存储当前线程的用户信息（保留兼容性）
     * @deprecated 建议直接使用Spring Security认证信息
     */
    @Deprecated
    private static final ThreadLocal<User> USER_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * ThreadLocal存储当前线程的JWT Token（保留兼容性）
     * @deprecated 建议使用JwtUserDetailsService.getCurrentJwt()
     */
    @Deprecated
    private static final ThreadLocal<String> TOKEN_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 设置当前用户信息
     * @deprecated 使用Spring Security JWT，无需手动设置用户信息
     * 
     * @param user 用户信息
     */
    @Deprecated
    public static void setCurrentUser(User user) {
        if (user != null) {
            USER_THREAD_LOCAL.set(user);
            log.debug("设置当前用户上下文: userId={}, address={}", 
                     user.getId(), user.getUserAddress());
        }
    }

    /**
     * 获取当前用户信息
     * 
     * @return 当前用户信息，如果未登录则返回null
     */
    public static User getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication instanceof JwtAuthenticationToken) {
                JwtAuthenticationToken jwtAuth = (JwtAuthenticationToken) authentication;
                Jwt jwt = jwtAuth.getToken();
                
                // 从JWT的subject获取用户地址
                String userAddress = jwt.getSubject();
                if (userAddress == null || userAddress.trim().isEmpty()) {
                    log.debug("JWT token中未包含用户地址");
                    return null;
                }
                
                // 查询用户信息
                User user = getUserService().findByAddress(userAddress);
                if (user == null) {
                    log.debug("用户不存在: {}", maskAddress(userAddress));
                    return null;
                }
                
                // 检查用户状态
                if (user.getState() == null || user.getState() != 1) {
                    log.debug("用户状态异常: userId={}, state={}", user.getId(), user.getState());
                    return null;
                }
                
                return user;
            }
            
            // 回退到ThreadLocal方式
            return USER_THREAD_LOCAL.get();
            
        } catch (Exception e) {
            log.debug("获取用户信息失败，回退到ThreadLocal方式", e);
            return USER_THREAD_LOCAL.get();
        }
    }

    /**
     * 获取当前用户ID
     * 
     * @return 当前用户ID，如果未登录则返回null
     */
    public static Long getCurrentUserId() {
        User user = getCurrentUser();
        return user != null ? user.getId() : null;
    }

    /**
     * 获取当前用户地址
     * 
     * @return 当前用户地址，如果未登录则返回null
     */
    public static String getCurrentUserAddress() {
        User user = getCurrentUser();
        return user != null ? user.getUserAddress() : null;
    }

    /**
     * 获取当前用户链类型
     * 
     * @return 当前用户链类型，如果未登录则返回null
     */
    public static String getCurrentUserChain() {
        User user = getCurrentUser();
        return user != null ? user.getChain() : null;
    }

    /**
     * 设置当前JWT Token
     * @deprecated 使用Spring Security JWT，无需手动设置Token
     * 
     * @param token JWT Token
     */
    @Deprecated
    public static void setCurrentToken(String token) {
        if (token != null && !token.trim().isEmpty()) {
            TOKEN_THREAD_LOCAL.set(token);
            log.debug("设置当前Token上下文");
        }
    }

    /**
     * 获取当前JWT Token
     * 
     * @return 当前JWT Token，如果不存在则返回null
     */
    public static String getCurrentToken() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication instanceof JwtAuthenticationToken) {
                JwtAuthenticationToken jwtAuth = (JwtAuthenticationToken) authentication;
                Jwt jwt = jwtAuth.getToken();
                return jwt.getTokenValue();
            }
            // 回退到ThreadLocal方式
            return TOKEN_THREAD_LOCAL.get();
        } catch (Exception e) {
            log.debug("获取JWT Token失败，回退到ThreadLocal方式", e);
            return TOKEN_THREAD_LOCAL.get();
        }
    }

    /**
     * 检查当前是否已登录
     * 
     * @return 如果已登录返回true，否则返回false
     */
    public static boolean isAuthenticated() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            return authentication != null && 
                   authentication.isAuthenticated() && 
                   authentication instanceof JwtAuthenticationToken;
        } catch (Exception e) {
            log.debug("检查认证状态失败，回退到ThreadLocal方式", e);
            return USER_THREAD_LOCAL.get() != null;
        }
    }

    /**
     * 检查当前用户是否为指定地址的用户
     * 
     * @param address 要检查的地址
     * @return 如果是指定用户返回true，否则返回false
     */
    public static boolean isCurrentUser(String address) {
        String currentAddress = getCurrentUserAddress();
        return currentAddress != null && currentAddress.equalsIgnoreCase(address);
    }

    /**
     * 清除当前线程的用户上下文
     * @deprecated Spring Security JWT模式下无需手动清除
     */
    @Deprecated
    public static void clear() {
        User user = USER_THREAD_LOCAL.get();
        if (user != null) {
            log.debug("清除用户上下文: userId={}, address={}", 
                     user.getId(), user.getUserAddress());
        }
        USER_THREAD_LOCAL.remove();
        TOKEN_THREAD_LOCAL.remove();
    }

    /**
     * 获取当前用户的简要信息（用于日志）
     * 
     * @return 用户简要信息字符串
     */
    public static String getCurrentUserInfo() {
        User user = getCurrentUser();
        if (user == null) {
            return "未登录用户";
        }
        return String.format("用户[ID:%d, 地址:%s, 链:%s]", 
                           user.getId(), 
                           maskAddress(user.getUserAddress()), 
                           user.getChain());
    }

    /**
     * 掩码地址（用于日志安全）
     * 
     * @param address 原始地址
     * @return 掩码后的地址
     */
    private static String maskAddress(String address) {
        if (address == null || address.length() < 10) {
            return address;
        }
        return address.substring(0, 6) + "****" + address.substring(address.length() - 4);
    }

    /**
     * 要求当前必须已登录，否则抛出异常
     * 
     * @throws RuntimeException 如果未登录
     */
    public static void requireAuthenticated() {
        if (!isAuthenticated()) {
            throw new BusinessException("用户未登录");
        }
    }

    /**
     * 要求当前用户必须是指定地址的用户，否则抛出异常
     * 
     * @param address 要求的用户地址
     * @throws RuntimeException 如果不是指定用户
     */
    public static void requireCurrentUser(String address) {
        requireAuthenticated();
        if (!isCurrentUser(address)) {
            throw new BusinessException("无权限访问其他用户的数据");
        }
    }
}