package com.lct.finance.utils;

import com.lct.finance.model.constants.WithdrawalConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.web3j.crypto.ECDSASignature;
import org.web3j.crypto.Hash;
import org.web3j.crypto.Keys;
import org.web3j.crypto.Sign;
import org.web3j.utils.Numeric;

import java.math.BigInteger;
import java.util.regex.Pattern;

/**
 * Web3工具类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class Web3Utils {

    // BSC地址正则表达式
    private static final Pattern BSC_ADDRESS_PATTERN = Pattern.compile("^0x[a-fA-F0-9]{40}$");
    
    // TRON地址正则表达式
    private static final Pattern TRON_ADDRESS_PATTERN = Pattern.compile("^T[A-Za-z1-9]{33}$");

    /**
     * 验证钱包地址格式
     */
    public boolean isValidAddress(String address, String chain) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }
        
        switch (chain.toUpperCase()) {
            case WithdrawalConstants.ChainType.BSC:
                return BSC_ADDRESS_PATTERN.matcher(address).matches();
            case "TRON":
            case "TRX":
                return TRON_ADDRESS_PATTERN.matcher(address).matches();
            default:
                return false;
        }
    }

    /**
     * 验证签名
     */
    public boolean verifySignature(String message, String signature, String address, String chain) {
        try {
            switch (chain.toUpperCase()) {
                case WithdrawalConstants.ChainType.BSC:
                    return verifyEthereumSignature(message, signature, address);
                case "TRON":
                case "TRX":
                    return verifyTronSignature(message, signature, address);
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("签名验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证以太坊/BSC签名
     */
    private boolean verifyEthereumSignature(String message, String signature, String address) {
        try {
            // 添加以太坊消息前缀
            String prefixedMessage = "\u0019Ethereum Signed Message:\n" + message.length() + message;
            byte[] messageHash = Hash.sha3(prefixedMessage.getBytes());
            
            // 解析签名
            byte[] signatureBytes = Numeric.hexStringToByteArray(signature);
            if (signatureBytes.length != 65) {
                return false;
            }
            
            byte v = signatureBytes[64];
            if (v < 27) {
                v += 27;
            }
            
            Sign.SignatureData signatureData = new Sign.SignatureData(
                v,
                java.util.Arrays.copyOfRange(signatureBytes, 0, 32),
                java.util.Arrays.copyOfRange(signatureBytes, 32, 64)
            );
            
            // 恢复公钥
            BigInteger publicKey = Sign.signedMessageHashToKey(messageHash, signatureData);
            String recoveredAddress = "0x" + Keys.getAddress(publicKey);
            
            return recoveredAddress.equalsIgnoreCase(address);
            
        } catch (Exception e) {
            log.error("以太坊签名验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证TRON签名
     */
    private boolean verifyTronSignature(String message, String signature, String address) {
        try {
            // TRON签名验证逻辑
            // 这里需要使用TRON的签名验证库
            // 暂时返回true，实际项目中需要实现具体的TRON签名验证
            log.warn("TRON签名验证暂未实现，需要集成TRON签名验证库");
            return true;
            
        } catch (Exception e) {
            log.error("TRON签名验证失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成消息哈希
     */
    public String generateMessageHash(String message) {
        try {
            byte[] messageBytes = message.getBytes();
            byte[] hash = Hash.sha3(messageBytes);
            return Numeric.toHexString(hash);
        } catch (Exception e) {
            log.error("生成消息哈希失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证地址校验和
     */
    public boolean isValidChecksumAddress(String address) {
        try {
            if (!BSC_ADDRESS_PATTERN.matcher(address).matches()) {
                return false;
            }
            
            // 验证校验和
            String addressHash = Numeric.cleanHexPrefix(Hash.sha3String(address.toLowerCase()));
            
            for (int i = 0; i < 40; i++) {
                char addressChar = address.charAt(i + 2);
                char hashChar = addressHash.charAt(i);
                
                if (Character.isLetter(addressChar)) {
                    int hashValue = Integer.parseInt(String.valueOf(hashChar), 16);
                    if ((hashValue >= 8 && Character.isLowerCase(addressChar)) ||
                        (hashValue < 8 && Character.isUpperCase(addressChar))) {
                        return false;
                    }
                }
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("校验和验证失败: {}", e.getMessage(), e);
            return false;
        }
    }
} 