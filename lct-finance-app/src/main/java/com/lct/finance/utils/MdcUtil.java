package com.lct.finance.utils;

import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.concurrent.ListenableFuture;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * MDC 工具类
 * 用于在异步线程间传递 MDC 上下文，确保日志中的 TraceId 等信息能够正确传递
 * 
 * <AUTHOR> Finance Team
 */
public class MdcUtil {

    private static final String TRACE_ID = "TraceId";

    /**
     * 获取当前线程的 TraceId
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID);
    }

    /**
     * 设置当前线程的 TraceId
     */
    public static void setTraceId(String traceId) {
        if (traceId != null && !traceId.isEmpty()) {
            MDC.put(TRACE_ID, traceId);
        }
    }

    /**
     * 清除当前线程的 TraceId
     */
    public static void clearTraceId() {
        MDC.remove(TRACE_ID);
    }

    /**
     * 创建包装了 MDC 上下文的 Runnable
     */
    public static Runnable wrap(Runnable runnable) {
        // 获取当前线程的 MDC 上下文
        Map<String, String> context = MDC.getCopyOfContextMap();
        
        return () -> {
            // 获取原来的 MDC 上下文
            Map<String, String> oldContext = MDC.getCopyOfContextMap();
            try {
                // 设置新线程的 MDC 上下文
                if (context != null) {
                    MDC.setContextMap(context);
                }
                // 执行原始任务
                runnable.run();
            } finally {
                // 恢复原来的 MDC 上下文
                if (oldContext != null) {
                    MDC.setContextMap(oldContext);
                } else {
                    MDC.clear();
                }
            }
        };
    }

    /**
     * 创建包装了 MDC 上下文的 Callable
     */
    public static <T> Callable<T> wrap(Callable<T> callable) {
        Map<String, String> context = MDC.getCopyOfContextMap();
        
        return () -> {
            Map<String, String> oldContext = MDC.getCopyOfContextMap();
            try {
                if (context != null) {
                    MDC.setContextMap(context);
                }
                return callable.call();
            } finally {
                if (oldContext != null) {
                    MDC.setContextMap(oldContext);
                } else {
                    MDC.clear();
                }
            }
        };
    }

    /**
     * 创建支持 MDC 传递的线程池执行器装饰器类
     */
    public static class ThreadPoolTaskExecutorMdcWrapper extends ThreadPoolTaskExecutor {
        @Override
        public void execute(Runnable task) {
            super.execute(MdcUtil.wrap(task));
        }

        @Override
        public Future<?> submit(Runnable task) {
            return super.submit(MdcUtil.wrap(task));
        }

        @Override
        public <T> Future<T> submit(Callable<T> task) {
            return super.submit(MdcUtil.wrap(task));
        }

        @Override
        public ListenableFuture<?> submitListenable(Runnable task) {
            return super.submitListenable(MdcUtil.wrap(task));
        }

        @Override
        public <T> ListenableFuture<T> submitListenable(Callable<T> task) {
            return super.submitListenable(MdcUtil.wrap(task));
        }
    }
} 