package com.lct.finance.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁工具类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class RedisLockUtil {

    @Autowired
    @Qualifier("lockRedisTemplate")
    private RedisTemplate<String, String> lockRedisTemplate;

    // Lua脚本：释放锁时验证锁的拥有者
    private static final String UNLOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";

    private static final DefaultRedisScript<Long> UNLOCK_LUA_SCRIPT = new DefaultRedisScript<>(UNLOCK_SCRIPT, Long.class);

    /**
     * 尝试获取分布式锁
     * 
     * @param key 锁的键
     * @param expireTime 过期时间（秒）
     * @return 锁的值（用于释放锁时验证），获取失败返回null
     */
    public String tryLock(String key, long expireTime) {
        try {
            String lockValue = UUID.randomUUID().toString();
            Boolean success = lockRedisTemplate.opsForValue()
                .setIfAbsent(key, lockValue, expireTime, TimeUnit.SECONDS);
            
            if (Boolean.TRUE.equals(success)) {
                log.debug("获取分布式锁成功: key={}, value={}, expireTime={}", key, lockValue, expireTime);
                return lockValue;
            } else {
                log.debug("获取分布式锁失败: key={}", key);
                return null;
            }
        } catch (Exception e) {
            log.error("获取分布式锁异常: key={}", key, e);
            return null;
        }
    }

    /**
     * 释放分布式锁
     * 
     * @param key 锁的键
     * @param lockValue 锁的值（用于验证锁的拥有者）
     * @return 是否释放成功
     */
    public boolean releaseLock(String key, String lockValue) {
        try {
            Long result = lockRedisTemplate.execute(UNLOCK_LUA_SCRIPT, 
                Collections.singletonList(key), lockValue);
            boolean success = result != null && result > 0;
            
            if (success) {
                log.debug("释放分布式锁成功: key={}, value={}", key, lockValue);
            } else {
                log.warn("释放分布式锁失败: key={}, value={}", key, lockValue);
            }
            
            return success;
        } catch (Exception e) {
            log.error("释放分布式锁异常: key={}, value={}", key, lockValue, e);
            return false;
        }
    }

    /**
     * 生成销毁操作的锁键
     * 
     * @param userId 用户ID
     * @return 锁键
     */
    public String getBurnLockKey(Long userId) {
        return "burn:lock:" + userId;
    }

    /**
     * 生成交易哈希去重的缓存键
     * 
     * @param txhash 交易哈希
     * @return 缓存键
     */
    public String getTxHashCacheKey(String txhash) {
        return "burn:txhash:" + txhash;
    }

    /**
     * 检查交易哈希是否已存在（用于去重）
     * 
     * @param txhash 交易哈希
     * @return 是否已存在
     */
    public boolean isTxHashExists(String txhash) {
        try {
            String cacheKey = getTxHashCacheKey(txhash);
            Boolean exists = lockRedisTemplate.hasKey(cacheKey);
            return Boolean.TRUE.equals(exists);
        } catch (Exception e) {
            log.error("检查交易哈希缓存异常: txhash={}", txhash, e);
            return false;
        }
    }

    /**
     * 设置交易哈希缓存（用于去重）
     * 
     * @param txhash 交易哈希
     * @param expireTime 过期时间（秒）
     */
    public void setTxHashCache(String txhash, long expireTime) {
        try {
            String cacheKey = getTxHashCacheKey(txhash);
            lockRedisTemplate.opsForValue().set(cacheKey, "1", expireTime, TimeUnit.SECONDS);
            log.debug("设置交易哈希缓存: txhash={}, expireTime={}", txhash, expireTime);
        } catch (Exception e) {
            log.error("设置交易哈希缓存异常: txhash={}", txhash, e);
        }
    }
} 