package com.lct.finance.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 敏感信息过滤工具类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
public class SensitiveDataFilter {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 敏感字段列表
     */
    private static final Set<String> SENSITIVE_FIELDS = new HashSet<>(Arrays.asList(
            "password", "pwd", "token", "accesstoken", "refreshtoken",
            "sign", "signature", "privatekey", "secret", "key",
            "authorization", "captcha", "code", "verifycode"
    ));

    /**
     * 敏感字段替换值
     */
    private static final String MASK_VALUE = "***";

    /**
     * 过滤JSON字符串中的敏感信息
     * 
     * @param jsonString JSON字符串
     * @return 过滤后的JSON字符串
     */
    public static String filterSensitiveData(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return jsonString;
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            JsonNode filteredNode = filterJsonNode(jsonNode);
            return objectMapper.writeValueAsString(filteredNode);
        } catch (Exception e) {
            // 如果不是有效的JSON，直接返回原字符串
            log.debug("无法解析JSON字符串进行敏感信息过滤: {}", e.getMessage());
            return jsonString;
        }
    }

    /**
     * 递归过滤JsonNode中的敏感字段
     */
    private static JsonNode filterJsonNode(JsonNode node) {
        if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            ObjectNode filteredNode = objectMapper.createObjectNode();
            
            objectNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                JsonNode fieldValue = entry.getValue();
                
                if (isSensitiveField(fieldName)) {
                    filteredNode.put(fieldName, MASK_VALUE);
                } else {
                    filteredNode.set(fieldName, filterJsonNode(fieldValue));
                }
            });
            
            return filteredNode;
        } else if (node.isArray()) {
            com.fasterxml.jackson.databind.node.ArrayNode arrayNode = objectMapper.createArrayNode();
            for (int i = 0; i < node.size(); i++) {
                JsonNode arrayItem = node.get(i);
                arrayNode.add(filterJsonNode(arrayItem));
            }
            return arrayNode;
        }
        
        return node;
    }

    /**
     * 判断字段名是否为敏感字段
     */
    private static boolean isSensitiveField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        // 精确匹配或包含敏感关键词
        return SENSITIVE_FIELDS.contains(lowerFieldName) || 
               SENSITIVE_FIELDS.stream().anyMatch(sensitiveField -> 
                   lowerFieldName.contains(sensitiveField)
               );
    }

    /**
     * 过滤URL参数中的敏感信息
     * 
     * @param queryString URL参数字符串
     * @return 过滤后的参数字符串
     */
    public static String filterQueryString(String queryString) {
        if (queryString == null || queryString.trim().isEmpty()) {
            return queryString;
        }

        StringBuilder filteredQuery = new StringBuilder();
        String[] params = queryString.split("&");
        
        for (int i = 0; i < params.length; i++) {
            if (i > 0) {
                filteredQuery.append("&");
            }
            
            String param = params[i];
            String[] keyValue = param.split("=", 2);
            
            if (keyValue.length == 2) {
                String key = keyValue[0];
                String value = keyValue[1];
                
                if (isSensitiveField(key)) {
                    filteredQuery.append(key).append("=").append(MASK_VALUE);
                } else {
                    filteredQuery.append(param);
                }
            } else {
                filteredQuery.append(param);
            }
        }
        
        return filteredQuery.toString();
    }

    /**
     * 限制字符串长度，避免日志过长
     * 
     * @param content 内容
     * @param maxLength 最大长度
     * @return 截断后的内容
     */
    public static String truncateContent(String content, int maxLength) {
        if (content == null) {
            return null;
        }
        
        if (content.length() <= maxLength) {
            return content;
        }
        
        return content.substring(0, maxLength) + "...(truncated)";
    }
} 