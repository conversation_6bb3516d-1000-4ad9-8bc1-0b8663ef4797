package com.lct.finance.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

/**
 * 请求追踪上下文工具类
 * 用于管理请求的traceId，支持分布式追踪
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
public class TraceContext {

    /**
     * 请求头中的traceId字段名
     */
    public static final String TRACE_ID_HEADER = "X-Trace-Id";

    /**
     * MDC中的traceId字段名
     */
    public static final String TRACE_ID_MDC_KEY = "traceId";

    /**
     * ThreadLocal存储traceId
     */
    private static final ThreadLocal<String> TRACE_ID_HOLDER = new ThreadLocal<>();

    /**
     * 生成新的traceId
     * 
     * @return 新的traceId
     */
    public static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 设置当前线程的traceId
     * 
     * @param traceId traceId
     */
    public static void setTraceId(String traceId) {
        if (traceId != null && !traceId.trim().isEmpty()) {
            TRACE_ID_HOLDER.set(traceId);
            // 同时设置到MDC中，方便日志框架使用
            org.slf4j.MDC.put(TRACE_ID_MDC_KEY, traceId);
        }
    }

    /**
     * 获取当前线程的traceId
     * 
     * @return traceId，如果不存在则返回null
     */
    public static String getTraceId() {
        return TRACE_ID_HOLDER.get();
    }

    /**
     * 获取当前线程的traceId，如果不存在则生成新的
     * 
     * @return traceId
     */
    public static String getOrGenerateTraceId() {
        String traceId = getTraceId();
        if (traceId == null || traceId.trim().isEmpty()) {
            traceId = generateTraceId();
            setTraceId(traceId);
        }
        return traceId;
    }

    /**
     * 清理当前线程的traceId
     * 注意：在请求结束时必须调用此方法，避免内存泄漏
     */
    public static void clear() {
        TRACE_ID_HOLDER.remove();
        org.slf4j.MDC.remove(TRACE_ID_MDC_KEY);
    }

    /**
     * 检查traceId是否有效
     * 
     * @param traceId traceId
     * @return 是否有效
     */
    public static boolean isValidTraceId(String traceId) {
        return traceId != null && 
               !traceId.trim().isEmpty() && 
               traceId.length() >= 8 && 
               traceId.length() <= 64 &&
               traceId.matches("[a-zA-Z0-9]+");
    }
} 