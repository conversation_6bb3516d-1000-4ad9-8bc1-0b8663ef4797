package com.lct.finance.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 客户端IP获取工具类
 * 提供统一的客户端真实IP地址获取功能
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
public class ClientIpUtils {

    // IP地理位置缓存
    private static final Map<String, String> IP_LOCATION_CACHE = new ConcurrentHashMap<>();

    // IP地理位置查询的免费API服务列表
    private static final String[] IP_LOCATION_APIS = {
            "http://ip-api.com/json/{ip}?fields=country,regionName,city,status",
            "https://ipapi.co/{ip}/json/",
            "http://ipinfo.io/{ip}/json"
    };

    /**
     * 获取客户端真实IP地址
     * 支持多种代理服务器的Header检查
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址，如果无法获取则返回unknown
     */
    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest为空，无法获取客户端IP");
            return "unknown";
        }

        String ip = null;

        // 1. 检查X-Forwarded-For header (最常用的代理header)
        ip = request.getHeader("X-Forwarded-For");
        if (isValidIp(ip)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            if (ip.contains(",")) {
                ip = ip.split(",")[0].trim();
            }
            log.debug("从X-Forwarded-For获取到IP: {}", ip);
            return ip;
        }

        // 2. 检查X-Real-IP header (Nginx代理常用)
        ip = request.getHeader("X-Real-IP");
        if (isValidIp(ip)) {
            log.debug("从X-Real-IP获取到IP: {}", ip);
            return ip;
        }

        // 3. 检查Proxy-Client-IP header (Apache代理常用)
        ip = request.getHeader("Proxy-Client-IP");
        if (isValidIp(ip)) {
            log.debug("从Proxy-Client-IP获取到IP: {}", ip);
            return ip;
        }

        // 4. 检查WL-Proxy-Client-IP header (WebLogic代理)
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (isValidIp(ip)) {
            log.debug("从WL-Proxy-Client-IP获取到IP: {}", ip);
            return ip;
        }

        // 5. 检查HTTP_CLIENT_IP header
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (isValidIp(ip)) {
            log.debug("从HTTP_CLIENT_IP获取到IP: {}", ip);
            return ip;
        }

        // 6. 检查HTTP_X_FORWARDED_FOR header
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (isValidIp(ip)) {
            if (ip.contains(",")) {
                ip = ip.split(",")[0].trim();
            }
            log.debug("从HTTP_X_FORWARDED_FOR获取到IP: {}", ip);
            return ip;
        }

        // 7. 最后使用request.getRemoteAddr()
        ip = request.getRemoteAddr();
        log.debug("从RemoteAddr获取到IP: {}", ip);

        return ip != null ? ip : "unknown";
    }

    /**
     * 获取IP地理位置信息
     * 
     * @param ip IP地址
     * @return 地理位置信息
     */
    public static String getIpLocation(String ip) {
        try {
            // 参数校验
            if (!isValidIp(ip)) {
                return "Unknown";
            }

            // 内网IP处理
            if (isPrivateIp(ip)) {
                return "Private Network";
            }

            // 先从缓存中查询
            if (IP_LOCATION_CACHE.containsKey(ip)) {
                String cachedLocation = IP_LOCATION_CACHE.get(ip);
                log.debug("从缓存获取IP地理位置: ip={}, location={}", ip, cachedLocation);
                return cachedLocation;
            }

            // 调用第三方IP地理位置服务
            String location = queryIpLocationFromApi(ip);

            // 更新缓存
            if (location != null) {
                IP_LOCATION_CACHE.put(ip, location);
            }

            log.info("获取IP地理位置: ip={}, location={}", ip, location);
            return location != null ? location : "Unknown";

        } catch (Exception e) {
            log.error("获取IP地理位置失败: ip={}", ip, e);
            // 即使失败也要缓存，避免重复查询
            IP_LOCATION_CACHE.put(ip, "Unknown");
            return "Unknown";
        }
    }

    /**
     * 验证IP地址是否有效
     * 
     * @param ip IP地址字符串
     * @return 是否为有效IP
     */
    private static boolean isValidIp(String ip) {
        return ip != null
                && !ip.trim().isEmpty()
                && !"unknown".equalsIgnoreCase(ip)
                && !"null".equalsIgnoreCase(ip);
    }

    /**
     * 获取客户端IP的简化版本（保持向后兼容）
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        return getClientIp(request);
    }

    /**
     * 调用第三方API查询IP地理位置
     */
    private static String queryIpLocationFromApi(String ip) {
        for (String apiUrl : IP_LOCATION_APIS) {
            try {
                String url = apiUrl.replace("{ip}", ip);
                log.debug("查询IP地理位置API: {}", url);

                // 创建RestTemplate并设置超时
                SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
                factory.setConnectTimeout(3000);
                factory.setReadTimeout(3000);

                RestTemplate restTemplate = new RestTemplate(factory);
                Map<String, Object> response = restTemplate.getForObject(url, Map.class);

                if (response != null) {
                    String location = parseLocationFromResponse(response, apiUrl);
                    if (location != null && !location.isEmpty()) {
                        log.info("成功获取IP地理位置: ip={}, location={}, api={}", ip, location, apiUrl);
                        return location;
                    }
                }
            } catch (Exception e) {
                log.warn("IP地理位置API查询失败: ip={}, api={}, error={}", ip, apiUrl, e.getMessage());
                // 继续尝试下一个API
            }
        }

        log.warn("所有IP地理位置API都查询失败: ip={}", ip);
        return "Unknown";
    }

    /**
     * 解析不同API的响应格式
     */
    private static String parseLocationFromResponse(Map<String, Object> response, String apiUrl) {
        try {
            if (apiUrl.contains("ip-api.com")) {
                // ip-api.com格式:
                // {"status":"success","country":"China","regionName":"Guangdong","city":"Shenzhen"}
                String status = (String) response.get("status");
                if ("success".equals(status)) {
                    String country = (String) response.get("country");
                    String regionName = (String) response.get("regionName");
                    String city = (String) response.get("city");
                    return formatLocation(country, regionName, city);
                }
            } else if (apiUrl.contains("ipapi.co")) {
                // ipapi.co格式: {"country_name":"China","region":"Guangdong","city":"Shenzhen"}
                String country = (String) response.get("country_name");
                String region = (String) response.get("region");
                String city = (String) response.get("city");
                return formatLocation(country, region, city);
            } else if (apiUrl.contains("ipinfo.io")) {
                // ipinfo.io格式: {"country":"CN","region":"Guangdong","city":"Shenzhen"}
                String country = (String) response.get("country");
                String region = (String) response.get("region");
                String city = (String) response.get("city");
                return formatLocation(country, region, city);
            }
        } catch (Exception e) {
            log.warn("解析IP地理位置响应失败: response={}", response, e);
        }

        return null;
    }

    /**
     * 格式化地理位置信息
     */
    private static String formatLocation(String country, String region, String city) {
        StringBuilder location = new StringBuilder();

        if (country != null && !country.isEmpty()) {
            location.append(country);
        }

        if (region != null && !region.isEmpty()) {
            if (location.length() > 0)
                location.append(", ");
            location.append(region);
        }

        if (city != null && !city.isEmpty()) {
            if (location.length() > 0)
                location.append(", ");
            location.append(city);
        }

        return location.length() > 0 ? location.toString() : null;
    }

    /**
     * 判断是否为内网IP
     */
    private static boolean isPrivateIp(String ip) {
        try {
            String[] parts = ip.split("\\.");
            if (parts.length != 4) {
                return false;
            }

            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);

            // 10.0.0.0 - **************
            if (first == 10) {
                return true;
            }

            // ********** - **************
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }

            // *********** - ***************
            if (first == 192 && second == 168) {
                return true;
            }

            // ********* - *************** (本地回环)
            if (first == 127) {
                return true;
            }

            return false;
        } catch (Exception e) {
            return false;
        }
    }
}