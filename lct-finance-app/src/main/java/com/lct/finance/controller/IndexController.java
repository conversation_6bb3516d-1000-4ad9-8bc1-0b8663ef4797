package com.lct.finance.controller;

import com.lct.finance.annotation.RateLimit;
import com.lct.finance.model.enums.LimitAlgorithm;
import com.lct.finance.model.enums.LimitType;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.dto.IndexDataResponse;
import com.lct.finance.service.IIndexDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 首页控制器 - 兼容旧PHP接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class IndexController {

    @Autowired
    private IIndexDataService indexDataService;

    /**
     * 获取首页数据 - 兼容旧PHP接口 /api/index_data
     * 
     * @param language 语言参数（可选）
     * @return 首页数据
     */
    @GetMapping("/index_data")
    @RateLimit(prefix = "index_data_rate:", count = 60, // 每分钟60次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.COUNTER, // 使用计数器算法
            limitType = LimitType.IP, // 按IP限流，因为首页是公共资源
            message = "获取首页数据请求过于频繁，请稍后再试")
    public ApiResponse<IndexDataResponse> getIndexData(
            @RequestParam(value = "language", required = false, defaultValue = "zh") String language) {
        IndexDataResponse response = indexDataService.getIndexData(language);
        return ApiResponse.success(response);
    }


}