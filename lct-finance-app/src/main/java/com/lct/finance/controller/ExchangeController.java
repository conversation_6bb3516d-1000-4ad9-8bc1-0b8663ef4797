package com.lct.finance.controller;

import com.lct.finance.annotation.PreventDuplicateSubmit;
import com.lct.finance.annotation.RateLimit;
import com.lct.finance.config.NonceConfig;
import com.lct.finance.context.UserContext;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.enums.LimitAlgorithm;
import com.lct.finance.model.enums.LimitType;
import com.lct.finance.model.dto.*;
import com.lct.finance.service.IExchangeService;
import com.lct.finance.service.INonceService;
import com.lct.finance.service.IRiskAssessmentService;
import com.lct.finance.service.ISignatureService;
import com.lct.finance.utils.ClientIpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 兑换相关接口控制器
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/exchange")
public class ExchangeController {

        @Autowired
        private IExchangeService exchangeService;

        @Autowired
        private INonceService nonceService;

        @Autowired
        private ISignatureService signatureService;

        @Autowired
        private IRiskAssessmentService riskAssessmentService;

        @Autowired
        private NonceConfig nonceConfig;

        /**
         * 获取兑换基础数据 - 兼容旧PHP接口 /api/exchange/basic
         * 
         * @return 兑换基础数据响应
         */
        @GetMapping("/basic")
        @RateLimit(prefix = "exchange_basic_rate:", count = 60, // 每分钟60次请求
                        time = 60, // 60秒时间窗口
                        algorithm = LimitAlgorithm.COUNTER, // 使用计数器算法
                        limitType = LimitType.IP, // 按IP限流
                        message = "获取基础数据请求过于频繁，请稍后再试")
        public ApiResponse<ExchangeBasicResponse> getBasicData() {
                ExchangeBasicResponse response = exchangeService.getBasicData();
                return ApiResponse.success(response);
        }

        /**
         * 创建兑换订单 - 兼容旧PHP接口 /api/exchange/createPayOrder
         * 
         * @param request     兑换创建请求
         * @param httpRequest HTTP请求对象
         * @return 兑换创建响应
         */
        @PostMapping("/order")
        @PreventDuplicateSubmit(expireTime = 10, keyPrefix = "exchange_create", message = "兑换订单创建中，请勿重复提交")
        @RateLimit(prefix = "exchange_create_rate:", count = 5, // 桶容量为5
                        time = 60, // 60秒时间窗口
                        rate = 1, // 每秒处理1个请求
                        limitType = LimitType.USER, // 按用户限流
                        algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
                        message = "您的兑换操作太频繁，请稍后再试")
        public ApiResponse<ExchangeCreateResponse> createPayOrder(
                        @Valid @RequestBody ExchangeCreateRequest request,
                        HttpServletRequest httpRequest) {

                User currentUser = UserContext.getCurrentUser();
                String userAddress = currentUser.getUserAddress();
                String clientIp = ClientIpUtils.getClientIp(httpRequest);
                request.setIp(clientIp);
                // 记录请求参数（敏感信息脱敏）
                logRequestDetails(request, userAddress, clientIp);

                // 🔐 进行签名验证和风险评估
                return exchangeService
                        .createExchangeWithValidation(request, userAddress, clientIp);
        }

        /**
         * 支付确认 - 兼容旧PHP接口 /api/exchange/pay
         * 
         * @param request     支付确认请求
         * @param httpRequest HTTP请求对象
         * @return 支付确认结果
         */
        @PostMapping("/pay")
        @PreventDuplicateSubmit(expireTime = 15, keyExpression = "'exchange_pay:' + #request.orderId", message = "支付确认处理中，请勿重复提交")
        @RateLimit(prefix = "exchange_pay_rate:", count = 3, // 桶容量为3
                        time = 30, // 30秒时间窗口
                        algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
                        limitType = LimitType.USER, // 按用户限流
                        message = "您的支付操作太频繁，请稍后再试")
        public ApiResponse<Boolean> payExchange(
                        @Valid @RequestBody ExchangePayRequest request,
                        HttpServletRequest httpRequest) {
                String clientIp = ClientIpUtils.getClientIp(httpRequest);
                
                try {
                        return exchangeService.payExchangeWithValidation(request, clientIp);
                } catch (BusinessException e) {
                        // ✅ 捕获业务异常，返回用户友好的错误信息
                        log.warn("兑换支付业务异常 - 订单: {}, 原因: {}", request.getOrderId(), e.getMessage());
                        return ApiResponse.error(e.getMessage());
                } catch (Exception e) {
                        // ✅ 捕获系统异常，返回通用错误信息
                        log.error("兑换支付系统异常 - 订单: {}", request.getOrderId(), e);
                        return ApiResponse.error("支付确认失败，请重试");
                }
        }

        /**
         * 获取兑换历史记录 - 兼容旧PHP接口 /api/exchange/history
         * 
         * @param page        页码（从1开始）
         * @param pageSize    页大小
         * @param startDate   开始日期（格式：yyyy-MM-dd）
         * @param endDate     结束日期（格式：yyyy-MM-dd）
         * @param httpRequest HTTP请求对象
         * @return 兑换历史记录列表
         */
        @GetMapping("/history")
        @RateLimit(prefix = "exchange_history_rate:", count = 30, // 每分钟30次请求
                        time = 60, // 60秒时间窗口
                        algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
                        limitType = LimitType.USER, // 按用户限流
                        message = "查询历史记录过于频繁，请稍后再试")
        public ApiResponse<List<ExchangeHistoryResponse>> getExchangeHistory(
                        @RequestParam(defaultValue = "1") @Min(1) Integer page,
                        @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer pageSize,
                        @RequestParam(required = false) String startDate,
                        @RequestParam(required = false) String endDate,
                        HttpServletRequest httpRequest) {
                List<ExchangeHistoryResponse> response = exchangeService.getExchangeHistory(
                                page, pageSize, startDate, endDate);
                return ApiResponse.success(response);
        }

        /**
         * 记录请求详情
         */
        private void logRequestDetails(ExchangeCreateRequest request, String userAddress, String clientIp) {
                log.debug("兑换请求详情 - 用户: {}, IP: {}, 存入币种: {}, 接收币种: {}, 存入金额: {}, 接收金额: {}, 接收地址: {}",
                                maskAddress(userAddress), clientIp, request.getDepositCoinType(),
                                request.getReceiveCoinType(),
                                request.getDepositAmount(), request.getReceiveAmount(),
                                maskAddress(request.getReceiveAddress()));
        }

        /**
         * 地址脱敏
         */
        private String maskAddress(String address) {
                if (address == null || address.length() <= 10) {
                        return address;
                }
                return address.substring(0, 6) + "..." + address.substring(address.length() - 4);
        }

}