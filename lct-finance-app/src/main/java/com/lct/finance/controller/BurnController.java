package com.lct.finance.controller;

import com.lct.finance.annotation.PreventDuplicateSubmit;
import com.lct.finance.annotation.RateLimit;
import com.lct.finance.config.NonceConfig;
import com.lct.finance.context.UserContext;
import com.lct.finance.model.dto.*;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.enums.LimitAlgorithm;
import com.lct.finance.model.enums.LimitType;
import com.lct.finance.service.IBurnService;
import com.lct.finance.service.INonceService;
import com.lct.finance.service.IRiskAssessmentService;
import com.lct.finance.service.ISignatureService;
import com.lct.finance.utils.ClientIpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 销毁相关接口控制器
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/burn")

public class BurnController {

        @Autowired
        private IBurnService burnService;

        @Autowired
        private INonceService nonceService;

        @Autowired
        private ISignatureService signatureService;

        @Autowired
        private IRiskAssessmentService riskAssessmentService;

        @Autowired
        private NonceConfig nonceConfig;

        /**
         * 获取用户销毁记录历史
         *
         * @param address 用户地址（可选，如果不提供则查询当前登录用户）
         * @param date    日期筛选（可选）
         * @param page    页码，默认1
         * @param size    每页大小，默认20
         * @return 销毁记录历史
         */
        @GetMapping("/history")
        @RateLimit(prefix = "burn_history_rate:", count = 30, // 每分钟30次请求
                        time = 60, // 60秒时间窗口
                        algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
                        limitType = LimitType.USER, // 按用户限流
                        message = "查询历史记录过于频繁，请稍后再试")
        public ApiResponse<BurnHistoryResponse> getBurnHistory(
                        @RequestParam(required = false) String address,
                        @RequestParam(required = false) String date,
                        @RequestParam(defaultValue = "1") Integer page,
                        @RequestParam(defaultValue = "20") Integer size) {
                BurnHistoryResponse response = burnService.getBurnHistory(address, date, page, size);
                return ApiResponse.success(response);
        }

        /**
         * 获取用户销毁收益记录
         *
         * @param address  用户地址（可选，如果不提供则查询当前登录用户）
         * @param date     日期筛选（可选）
         * @param page     页码，默认1
         * @param pageSize 每页大小，默认20
         * @return 销毁收益记录
         */
        @GetMapping("/profits")
        @RateLimit(prefix = "burn_profits_rate:", count = 30, // 每分钟30次请求
                        time = 60, // 60秒时间窗口
                        algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
                        limitType = LimitType.USER, // 按用户限流
                        message = "查询收益记录过于频繁，请稍后再试")
        public ApiResponse<BurnProfitResponse> getBurnProfits(
                        @RequestParam(required = false) String address,
                        @RequestParam(required = false) String date,
                        @RequestParam(defaultValue = "1") Integer page,
                        @RequestParam(defaultValue = "20") Integer pageSize) {
                BurnProfitResponse response = burnService.getBurnProfits(address, date, page, pageSize);
                return ApiResponse.success(response);
        }

        /**
         * 获取用户团队收益记录
         *
         * @param address 用户地址（可选，如果不提供则查询当前登录用户）
         * @param date    日期筛选（可选）
         * @param page    页码，默认1
         * @param size    每页大小，默认20
         * @return 团队收益记录
         */
        @GetMapping("/team_profits")
        @RateLimit(prefix = "burn_team_profits_rate:", count = 30, // 每分钟30次请求
                        time = 60, // 60秒时间窗口
                        algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
                        limitType = LimitType.USER, // 按用户限流
                        message = "查询团队收益记录过于频繁，请稍后再试")
        public ApiResponse<TeamProfitResponse> getTeamProfits(
                        @RequestParam(required = false) String address,
                        @RequestParam(required = false) String date,
                        @RequestParam(defaultValue = "1") Integer page,
                        @RequestParam(defaultValue = "20") Integer size) {
                TeamProfitResponse response = burnService.getTeamProfits(address, date, page, size);
                return ApiResponse.success(response);
        }

        /**
         * 销毁预校验接口
         * 在执行区块链交易前进行所有必要的校验，确保数据一致性
         *
         * @param request 预校验请求
         * @return 预校验结果
         */
        @PostMapping("/preValidate")
        @RateLimit(prefix = "burn_prevalidate_rate:", count = 60, // 每分钟60次请求
                        time = 60, // 60秒时间窗口
                        algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
                        limitType = LimitType.USER, // 按用户限流
                        message = "预校验请求过于频繁，请稍后再试")
        public ApiResponse<BurnPreValidateResponse> preValidateBurn(@Valid @RequestBody BurnPreValidateRequest request,
                        HttpServletRequest httpRequest) {
                User currentUser = UserContext.getCurrentUser();

                log.info("销毁预校验请求 - userId: {}, amount: {}, ip: {}",
                        currentUser.getId(), request.getAmount(), ClientIpUtils.getClientIp(httpRequest));

                BurnPreValidateResponse response = burnService.preValidateBurn(request, currentUser.getId());

                if (response.isCanBurn()) {
                        return ApiResponse.success(response);
                } else {
                        return ApiResponse.error(response.getErrorMessage());
                }
        }

        /**
         * 销毁提交接口 - 与 /create 接口功能相同，为前端兼容性而提供
         *
         * @param request     销毁提交请求
         * @param httpRequest HTTP请求对象
         * @return 提交结果
         */
        @PostMapping("/submit")
        @PreventDuplicateSubmit(expireTime = 10, keyPrefix = "burn_submit", message = "销毁提交处理中，请勿重复提交")
        @RateLimit(prefix = "burn_submit_rate:", count = 5, // 桶容量为5
                        time = 60, // 60秒时间窗口
                        rate = 1, // 每秒处理1个请求
                        algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
                        limitType = LimitType.USER, // 按用户限流
                        message = "您的销毁操作太频繁，请稍后再试")
        public ApiResponse<String> submitBurn(@Valid @RequestBody BurnCreateRequest request,
                        HttpServletRequest httpRequest) {
                String clientIp = ClientIpUtils.getClientIp(httpRequest);
                return burnService.burn(request, clientIp);
        }

}