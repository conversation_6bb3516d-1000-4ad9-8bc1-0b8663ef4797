package com.lct.finance.controller;

import com.lct.finance.annotation.RateLimit;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.dto.NoticeListResponse;
import com.lct.finance.model.enums.LimitAlgorithm;
import com.lct.finance.model.enums.LimitType;
import com.lct.finance.service.INoticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 公告控制器 V2 - 兼容旧PHP接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api")

public class NoticeController {

    @Autowired
    private INoticeService noticeService;

    /**
     * 获取公告列表 - 兼容旧PHP接口 /api/notices
     * 
     * @param page    页码，默认1
     * @param size    每页大小，默认20，最大20
     * @param date    日期筛选 (格式: yyyy-MM-dd)
     * @param request HTTP请求对象
     * @return 公告列表
     */
    @GetMapping("/notices")
    @RateLimit(prefix = "notice_list_rate:", count = 60, // 每分钟60次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.COUNTER, // 使用计数器算法
            limitType = LimitType.IP, // 按IP限流，因为公告是公共资源
            message = "获取公告列表请求过于频繁，请稍后再试")
    public ApiResponse<List<NoticeListResponse>> getNotices(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size,
            @RequestParam(value = "date", required = false) String date,
            HttpServletRequest request) {
        // 在 Controller 层处理语言获取
        String language = getLanguageFromRequest(request);
        List<NoticeListResponse> responseList = noticeService.getNotices(page, size, date, language);
        return ApiResponse.success(responseList);
    }

    /**
     * 从请求中获取语言标识 - 兼容旧PHP接口的多种获取方式，支持zh_CN格式
     * 
     * @param request HTTP请求对象
     * @return 语言标识
     */
    private String getLanguageFromRequest(HttpServletRequest request) {
        // 1. 优先从请求参数获取 (兼容旧接口)
        String language = request.getParameter("language");
        if (language != null && !language.trim().isEmpty()) {
            return language.trim();
        }

        // 2. 从请求头获取
        language = request.getHeader("language");
        if (language != null && !language.trim().isEmpty()) {
            return language.trim();
        }

        // 3. 从Accept-Language请求头获取
        String acceptLanguage = request.getHeader("Accept-Language");
        if (acceptLanguage != null && !acceptLanguage.trim().isEmpty()) {
            // 简单解析Accept-Language，取第一个语言标识
            String[] languages = acceptLanguage.split(",");
            if (languages.length > 0) {
                String firstLang = languages[0].trim();
                if (firstLang.startsWith("zh")) {
                    return "zh_CN"; // 返回完整的中文语言标识
                } else if (firstLang.startsWith("en")) {
                    return "en"; // 英文保持简单格式
                }
            }
        }

        // 4. 默认返回中文
        return "zh_CN";
    }
}