package com.lct.finance.controller;

import com.lct.finance.annotation.RateLimit;
import com.lct.finance.model.enums.LimitAlgorithm;
import com.lct.finance.model.enums.LimitType;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.dto.NonceRequest;
import com.lct.finance.model.dto.NonceResponse;

import com.lct.finance.service.INonceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Nonce控制器
 * 提供nonce生成和验证接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/nonce")
@RequiredArgsConstructor
public class NonceController {

    @Autowired
    private final INonceService nonceService;

    /**
     * 生成nonce
     * 
     * @param request 请求参数
     * @return API响应
     */
    @PostMapping("/generate")
    @RateLimit(
        prefix = "nonce_generate_rate:", 
        count = 20,  // 每分钟20次请求
        time = 60,  // 60秒时间窗口
        algorithm = LimitAlgorithm.COUNTER,  // 使用计数器算法
        limitType = LimitType.IP,  // 按IP限流，因为可能未登录用户也需要获取nonce
        message = "获取nonce请求过于频繁，请稍后再试"
    )
    public ApiResponse<NonceResponse> generateNonce(@RequestBody NonceRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("接收到生成nonce请求: userAddress={}, operation={}, chain={}", 
                request.getUserAddress(), request.getOperation(), request.getChain());
        
        try {
            // 参数验证
            if (request.getUserAddress() == null || request.getUserAddress().trim().isEmpty()) {
                log.warn("生成nonce请求参数错误 - 用户地址为空");
                return ApiResponse.error("用户地址不能为空");
            }
            
            if (request.getOperation() == null || request.getOperation().trim().isEmpty()) {
                log.warn("生成nonce请求参数错误 - 操作类型为空: userAddress={}", 
                        request.getUserAddress());
                return ApiResponse.error("操作类型不能为空");
            }
            
            // 生成nonce
            NonceResponse response = nonceService.generateNonce(
                    request.getUserAddress(),
                    request.getOperation(),
                    request.getChain()
            );
            
            long endTime = System.currentTimeMillis();
            log.info("生成nonce成功: userAddress={}, operation={}, nonce={}, 耗时={}ms", 
                    request.getUserAddress(), request.getOperation(), response.getNonce(), 
                    (endTime - startTime));
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("生成nonce异常: userAddress={}, operation={}, 耗时={}ms, 错误信息={}", 
                    request.getUserAddress(), request.getOperation(), 
                    (endTime - startTime), e.getMessage(), e);
            
            return ApiResponse.error("生成nonce失败: " + e.getMessage());
        }
    }
} 