package com.lct.finance.controller;

import com.lct.finance.annotation.PreventDuplicateSubmit;
import com.lct.finance.annotation.RateLimit;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.dto.WithdrawalHistoryResponse;
import com.lct.finance.model.dto.WithdrawalRequest;
import com.lct.finance.model.dto.WithdrawalResponse;
import com.lct.finance.model.enums.LimitAlgorithm;
import com.lct.finance.model.enums.LimitType;
import com.lct.finance.service.IRiskAssessmentService;
import com.lct.finance.service.ISignatureService;
import com.lct.finance.service.IUserService;
import com.lct.finance.service.IWalletService;
import com.lct.finance.utils.ClientIpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 钱包控制器 - 现代化JSON API接口
 * 
 * 更新说明：
 * - 主要接口已迁移到JSON格式，提供更好的类型安全和性能
 * - 表单格式接口已标记为废弃，仅保持向后兼容
 * - 建议所有新的前端开发使用JSON格式接口
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 * @updated 2024-12-19 迁移到JSON格式
 */
@Slf4j
@RestController
@RequestMapping("/api/wallet")
@Validated
public class WalletController {

    @Autowired
    private IWalletService walletService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ISignatureService signatureService;

    @Autowired
    private IRiskAssessmentService riskAssessmentService;

    /**
     * 获取提现历史记录
     * 
     * @param date     日期筛选（可选，格式：YYYY-MM-DD）
     * @param page     页码（可选，默认1）
     * @param size     每页大小（可选，默认20）
     * @param pageSize 每页大小（兼容前端参数名）
     * @return 提现历史记录列表
     */
    @GetMapping("/withdrawal/history")
    @RateLimit(prefix = "wallet_withdrawal_history_rate:", count = 30, // 每分钟30次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
            limitType = LimitType.USER, // 按用户限流
            message = "查询提现历史记录过于频繁，请稍后再试")
    public ApiResponse<List<WithdrawalHistoryResponse>> withdrawalHistory(
            @RequestParam(value = "date", required = false) String date,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        List<WithdrawalHistoryResponse> historyList = walletService.getWithdrawalHistory(date, page, size, pageSize);
        return ApiResponse.success(historyList);
    }

    /**
     * 用户提现接口 - JSON格式（推荐）
     * 
     * @param request           HTTP请求对象
     * @param withdrawalRequest 提现请求参数
     * @return 提现响应结果
     */
    @PostMapping("/withdrawal")
    @PreventDuplicateSubmit(expireTime = 30, keyPrefix = "wallet_withdrawal", message = "提现申请处理中，请勿重复提交")
    @RateLimit(prefix = "wallet_withdrawal_rate:", count = 5, // 桶容量为5
            time = 60, // 60秒时间窗口
            rate = 1, // 每秒处理1个请求
            algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
            limitType = LimitType.USER, // 按用户限流
            message = "您的提现操作太频繁，请稍后再试")
    public ApiResponse<WithdrawalResponse> withdrawal(
            HttpServletRequest request,
            @Valid @RequestBody WithdrawalRequest withdrawalRequest) {
        String clientIp = ClientIpUtils.getClientIp(request);
        return walletService.withdrawalWithValidation(withdrawalRequest, clientIp);
    }
}