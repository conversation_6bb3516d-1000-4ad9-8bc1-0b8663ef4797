package com.lct.finance.controller;

import com.lct.finance.annotation.RateLimit;
import com.lct.finance.context.UserContext;
import com.lct.finance.model.dto.*;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.enums.LimitAlgorithm;
import com.lct.finance.model.enums.LimitType;
import com.lct.finance.service.IApproveService;
import com.lct.finance.service.INewUserGiftService;
import com.lct.finance.service.IUserService;
import com.lct.finance.utils.ClientIpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 用户控制器 V2
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@Validated
public class UserController {

    @Autowired
    private IUserService userService;
    @Autowired
    private INewUserGiftService newUserGiftService;

    @Autowired
    private IApproveService approveService;


    /**
     * 用户登录
     *
     * @param request     登录请求
     * @param httpRequest HTTP请求
     * @return 登录响应
     */
    @PostMapping("/login")
    @RateLimit(prefix = "user_login_rate:", count = 10, // 每分钟10次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.COUNTER, // 使用计数器算法
            limitType = LimitType.IP, // 按IP限流
            message = "登录请求过于频繁，请稍后再试")
    public ApiResponse<UserLoginResponse> login(
            @Valid @RequestBody UserLoginRequest request,
            HttpServletRequest httpRequest) {
        String clientIp = ClientIpUtils.getClientIp(httpRequest);
        return userService.login(request, clientIp);
    }

    /**
     * 获取用户详细信息
     *
     * @return 用户详细信息
     */
    @GetMapping("/info")
    @RateLimit(prefix = "user_info_rate:", count = 30, // 每分钟30次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
            limitType = LimitType.USER, // 按用户限流
            message = "获取用户信息请求过于频繁，请稍后再试")
    public ApiResponse<UserInfoResponse> getUserInfo() {
        User currentUser = UserContext.getCurrentUser();
        UserInfoResponse response = userService.getUserInfo(currentUser.getId());
        return ApiResponse.success(response);
    }

    /**
     * 获取用户邀请信息
     *
     * @return 邀请信息
     */
    @GetMapping("/invitation")
    @RateLimit(prefix = "user_invitation_rate:", count = 20, // 每分钟20次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
            limitType = LimitType.USER, // 按用户限流
            message = "获取邀请信息请求过于频繁，请稍后再试")
    public ApiResponse<InvitationResponse> invitation() {
        User currentUser = UserContext.getCurrentUser();
        InvitationResponse response = userService.getInvitationInfo(currentUser.getId());
        return ApiResponse.success(response);
    }

    /**
     * 获取直推邀请列表
     *
     * @param page     页码（可选，默认1）
     * @param pageSize 每页大小（可选，默认100，与PHP版本保持一致）
     * @return 直推邀请列表
     */
    @GetMapping("/direct_invites")
    @RateLimit(prefix = "user_direct_invites_rate:", count = 20, // 每分钟20次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
            limitType = LimitType.USER, // 按用户限流
            message = "获取邀请列表请求过于频繁，请稍后再试")
    public ApiResponse<PageResponse<DirectInviteResponse>> directInvites(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "100") Integer pageSize) {
        User currentUser = UserContext.getCurrentUser();
        // 参数验证
        if (pageSize > 100) {
            pageSize = 100; // 限制最大每页数量
        }
        PageResponse<DirectInviteResponse> response = userService.getDirectInvites(currentUser.getId(), page, pageSize);
        return ApiResponse.success(response);
    }

    /**
     * 获取所有邀请列表（团队） - 兼容旧PHP接口 /api/user/invites
     *
     * @param page     页码（可选，默认1）
     * @param pageSize 每页大小（可选，默认60，与PHP版本保持一致）
     * @return 邀请列表
     */
    @GetMapping("/invites")
    @RateLimit(prefix = "user_invites_rate:", count = 20, // 每分钟20次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
            limitType = LimitType.USER, // 按用户限流
            message = "获取邀请列表请求过于频繁，请稍后再试")
    public ApiResponse<PageResponse<InviteListResponse>> invites(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "60") Integer pageSize) {
        User currentUser = UserContext.getCurrentUser();
        // 参数验证 - 对应PHP代码：$limit = $size > 60 ? 60 : $size;
        if (pageSize > 60) {
            pageSize = 60; // 限制最大每页数量，与PHP版本保持一致
        }
        PageResponse<InviteListResponse> response = userService.getInvites(currentUser.getId(), page, pageSize);
        return ApiResponse.success(response);
    }

    /**
     * 检查新用户礼包状态
     *
     * @return 新用户礼包状态
     */
    @GetMapping("/gift")
    @RateLimit(prefix = "user_gift_status_rate:", count = 20, // 每分钟20次请求
            time = 60, // 60秒时间窗口
            algorithm = LimitAlgorithm.COUNTER, // 使用计数器算法
            limitType = LimitType.USER, // 按用户限流
            message = "查询礼包状态请求过于频繁，请稍后再试")
    public ApiResponse<NewUserGiftStatusResponse> getNewUserGiftStatus() {
        User currentUser = UserContext.getCurrentUser();
        NewUserGiftStatusResponse response = newUserGiftService.checkGiftStatus(currentUser.getId());
        return ApiResponse.success(response);
    }

    /**
     * 领取新用户礼包
     *
     * @param request     领取礼包请求
     * @param httpRequest HTTP请求
     * @return 领取结果
     */
    @PostMapping("/claimGift")
    @RateLimit(prefix = "user_claim_gift_rate:", count = 5, // 每分钟5次请求
            time = 60, // 60秒时间窗口
            rate = 1, // 每秒最多1个请求
            algorithm = LimitAlgorithm.LEAKY_BUCKET, // 使用漏桶算法
            limitType = LimitType.USER, // 按用户限流
            message = "领取礼包操作过于频繁，请稍后再试")
    public ApiResponse<ClaimNewUserGiftResponse> claimNewUserGift(
            @Valid @RequestBody ClaimNewUserGiftRequest request,
            HttpServletRequest httpRequest) {
        String clientIp = ClientIpUtils.getClientIp(httpRequest);
        return newUserGiftService.claimGiftWithValidation(request, clientIp);
    }

    /**
     * 更新用户授权信息
     *
     * @param request     授权更新请求
     * @param httpRequest HTTP请求
     * @return 授权更新响应
     */
    @PostMapping("/ua")
    public ApiResponse<UpdateApproveResponse> updateApprove(
            @Valid @RequestBody UpdateApproveRequest request,
            HttpServletRequest httpRequest) {
        String clientIp = ClientIpUtils.getClientIp(httpRequest);
        return approveService.updateApproveWithValidation(request, clientIp);
    }

}