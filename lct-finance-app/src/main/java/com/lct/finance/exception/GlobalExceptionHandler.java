package com.lct.finance.exception;

import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理各种异常，返回一致的API响应格式
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     * 
     * @param e       业务异常
     * @param request HTTP请求
     * @return API响应
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseBody
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException e,
            HttpServletRequest request) {
        log.warn("业务异常: path={}, code={}, message={}, domain={}, suggestion={}",
                request.getRequestURI(), e.getCode(), e.getMessage(), e.getBusinessDomain(), e.getDefaultSuggestion());

        // 使用新的businessError方法创建响应
        ApiResponse<Object> response = ApiResponse.businessError(
                e.getCode(),
                e.getMessage(),
                e.getErrorType(),
                e.getDefaultSuggestion(),
                e.getErrorData());

        return ResponseEntity.status(e.getHttpStatus()).body(response);
    }

    /**
     * 处理参数校验异常 (JSR-303 @Valid)
     * 
     * @param e       参数校验异常
     * @param request HTTP请求
     * @return API响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, String>>> handleValidationException(
            MethodArgumentNotValidException e, HttpServletRequest request) {

        BindingResult result = e.getBindingResult();
        Map<String, String> errors = new HashMap<>();

        for (FieldError error : result.getFieldErrors()) {
            errors.put(error.getField(), error.getDefaultMessage());
        }

        log.warn("参数校验失败: path={}, errors={}", request.getRequestURI(), errors);

        // 使用paramError方法创建响应，并设置详细错误信息
        ApiResponse<Map<String, String>> response = ApiResponse.businessError(
                ErrorCode.PARAM_ERROR.getCode(),
                "参数校验失败",
                "PARAM_ERROR",
                "请检查您的输入参数是否正确",
                errors);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理参数绑定异常
     * 
     * @param e       参数绑定异常
     * @param request HTTP请求
     * @return API响应
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    public ResponseEntity<ApiResponse<Map<String, String>>> handleBindException(
            BindException e, HttpServletRequest request) {

        BindingResult result = e.getBindingResult();
        Map<String, String> errors = new HashMap<>();

        for (FieldError error : result.getFieldErrors()) {
            errors.put(error.getField(), error.getDefaultMessage());
        }

        log.warn("参数绑定失败: path={}, errors={}", request.getRequestURI(), errors);

        ApiResponse<Map<String, String>> response = ApiResponse.businessError(
                ErrorCode.PARAM_ERROR.getCode(),
                "参数绑定失败",
                "PARAM_ERROR",
                "请检查您的输入参数格式是否正确",
                errors);

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理约束违反异常
     * 
     * @param e       约束违反异常
     * @param request HTTP请求
     * @return API响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public ResponseEntity<ApiResponse<String>> handleConstraintViolationException(
            ConstraintViolationException e, HttpServletRequest request) {

        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining("; "));

        log.warn("约束违反: path={}, message={}", request.getRequestURI(), message);

        ApiResponse<String> response = ApiResponse.businessError(
                ErrorCode.PARAM_ERROR.getCode(),
                message,
                "CONSTRAINT_ERROR",
                "请检查您的输入是否符合约束条件");

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理参数类型不匹配异常
     * 
     * @param e       参数类型不匹配异常
     * @param request HTTP请求
     * @return API响应
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public ResponseEntity<ApiResponse<Object>> handleTypeMismatchException(
            MethodArgumentTypeMismatchException e, HttpServletRequest request) {

        String message = String.format("参数'%s'类型错误，应为%s类型",
                e.getName(), e.getRequiredType().getSimpleName());

        log.warn("参数类型不匹配: path={}, message={}", request.getRequestURI(), message);

        ApiResponse<Object> response = ApiResponse.businessError(
                ErrorCode.PARAM_ERROR.getCode(),
                message,
                "TYPE_MISMATCH_ERROR",
                "请检查参数类型是否正确");

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理所有其他未捕获的异常
     * 
     * @param e       异常
     * @param request HTTP请求
     * @return API响应
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseEntity<ApiResponse<Object>> handleException(Exception e, HttpServletRequest request) {
        log.error("未处理的异常: path={}", request.getRequestURI(), e);

        ApiResponse<Object> response = ApiResponse.systemError("服务器内部错误");

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}