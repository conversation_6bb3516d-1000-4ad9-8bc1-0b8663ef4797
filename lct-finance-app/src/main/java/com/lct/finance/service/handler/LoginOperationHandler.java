package com.lct.finance.service.handler;

import com.lct.finance.model.dto.UserLoginRequest;
import com.lct.finance.model.dto.UserLoginResponse;
import com.lct.finance.model.request.OperationValidationRequest;
import com.lct.finance.model.response.OperationValidationResult;
import com.lct.finance.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 登录操作处理器
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class LoginOperationHandler extends AbstractOperationHandler<UserLoginRequest, UserLoginResponse> {

    @Autowired
    private IUserService userService;

    @Override
    protected OperationValidationRequest buildValidationRequest(UserLoginRequest request, String clientIp) {
        return OperationValidationRequest.builder()
                .address(request.getUserAddress())
                .operation("LOGIN")
                .message(request.getMessage())
                .signature(request.getSign())
                .nonce(request.getNonce())
                .clientIp(clientIp)
                .deviceFingerprint(request.getDeviceFingerprint())
                .chain(request.getChain())
                .performRiskAssessment(true)
                .recordOperationLog(true)
                .build();
    }

    @Override
    protected UserLoginResponse executeBusinessLogic(UserLoginRequest request, String clientIp,
            OperationValidationResult validationResult) {

        log.info("执行登录业务逻辑: chain={}, address={}, inviteCode={}",
                request.getChain(), request.getUserAddress(), request.getInviteCode());

        // 调用用户服务处理登录，传递验证结果避免重复验证
        return userService.handleLogin(request, clientIp, validationResult.getSignatureResult());
    }

    @Override
    protected String getOperationName() {
        return "登录";
    }

    @Override
    protected void onHighRiskDetected(OperationValidationRequest request, OperationValidationResult result) {
        // 登录的高风险处理
        log.warn("高风险登录操作被检测到: address={}, riskLevel={}",
                request.getAddress(), result.getRiskLevel());

        // TODO: 可以在这里实现额外的安全措施
        // 1. 强制二次认证
        // 2. 发送登录警报
        // 3. 临时锁定账户
        // 4. 要求更换密码
    }
}