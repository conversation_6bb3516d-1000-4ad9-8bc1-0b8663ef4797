package com.lct.finance.service.impl;

import com.lct.finance.annotation.RateLimit;
import com.lct.finance.context.UserContext;
import com.lct.finance.aspect.RateLimitKeyGenerator;
import com.lct.finance.utils.ClientIpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 默认的限流Key生成器实现
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-20
 */
@Slf4j
@Component
public class DefaultRateLimitKeyGenerator implements RateLimitKeyGenerator {
    

    @Override
    public String generate(RateLimit rateLimit, JoinPoint joinPoint) {
        StringBuilder key = new StringBuilder(rateLimit.prefix());
        
        // 根据限流类型生成对应的key
        switch (rateLimit.limitType()) {
            case IP:
                // 按照IP限流
                key.append(ClientIpUtils.getClientIp(getRequest())).append(":");
                break;
            case USER:
                // 按照用户限流，需要在实际项目中获取用户ID
                String userId = getUserId();
                if (StringUtils.isNotBlank(userId)) {
                    key.append(userId).append(":");
                } else {
                    // 如果获取不到用户ID，则按照IP限流
                    String ip = ClientIpUtils.getClientIp(getRequest());
                    key.append("ip:").append(ip).append(":");
                    log.debug("未找到用户ID，按照IP限流: {}", ip);
                }
                break;
            case DEFAULT:
            default:
                // 默认按照接口限流
                MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                Method method = signature.getMethod();
                key.append(method.getDeclaringClass().getName())
                   .append(":")
                   .append(method.getName())
                   .append(":");
                break;
        }
        
        // 如果配置了自定义key，则使用自定义key
        if (StringUtils.isNotBlank(rateLimit.key())) {
            key.append(rateLimit.key());
        } else {
            // 否则使用请求的URI作为key
            key.append(getRequestURI());
        }
        
        return key.toString();
    }
    

    
    /**
     * 获取当前请求的URI
     */
    private String getRequestURI() {
        HttpServletRequest request = getRequest();
        return request == null ? "" : request.getRequestURI();
    }
    
    /**
     * 获取当前请求对象
     */
    private HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes == null ? null : attributes.getRequest();
    }
    
    /**
     * 获取当前用户ID或地址，用于按用户限流
     * 按以下优先级获取：
     * 1. UserContext中的用户地址（已登录用户）
     * 2. 请求头中的用户地址（未登录用户）
     *
     * @return 用户ID或地址，无法获取则返回null
     */
    private String getUserId() {
        // 尝试从UserContext获取当前用户地址（已登录用户）
        String userAddress = UserContext.getCurrentUserAddress();
        if (StringUtils.isNotBlank(userAddress)) {
            log.debug("从UserContext获取到用户地址: {}", maskAddress(userAddress));
            return userAddress;
        }
        
        // 如果UserContext中没有用户信息，尝试从请求头获取用户地址（未登录用户）
        HttpServletRequest request = getRequest();
        if (request != null) {
            String address = request.getHeader("user-address");
            if (StringUtils.isBlank(address)) {
                address = request.getParameter("user_address");
            }
            
            if (StringUtils.isNotBlank(address)) {
                log.debug("从请求头或参数获取到用户地址: {}", maskAddress(address));
                return address;
            }
        }
        
        // 无法获取用户标识
        log.debug("无法获取用户标识，将回退到IP限流");
        return null;
    }
    
    /**
     * 对地址进行掩码处理，用于日志输出
     *
     * @param address 完整地址
     * @return 掩码后的地址
     */
    private String maskAddress(String address) {
        if (address == null || address.length() < 10) {
            return address;
        }
        return address.substring(0, 6) + "****" + address.substring(address.length() - 4);
    }
} 