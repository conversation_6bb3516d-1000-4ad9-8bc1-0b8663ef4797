package com.lct.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.model.dto.SwftAccountExchangeRequest;
import com.lct.finance.model.dto.SwftAccountExchangeResponse;
import com.lct.finance.model.dto.SwftBaseInfoResponse;
import com.lct.finance.service.ISwftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * SWFT服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class SwftServiceImpl implements ISwftService {

    @Value("${swft.api.host:https://api.omnibridge.pro}")
    private String apiHost;

    // 备用API主机，从PHP版本中发现
    private final String backupApiHost = "https://api.bridgers.xyz";

    @Value("${swft.api.timeout:60000}")
    private int timeout;

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public SwftServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 创建HTTP请求头，模仿PHP版本的简单请求
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 模仿PHP GuzzleHttp的User-Agent
        headers.set("User-Agent", "GuzzleHttp/7.0");

        return headers;
    }

    @Override
    public SwftBaseInfoResponse getBaseInfo(String depositCoinCode, String receiveCoinCode, String depositCoinAmt) {
        log.info("获取SWFT汇率基础信息 - 存入币种: {}, 接收币种: {}, 数量: {}",
                depositCoinCode, receiveCoinCode, depositCoinAmt);

        try {
            String url = apiHost + "/api/v2/getBaseInfo";

            Map<String, Object> params = new HashMap<>();
            params.put("depositCoinCode", depositCoinCode);
            params.put("receiveCoinCode", receiveCoinCode);
            params.put("depositCoinAmt", depositCoinAmt);

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params, headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            JsonNode jsonNode = objectMapper.readTree(response.getBody());

            if (!"800".equals(jsonNode.get("resCode").asText())) {
                log.error("SWFT获取汇率失败 - 错误码: {}, 错误信息: {}",
                        jsonNode.get("resCode").asText(), jsonNode.get("resMsg").asText());
                throw new BusinessException("获取汇率信息失败");
            }

            JsonNode data = jsonNode.get("data");

            return SwftBaseInfoResponse.builder()
                    .instantRate(new BigDecimal(data.get("instantRate").asText()))
                    .depositCoinCode(data.get("depositCoinCode").asText())
                    .receiveCoinCode(data.get("receiveCoinCode").asText())
                    .depositCoinAmt(new BigDecimal(data.get("depositCoinAmt").asText()))
                    .receiveCoinAmt(new BigDecimal(data.get("receiveCoinAmt").asText()))
                    .minDepositCoinAmt(new BigDecimal(data.get("minDepositCoinAmt").asText()))
                    .maxDepositCoinAmt(new BigDecimal(data.get("maxDepositCoinAmt").asText()))
                    .depositCoinFeeRate(new BigDecimal(data.get("depositCoinFeeRate").asText()))
                    .chainFee(new BigDecimal(data.get("chainFee").asText()))
                    .build();

        } catch (Exception e) {
            log.error("调用SWFT获取汇率基础信息失败", e);
            throw new BusinessException("获取汇率信息失败: " + e.getMessage());
        }
    }

    @Override
    public SwftAccountExchangeResponse accountExchange(SwftAccountExchangeRequest request) {
        log.info("创建SWFT账户兑换订单 - 存入币种: {}, 接收币种: {}, 存入数量: {}",
                request.getDepositCoinCode(), request.getReceiveCoinCode(), request.getDepositCoinAmt());

        try {
            String url = apiHost + "/api/v2/accountExchange";

            Map<String, Object> params = new HashMap<>();
            params.put("depositCoinCode", request.getDepositCoinCode());
            params.put("receiveCoinCode", request.getReceiveCoinCode());
            params.put("depositCoinAmt", request.getDepositCoinAmt());
            params.put("receiveCoinAmt", request.getReceiveCoinAmt());
            params.put("destinationAddr", request.getDestinationAddr());
            params.put("refundAddr", request.getRefundAddr());
            params.put("equipmentNo", request.getEquipmentNo());
            params.put("sourceType", request.getSourceType());
            params.put("sourceFlag", request.getSourceFlag());
            params.put("isNoGas", request.getIsNoGas() != null ? request.getIsNoGas() : false);

            log.debug("SWFT请求URL: {}", url);
            log.debug("SWFT请求参数: {}", objectMapper.writeValueAsString(params));

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params, headers);

            // 添加随机延迟，避免被识别为机器人
            Thread.sleep(1000 + (long) (Math.random() * 2000)); // 1-3秒随机延迟

            log.info("=========" + JSON.toJSONString(entity));
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            String responseBody = response.getBody();
            log.debug("SWFT响应原始内容: {}", responseBody);

            // 检查是否被Cloudflare拦截
            if (responseBody != null && responseBody.contains("Blocked by Cloudflare Gateway")) {
                log.warn("主API被Cloudflare拦截，尝试使用备用API: {}", backupApiHost);

                String backupUrl = backupApiHost + "/api/v2/accountExchange";
                log.debug("备用SWFT请求URL: {}", backupUrl);

                // 添加额外延迟
                Thread.sleep(2000);
                log.info("=========" + JSON.toJSONString(entity));

                ResponseEntity<String> backupResponse = restTemplate.exchange(backupUrl, HttpMethod.POST, entity,
                        String.class);
                responseBody = backupResponse.getBody();
                log.debug("备用SWFT响应原始内容: {}", responseBody);
            }

            JsonNode jsonNode = objectMapper.readTree(responseBody);

            if (!"800".equals(jsonNode.get("resCode").asText())) {
                log.error("SWFT创建账户兑换订单失败 - 错误码: {}, 错误信息: {}",
                        jsonNode.get("resCode").asText(), jsonNode.get("resMsg").asText());
                throw new BusinessException("创建SWFT订单失败");
            }

            JsonNode data = jsonNode.get("data");

            return SwftAccountExchangeResponse.builder()
                    .orderId(data.get("orderId").asText())
                    .depositCoinCode(data.get("depositCoinCode").asText())
                    .receiveCoinCode(data.get("receiveCoinCode").asText())
                    .depositCoinAmt(new BigDecimal(data.get("depositCoinAmt").asText()))
                    .receiveCoinAmt(new BigDecimal(data.get("receiveCoinAmt").asText()))
                    .platformAddr(data.get("platformAddr").asText())
                    .depositCoinState(data.get("depositCoinState").asText())
                    .depositCoinFeeRate(new BigDecimal(data.get("depositCoinFeeRate").asText()))
                    .depositCoinFeeAmt(new BigDecimal(data.get("depositCoinFeeAmt").asText()))
                    .orderState(data.get("orderState").asText())
                    .destinationAddr(data.get("destinationAddr").asText())
                    .refundAddr(data.get("refundAddr").asText())
                    .instantRate(new BigDecimal(data.get("instantRate").asText()))
                    .chainFee(new BigDecimal(data.get("chainFee").asText()))
                    .createTime(data.get("createTime").asText())
                    .build();

        } catch (Exception e) {
            log.error("调用SWFT创建账户兑换订单失败", e);
            throw new BusinessException("创建SWFT订单失败: " + e.getMessage());
        }
    }
}