package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.MintBaseMapper;
import com.lct.finance.model.entity.MintBase;
import com.lct.finance.service.IMintBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 铸造基础数据服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class MintBaseServiceImpl extends ServiceImpl<MintBaseMapper, MintBase> implements IMintBaseService {

    @Override
    public BigDecimal getEstimateTotalProfit(Long userId) {
        try {
            return baseMapper.getEstimateTotalProfit(userId);
        } catch (Exception e) {
            log.error("获取用户预估总收益失败: userId={}", userId, e);
            throw e;
        }
    }
}