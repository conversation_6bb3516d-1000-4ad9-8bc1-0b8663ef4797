package com.lct.finance.service.impl;

import com.lct.finance.model.dto.IndexDataResponse;
import com.lct.finance.model.dto.ProfitRankingResponse;
import com.lct.finance.model.entity.Banner;
import com.lct.finance.model.entity.Notice;
import com.lct.finance.model.entity.NoticeModal;
import com.lct.finance.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IndexDataServiceImpl implements IIndexDataService {
    @Autowired
    private IRankingService rankingService;
    @Autowired
    private IBannerService bannerService;
    @Autowired
    private INoticeService noticeService;
    @Autowired
    private IOptionService optionService;

    @Override
    public IndexDataResponse getIndexData(String language) {
        try {
            log.info("获取首页数据请求: language={}", language);
            // 获取个人收益排行榜（前10名）
            List<ProfitRankingResponse> personalProfitRank = rankingService.getPersonalProfitRanking(10);

            // 获取轮播图数据（最多5张）
            String bannerLanguage = convertLanguageCode(language);
            List<Banner> bannerList = bannerService.getVisibleBanners(bannerLanguage, 5);
            List<IndexDataResponse.BannerItem> banners = bannerList.stream()
                    .map(banner -> IndexDataResponse.BannerItem.builder()
                            .picture(formatImageUrl(banner.getPicture()))
                            .link(banner.getLink() != null ? banner.getLink() : "")
                            .build())
                    .collect(Collectors.toList());

            // 获取公告数据（最多5条）
            List<Notice> noticeList = noticeService.getFlaggedNotices(bannerLanguage, 5);
            List<IndexDataResponse.NoticeItem> notices = noticeList.stream()
                    .map(notice -> IndexDataResponse.NoticeItem.builder()
                            .id(notice.getId())
                            .title(notice.getTitle())
                            .intro(notice.getIntro())
                            .picture(formatImageUrl(notice.getPicture()))
                            .link(notice.getLink() != null ? notice.getLink() : "")
                            .createdAt(notice.getCreatedAt() != null ?
                                    notice.getCreatedAt().atZone(java.time.ZoneId.systemDefault()).toEpochSecond() : 0L)
                            .build())
                    .collect(Collectors.toList());

            // 获取公告弹框
            NoticeModal noticeModal = noticeService.getNoticeModal(bannerLanguage);
            IndexDataResponse.NoticeModalItem noticeModalItem = IndexDataResponse.NoticeModalItem.builder()
                    .title(noticeModal != null ? noticeModal.getTitle() : "")
                    .content(noticeModal != null ? noticeModal.getContent() : "")
                    .build();

            // 获取最低销毁数量
            String minBurnAmount = optionService.getMinBurnAmount();

            // 构建响应数据
            IndexDataResponse response = IndexDataResponse.builder()
                    .minBurn(minBurnAmount)
                    .personalProfitRank(personalProfitRank)
                    .banners(banners)
                    .notices(notices)
                    .noticeModal(noticeModalItem)
                    .build();

            log.info("首页数据获取成功: 排行榜{}条记录, 轮播图{}张, 公告{}条",
                    personalProfitRank.size(), banners.size(), notices.size());
            return response;
        } catch (Exception e) {
            log.error("获取首页数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取首页数据失败");
        }
    }

    /**
     * 转换语言代码
     * 前端传入: zh, en
     * 数据库存储: zh_CN, en_US
     */
    private String convertLanguageCode(String language) {
        if (language == null) {
            return "zh_CN";
        }

        switch (language.toLowerCase()) {
            case "zh":
                return "zh_CN";
            case "en":
                return "en_US";
            default:
                return "zh_CN";
        }
    }

    /**
     * 格式化图片URL
     * 如果是相对路径，则添加域名前缀
     */
    private String formatImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            return "";
        }

        // 如果已经是完整URL，直接返回
        if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
            return imageUrl;
        }

        // 如果是相对路径，添加前缀（这里可以从配置中读取域名）
        if (imageUrl.startsWith("/")) {
            return imageUrl; // 保持相对路径，由前端处理
        } else {
            return "/" + imageUrl; // 确保以/开头
        }
    }
}