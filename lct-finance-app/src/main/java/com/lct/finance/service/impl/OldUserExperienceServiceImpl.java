package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.OldUserExperienceMapper;
import com.lct.finance.model.entity.OldUserExperience;
import com.lct.finance.service.IOldUserExperienceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 老用户体验金服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class OldUserExperienceServiceImpl extends ServiceImpl<OldUserExperienceMapper, OldUserExperience>
        implements IOldUserExperienceService {

    @Override
    public BigDecimal getUserExperienceAmount(Long userId) {
        log.debug("获取用户体验金数量: userId={}", userId);

        try {
            // 🔧 与PHP逻辑保持一致：OldUserExperience::where('user_id', $user->id)->first()
            LambdaQueryWrapper<OldUserExperience> queryWrapper = new LambdaQueryWrapper<OldUserExperience>()
                    .eq(OldUserExperience::getUserId, userId)
                    .eq(OldUserExperience::getStatus, 1); // 只查询有效的体验金

            OldUserExperience experience = this.getOne(queryWrapper);
            if (experience != null && experience.getAmount() != null) {
                log.debug("找到用户体验金: userId={}, amount={}", userId, experience.getAmount());
                return experience.getAmount();
            }

            log.debug("用户无体验金记录: userId={}", userId);
            return null; // 与PHP逻辑一致，不存在时返回null

        } catch (Exception e) {
            log.error("获取用户体验金数量失败: userId={}", userId, e);
            return null;
        }
    }
}