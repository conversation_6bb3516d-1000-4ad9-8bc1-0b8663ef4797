package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lct.finance.config.BlockchainNetworkConfig;
import com.lct.finance.context.UserContext;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.mapper.ExchangeMapper;
import com.lct.finance.model.constants.CommonConstants;
import com.lct.finance.model.dto.*;
import com.lct.finance.model.entity.Exchange;
import com.lct.finance.model.entity.Option;
import com.lct.finance.model.entity.Prices;
import com.lct.finance.model.entity.User;
import com.lct.finance.service.*;
import com.lct.finance.service.handler.ExchangeCreateOperationHandler;
import com.lct.finance.service.handler.ExchangePayOperationHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 兑换服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class ExchangeServiceImpl extends ServiceImpl<ExchangeMapper, Exchange> implements IExchangeService {

    @Autowired
    private IOptionService optionService;

    @Autowired
    private IPricesService pricesService;

    @Autowired
    private IUserService userService;

    @Autowired
    private BlockchainNetworkConfig blockchainNetworkConfig;

    @Autowired
    private ISwftService swftService;

    @Autowired
    private IOperationValidationService operationValidationService;

    @Autowired
    private IRiskAssessmentService riskAssessmentService;

    @Autowired
    private ExchangePayOperationHandler exchangePayOperationHandler;

    @Autowired
    private ExchangeCreateOperationHandler exchangeCreateOperationHandler;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ExchangeBasicResponse getBasicData() {
        log.debug("开始获取兑换基础数据...");

        // 获取价格配置
        Map<String, BigDecimal> priceConfig = getPriceConfig();
        // 获取授权地址配置
        Map<String, String> approveConfig = getApproveConfig();
        // 获取兑换开关配置
        Map<String, Integer> exchangeConfig = getExchangeConfig();
        // 获取价格历史数据
        Map<String, List<String>> priceHistory = getPriceHistory();
        // 构建响应数据
        ExchangeBasicResponse response = ExchangeBasicResponse.builder()
                .usdt_lct(priceConfig.getOrDefault("usdt_lct", BigDecimal.ZERO))
                .lct_usdt(priceConfig.getOrDefault("lct_usdt", BigDecimal.ZERO))
                .bsc_auth_address(approveConfig.getOrDefault("bsc_auth_address", ""))
                .exchange_switch_usdt2lct(exchangeConfig.getOrDefault("exchange_switch_usdt2lct", 0))
                .exchange_switch_lct2usdt(exchangeConfig.getOrDefault("exchange_switch_lct2usdt", 0))
                .dates(priceHistory.get("dates"))
                .lctusdtList(priceHistory.get("lctusdtList"))
                .usdtlctList(priceHistory.get("usdtlctList"))
                .build();

        log.debug("兑换基础数据获取完成");
        return response;
    }

    @Override
    @Transactional
    public ExchangeCreateResponse createExchange(ExchangeCreateRequest request, String userAddress) {
        log.info("创建兑换订单 - 用户: {}, 请求: {}", userAddress, request);

        try {
            // 1. 验证请求参数
            String validationError = validateExchangeRequest(request);
            if (validationError != null) {
                throw new BusinessException(validationError);
            }

            // 2. 查找用户
            User user = getUserByAddress(userAddress);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            // 3. 检查是否已有待支付订单
            // boolean hasPendingOrder = this.lambdaQuery()
            // .eq(Exchange::getUserId, user.getId())
            // .eq(Exchange::getStatus, CommonConstants.ExchangeStatus.PAYING)
            // .exists();
            //
            // if (hasPendingOrder) {
            // throw new BusinessException("您已有待支付的兑换订单，请先完成支付后再创建新订单");
            // }

            // 4. LCT兑换USDT特殊处理 // TODO 这个后续不处理了吧，前端应该也要注解
            if (request.getDepositCoinType() == CommonConstants.CoinType.LCT_BSC) {
                return handleLctToUsdtExchange(request, user, userAddress);
            }

            // 5. USDT兑换LCT处理
            return handleUsdtToLctExchange(request, user, userAddress);

        } catch (Exception e) {
            log.error("创建兑换订单失败 - 用户: {}", userAddress, e);
            throw new BusinessException("创建兑换订单失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<ExchangeCreateResponse> createExchangeWithValidation(ExchangeCreateRequest request,
            String userAddress, String clientIp) {
        // 使用模板方法模式的操作处理器，包含签名验证和风险评估
        return exchangeCreateOperationHandler.handleOperation(request, clientIp);
    }

    @Override
    @Transactional
    public ApiResponse<Boolean> payExchangeWithValidation(ExchangePayRequest request, String clientIp) {
        return exchangePayOperationHandler.handleOperation(request, clientIp);
    }

    @Override
    public List<ExchangeHistoryResponse> getExchangeHistory(int page, int pageSize, String startDate, String endDate) {
        User currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            return new ArrayList<>();
        }
        // 构建查询条件
        LambdaQueryWrapper<Exchange> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Exchange::getUserId, currentUser.getId());
        // 添加日期范围查询
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            wrapper.apply("DATE(created_at) BETWEEN {0} AND {1}", startDate, endDate);
        } else if (StringUtils.isNotBlank(startDate)) {
            wrapper.apply("DATE(created_at) >= {0}", startDate);
        } else if (StringUtils.isNotBlank(endDate)) {
            wrapper.apply("DATE(created_at) <= {0}", endDate);
        }
        // 按创建时间降序排序
        wrapper.orderByDesc(Exchange::getCreatedAt);
        // 创建分页对象
        Page<Exchange> pageParam = new Page<>(page, pageSize);
        // 分页查询
        Page<Exchange> pageResult = baseMapper.selectPage(pageParam, wrapper);
        List<Exchange> exchanges = pageResult.getRecords();
        // 转换为响应对象
        List<ExchangeHistoryResponse> responses = new ArrayList<>();
        for (Exchange exchange : exchanges) {
            responses.add(ExchangeHistoryResponse.fromExchange(exchange));
        }
        return responses;
    }

    @Override
    public Exchange getExchangeById(Long exchangeId) {
        try {
            return getById(exchangeId);
        } catch (Exception e) {
            log.error("根据ID获取兑换订单失败 - ID: {}", exchangeId, e);
            return null;
        }
    }

    @Override
    public Exchange getExchangeByOrderNo(String orderNo) {
        try {
            // 使用LambdaQueryWrapper替代原有的Mapper方法
            return this.lambdaQuery()
                    .eq(Exchange::getOrderId, orderNo)
                    .one();
        } catch (Exception e) {
            log.error("根据订单号获取兑换订单失败 - 订单号: {}", orderNo, e);
            return null;
        }
    }

    @Override
    public boolean isTransactionHashUsed(String txHash) {
        try {
            // 使用LambdaQueryWrapper替代原有的Mapper方法
            return this.lambdaQuery()
                    .eq(Exchange::getTxhash, txHash)
                    .exists();
        } catch (Exception e) {
            log.error("检查交易哈希失败 - Hash: {}", txHash, e);
            return true; // 保守策略，出错时认为已使用
        }
    }

    @Override
    public String validateExchangeRequest(ExchangeCreateRequest request) {
        if (request == null) {
            return "请求参数不能为空";
        }

        if (request.getDepositAmount() == null || request.getDepositAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return "存入数量必须大于0";
        }

        if (request.getReceiveAmount() == null || request.getReceiveAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return "接收数量必须大于0";
        }

        if (request.getDepositCoinType() == null || request.getReceiveCoinType() == null) {
            return "币种类型不能为空";
        }

        if (request.getDepositCoinType().equals(request.getReceiveCoinType())) {
            return "存入币种和接收币种不能相同";
        }

        return null; // 验证通过
    }

    /**
     * 获取价格配置
     */
    private Map<String, BigDecimal> getPriceConfig() {
        try {
            Option priceOption = optionService.getOne(
                    new LambdaQueryWrapper<Option>().eq(Option::getOptionName, "price"));

            List<Option> priceOptions = optionService.list(
                    new LambdaQueryWrapper<Option>().eq(Option::getOptionName, "price"));


            String priceJson = priceOption != null ? priceOption.getOptionValue() : null;
            if (priceJson != null && !priceJson.trim().isEmpty()) {
                Map<String, Object> priceMap = objectMapper.readValue(priceJson,
                        new TypeReference<Map<String, Object>>() {
                        });

                Map<String, BigDecimal> result = new HashMap<>();
                result.put("usdt_lct", parseDecimal(priceMap.get("usdt_lct")));
                result.put("lct_usdt", parseDecimal(priceMap.get("lct_usdt")));
                return result;
            }
        } catch (Exception e) {
            log.error("解析价格配置失败", e);
        }

        // 返回默认价格
        Map<String, BigDecimal> defaultPrice = new HashMap<>();
        defaultPrice.put("usdt_lct", new BigDecimal("1.0"));
        defaultPrice.put("lct_usdt", new BigDecimal("1.0"));
        return defaultPrice;
    }

    /**
     * 获取授权地址配置
     */
    private Map<String, String> getApproveConfig() {
        try {
            Option approveOption = optionService.getOne(
                    new LambdaQueryWrapper<Option>().eq(Option::getOptionName, "approve"));
            String approveJson = approveOption != null ? approveOption.getOptionValue() : null;
            if (approveJson != null && !approveJson.trim().isEmpty()) {
                Map<String, Object> approveMap = objectMapper.readValue(approveJson,
                        new TypeReference<Map<String, Object>>() {
                        });

                Map<String, String> result = new HashMap<>();
                result.put("bsc_auth_address", String.valueOf(approveMap.getOrDefault("bsc_auth_address", "")));

                return result;
            }
        } catch (Exception e) {
            log.error("解析授权地址配置失败", e);
        }

        // 返回默认地址
        Map<String, String> defaultApprove = new HashMap<>();
        defaultApprove.put("bsc_auth_address", "");

        return defaultApprove;
    }

    /**
     * 获取兑换开关配置
     */
    private Map<String, Integer> getExchangeConfig() {
        try {
            Option exchangeOption = optionService.getOne(
                    new LambdaQueryWrapper<Option>().eq(Option::getOptionName, "exchange"));
            String exchangeJson = exchangeOption != null ? exchangeOption.getOptionValue() : null;
            if (exchangeJson != null && !exchangeJson.trim().isEmpty()) {
                Map<String, Object> exchangeMap = objectMapper.readValue(exchangeJson,
                        new TypeReference<Map<String, Object>>() {
                        });

                Map<String, Integer> result = new HashMap<>();
                result.put("exchange_switch_usdt2lct", parseInteger(exchangeMap.get("exchange_switch_usdt2lct")));
                result.put("exchange_switch_lct2usdt", parseInteger(exchangeMap.get("exchange_switch_lct2usdt")));
                return result;
            }
        } catch (Exception e) {
            log.error("解析兑换开关配置失败", e);
        }

        // 返回默认开关状态
        Map<String, Integer> defaultExchange = new HashMap<>();
        defaultExchange.put("exchange_switch_usdt2lct", 1);
        defaultExchange.put("exchange_switch_lct2usdt", 1);
        return defaultExchange;
    }

    /**
     * 获取价格历史数据
     */
    public Map<String, List<String>> getPriceHistory() {
        log.debug("开始获取价格历史数据...");

        // 生成近15天的日期列表
        List<String> dates = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (int i = 14; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            dates.add(date.format(formatter));
        }

        // 获取价格历史数据
        LocalDateTime startDate = LocalDate.now().minusDays(15).atStartOfDay();
        LocalDateTime endDate = LocalDate.now().atTime(23, 59, 59);

        List<Prices> lctusdtRecords = pricesService.getDailyLatestPrices("lct_usdt", startDate, endDate);
        List<Prices> usdtlctRecords = pricesService.getDailyLatestPrices("usdt_lct", startDate, endDate);

        // 构建价格列表
        List<String> lctusdtList = buildPriceList(dates, lctusdtRecords);
        List<String> usdtlctList = buildPriceList(dates, usdtlctRecords);

        Map<String, List<String>> result = new HashMap<>();
        result.put("dates", dates);
        result.put("lctusdtList", lctusdtList);
        result.put("usdtlctList", usdtlctList);

        log.debug("价格历史数据获取完成，共{}天数据", dates.size());
        return result;
    }

    /**
     * 构建价格列表
     */
    private List<String> buildPriceList(List<String> dates, List<Prices> priceRecords) {
        // 🔥 优化1：预处理 - 构建日期到价格的映射 O(m)
        Map<String, String> priceMap = new HashMap<>(priceRecords.size());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (Prices record : priceRecords) {
            String recordDate = record.getCreatedAt().toLocalDate().format(formatter);
            priceMap.put(recordDate, processDecimals(record.getPrice()));
        }

        // 🔥 优化2：快速查找 - O(n)，替代原来的双重循环O(n²)
        List<String> priceList = new ArrayList<>(dates.size());
        for (String date : dates) {
            priceList.add(priceMap.getOrDefault(date, "0"));
        }

        return priceList;
    }

    /**
     * 处理小数位数（保留6位小数）
     */
    private String processDecimals(BigDecimal value) {
        if (value == null) {
            return "0";
        }
        return value.setScale(6, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
    }

    /**
     * 解析BigDecimal
     */
    private BigDecimal parseDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 解析Integer
     */
    private Integer parseInteger(Object value) {
        if (value == null) {
            return 0;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 获取默认基础数据
     */
    private ExchangeBasicResponse getDefaultBasicData() {
        // 生成近15天的日期列表
        List<String> dates = new ArrayList<>();
        List<String> defaultPrices = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        for (int i = 14; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            dates.add(date.format(formatter));
            defaultPrices.add("0");
        }

        return ExchangeBasicResponse.builder()
                .usdt_lct(new BigDecimal("1.0"))
                .lct_usdt(new BigDecimal("1.0"))
                .bsc_auth_address("")

                .exchange_switch_usdt2lct(1)
                .exchange_switch_lct2usdt(1)
                .dates(dates)
                .lctusdtList(new ArrayList<>(defaultPrices))
                .usdtlctList(new ArrayList<>(defaultPrices))
                .build();
    }

    /**
     * 根据地址获取用户
     */
    private User getUserByAddress(String address) {
        try {
            return userService.getOne(
                    new LambdaQueryWrapper<User>().eq(User::getUserAddress, address));
        } catch (Exception e) {
            log.error("根据地址获取用户失败 - 地址: {}", address, e);
            return null;
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderId() {
        return "EX" + System.currentTimeMillis() + "_" + (int) (Math.random() * 1000);
    }

    /**
     * 获取兑换类型
     */
    private Integer getExchangeType(Integer depositCoinType, Integer receiveCoinType) {
        if (depositCoinType.equals(CommonConstants.CoinType.USDT_BSC) &&
                receiveCoinType.equals(CommonConstants.CoinType.LCT_BSC)) {
            return CommonConstants.ExchangeType.U2LCT;
        } else if (depositCoinType.equals(CommonConstants.CoinType.LCT_BSC) &&
                (receiveCoinType.equals(CommonConstants.CoinType.USDT_BSC) ||
                        receiveCoinType.equals(CommonConstants.CoinType.USDT_TRON))) {
            return CommonConstants.ExchangeType.LCT2U;
        }
        return CommonConstants.ExchangeType.U2LCT; // 默认
    }

    /**
     * 获取币种符号
     */
    private String getCoinSymbol(Integer coinType) {
        return CommonConstants.getCoinCode(coinType);
    }

    /**
     * 获取合约地址
     */
    private String getContractAddress(Integer coinType) {
        return blockchainNetworkConfig.getContractAddress(coinType);
    }

    /**
     * 获取精度
     */
    private Integer getDecimals(Integer coinType) {
        return 18; // 默认18位精度
    }

    /**
     * 获取BSC兑换收款地址 - 与PHP config('coins.BSC.exchange_receive_address') 一致
     * 
     * PHP中的配置路径: config/coins.php -> BSC.exchange_receive_address
     * 当前值: 0xf3E5b6C835d20bbaD886070F3316BF88Ea13f4d3 (2024-04-22更换)
     */
    private String getBscExchangeAddress() {
        // 这个地址来自PHP配置文件，不是从数据库lct_options表读取
        // 对应PHP代码: config('coins.BSC.exchange_receive_address')
        return "0xf3E5b6C835d20bbaD886070F3316BF88Ea13f4d3";
    }

    /**
     * 获取SWFT配置
     * 从数据库options表读取，与PHP版本保持一致
     */
    private Map<String, String> getSwftConfig() {
        try {
            // 先尝试获取exchange配置（兑换功能使用）
            String exchangeConfigJson = optionService.getOption("exchange");
            Map<String, String> config = new HashMap<>();

            if (exchangeConfigJson != null && !exchangeConfigJson.isEmpty()) {
                Map<String, Object> exchangeConfig = objectMapper.readValue(exchangeConfigJson,
                        new TypeReference<Map<String, Object>>() {
                        });

                // 提取兑换配置中的收款地址
                if (exchangeConfig.containsKey("usdt_trx_receive_address")) {
                    config.put("usdt_trx_receive_address", exchangeConfig.get("usdt_trx_receive_address").toString());
                }
                if (exchangeConfig.containsKey("usdt_bsc_receive_address")) {
                    config.put("usdt_bsc_receive_address", exchangeConfig.get("usdt_bsc_receive_address").toString());
                }
            }

            // 再获取swft配置（销毁功能使用）
            String swftConfigJson = optionService.getOption("swft");
            if (swftConfigJson != null && !swftConfigJson.isEmpty()) {
                Map<String, Object> swftConfig = objectMapper.readValue(swftConfigJson,
                        new TypeReference<Map<String, Object>>() {
                        });

                // 提取SWFT配置中的地址
                if (swftConfig.containsKey("lct_destination_address")) {
                    config.put("lct_destination_address", swftConfig.get("lct_destination_address").toString());
                }
                if (swftConfig.containsKey("usdt_destination_address")) {
                    config.put("usdt_destination_address", swftConfig.get("usdt_destination_address").toString());
                }
                if (swftConfig.containsKey("default_usdt_chain")) {
                    config.put("default_usdt_chain", swftConfig.get("default_usdt_chain").toString());
                }
            }

            // 如果数据库配置为空，使用默认配置
            if (config.isEmpty()) {
                throw new BusinessException("数据库中SWFT配置不存在");
            }

            log.debug("从数据库获取SWFT配置成功: {}", config);
            return config;

        } catch (Exception e) {
            throw new BusinessException("数据库中SWFT配置不存在");
        }
    }

    /**
     * 处理LCT兑换USDT - 与PHP逻辑一致
     */
    private ExchangeCreateResponse handleLctToUsdtExchange(ExchangeCreateRequest request, User user,
            String userAddress) {
        // 最少兑换10个LCT
        if (request.getDepositAmount().compareTo(BigDecimal.valueOf(10)) < 0) {
            throw new BusinessException("最少兑换LCT 10个");
        }

        // 生成自己的订单号，不使用SWFT
        String orderId = generateOrderId();

        // 重新计算接收数量（根据价格配置）
        Map<String, BigDecimal> priceConfig = getPriceConfig();
        BigDecimal lctUsdtRate = priceConfig.get("lct_usdt");
        if (lctUsdtRate == null) {
            throw new BusinessException("LCT兑USDT汇率配置不存在");
        }

        BigDecimal receiveAmount = request.getDepositAmount().multiply(lctUsdtRate);
        if (receiveAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("数量有误，请重新输入");
        }

        // 创建兑换订单
        Exchange exchange = Exchange.builder()
                .orderId(orderId)
                .userId(user.getId().longValue())
                .address(userAddress)
                .depositAmount(request.getDepositAmount())
                .depositCoinType(request.getDepositCoinType())
                .receiveAmount(receiveAmount)
                .receiveCoinType(request.getReceiveCoinType())
                .receiveAddress(request.getReceiveAddress())
                .status(CommonConstants.ExchangeStatus.PAYING)
                .actionType(CommonConstants.ExchangeActionType.SYSTEM)
                .exchangeType(CommonConstants.ExchangeType.LCT2U)
                .ip(request.getIp())
                .build();

        save(exchange);

        // 返回响应，使用系统配置的收款地址
        String bscExchangeAddress = getBscExchangeAddress();
        return ExchangeCreateResponse.success(
                orderId,
                bscExchangeAddress,
                request.getDepositAmount(),
                CommonConstants.COIN_CODE_LCT,
                getContractAddress(request.getDepositCoinType()),
                getDecimals(request.getDepositCoinType()),
                receiveAmount,
                getCoinSymbol(request.getReceiveCoinType()),
                request.getReceiveAddress(),
                CommonConstants.ExchangeType.LCT2U);
    }

    /**
     * 处理USDT兑换LCT - 通过SWFT系统
     */
    private ExchangeCreateResponse handleUsdtToLctExchange(ExchangeCreateRequest request, User user,
            String userAddress) {
        // 获取配置
        Map<String, String> swftConfig = getSwftConfig();

        // 重新计算接收数量（根据价格配置）
        Map<String, BigDecimal> priceConfig = getPriceConfig();
        BigDecimal usdtLctRate = priceConfig.get("usdt_lct");
        if (usdtLctRate == null) {
            throw new BusinessException("USDT兑LCT汇率配置不存在");
        }

        BigDecimal receiveAmount = request.getDepositAmount().multiply(usdtLctRate);
        if (receiveAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("数量有误，请重新输入");
        }

        // 构建SWFT兑换参数 - 系统接管模式
        String depositCoinCode = getCoinSymbol(request.getDepositCoinType());
        String receiveCoinCode;
        String swftReceiveAddress;
        BigDecimal swftReceiveAmount;

        // 根据存入币种类型动态确定接收币种和地址
        if (request.getDepositCoinType() == CommonConstants.CoinType.USDT_BSC) {
            // 存入BSC的USDT，接收TRON的USDT
            receiveCoinCode = getCoinSymbol(CommonConstants.CoinType.USDT_TRON);
            swftReceiveAddress = swftConfig.get("usdt_trx_receive_address");
            swftReceiveAmount = request.getDepositAmount(); // 使用存入数量作为接收数量
        } else if (request.getDepositCoinType() == CommonConstants.CoinType.USDT_TRON) {
            // 存入TRON的USDT，接收BSC的USDT
            receiveCoinCode = getCoinSymbol(CommonConstants.CoinType.USDT_BSC);
            swftReceiveAddress = swftConfig.get("usdt_bsc_receive_address");
            swftReceiveAmount = request.getDepositAmount(); // 使用存入数量作为接收数量
        } else {
            throw new BusinessException("不支持的存入币种类型");
        }

        // 调用SWFT创建兑换订单
        SwftAccountExchangeRequest swftRequest = SwftAccountExchangeRequest.builder()
                .depositCoinCode(depositCoinCode)
                .receiveCoinCode(receiveCoinCode)
                .depositCoinAmt(request.getDepositAmount())
                .receiveCoinAmt(swftReceiveAmount)
                .destinationAddr(swftReceiveAddress)
                .refundAddr(userAddress)
                .equipmentNo(userAddress)
                .sourceType("H5")
                .sourceFlag("9778abb8ca135929")
                .isNoGas(false)
                .build();

        SwftAccountExchangeResponse swftResponse = swftService.accountExchange(swftRequest);
        if (swftResponse == null) {
            throw new BusinessException("SWFT订单创建失败");
        }

        // 创建兑换订单
        Exchange exchange = Exchange.builder()
                .orderId(swftResponse.getOrderId())
                .userId(user.getId().longValue())
                .address(userAddress)
                .depositAmount(request.getDepositAmount())
                .depositCoinType(request.getDepositCoinType())
                .receiveAmount(receiveAmount)
                .receiveCoinType(request.getReceiveCoinType())
                .receiveAddress(request.getReceiveAddress())
                .usdtReceiveAddress(swftReceiveAddress)
                .status(CommonConstants.ExchangeStatus.PAYING)
                .exchangeType(CommonConstants.ExchangeType.U2LCT)
                .actionType(CommonConstants.ExchangeActionType.SYSTEM)
                .ip(request.getIp())
                .build();

        save(exchange);

        // 构建响应
        return ExchangeCreateResponse.success(
                swftResponse.getOrderId(),
                swftResponse.getPlatformAddr(), // 真实的SWFT支付地址
                swftResponse.getDepositCoinAmt(),
                swftResponse.getDepositCoinCode(),
                getContractAddress(request.getDepositCoinType()),
                getDecimals(request.getDepositCoinType()),
                receiveAmount,
                getCoinSymbol(request.getReceiveCoinType()),
                request.getReceiveAddress(),
                CommonConstants.ExchangeType.U2LCT);
    }

    /**
     * 验证交易哈希格式
     * 检查是否为64位十六进制字符串
     */
    private boolean isValidTransactionHash(String txhash) {
        if (txhash == null || txhash.isEmpty()) {
            return false;
        }

        // 移除0x前缀（如果有）
        String cleanHash = txhash.toLowerCase().startsWith("0x") ? txhash.substring(2) : txhash;

        // 验证长度和格式
        return cleanHash.length() == 64 && Pattern.matches("^[0-9a-f]+$", cleanHash);
    }

    @Override
    @Transactional
    public boolean payExchange(ExchangePayRequest request, String userAddress) {
        log.info("兑换支付确认 - 用户: {}, 订单: {}", userAddress, request.getOrderId());

        try {
            // 1. 验证交易哈希格式（与PHP版本一致）
            if (!isValidTransactionHash(request.getTxhash())) {
                log.warn("交易哈希格式错误 - 用户: {}, 哈希: {}", userAddress, request.getTxhash());
                throw new BusinessException("交易哈希格式错误");
            }

            // 2. 查找订单
            Exchange exchange = getExchangeByOrderNo(request.getOrderId());
            if (exchange == null) {
                log.warn("订单不存在 - 用户: {}, 订单号: {}", userAddress, request.getOrderId());
                throw new BusinessException("订单不存在");
            }

            // 3. 验证订单状态和用户地址（与PHP版本逻辑一致）
            if (exchange.getStatus() != CommonConstants.ExchangeStatus.PAYING) {
                log.warn("订单状态错误 - 用户: {}, 订单号: {}, 当前状态: {}",
                        userAddress, request.getOrderId(), exchange.getStatus());
                throw new BusinessException("订单状态错误");
            }

            if (!userAddress.equalsIgnoreCase(exchange.getAddress())) {
                log.warn("用户地址不匹配 - 请求用户: {}, 订单用户: {}", userAddress, exchange.getAddress());
                throw new BusinessException("用户地址不匹配");
            }

            // 4. 检查交易哈希是否已被使用
            boolean txhashExists = this.lambdaQuery()
                    .eq(Exchange::getTxhash, request.getTxhash())
                    .exists();

            if (txhashExists) {
                log.info("交易哈希已存在，返回成功状态 - 用户: {}, 哈希: {}", userAddress, request.getTxhash());
                return true; // ✅ 重复哈希直接返回成功，符合幂等性原则
            }

            // 5. 更新订单状态（原子操作）
            boolean updated = this.lambdaUpdate()
                    .eq(Exchange::getId, exchange.getId())
                    .eq(Exchange::getStatus, CommonConstants.ExchangeStatus.PAYING) // 再次确认状态
                    .set(Exchange::getTxhash, request.getTxhash())
                    .set(Exchange::getStatus, CommonConstants.ExchangeStatus.PROCESSING)
                    .set(Exchange::getUpdatedAt, LocalDateTime.now())
                    .update();

            if (!updated) {
                log.warn("订单状态更新失败 - 用户: {}, 订单号: {}", userAddress, request.getOrderId());
                throw new BusinessException("订单状态更新失败，可能已被处理");
            }

            log.info("兑换支付确认成功 - 用户: {}, 订单号: {}, 交易哈希: {}",
                    userAddress, request.getOrderId(), request.getTxhash());
            return true;

        } catch (BusinessException e) {
            // ✅ 业务异常直接重新抛出，让事务正确回滚
            log.warn("兑换支付确认业务异常 - 用户: {}, 订单号: {}, 原因: {}", 
                     userAddress, request.getOrderId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            // ✅ 系统异常包装后重新抛出，让事务正确回滚
            log.error("兑换支付确认系统异常 - 用户: {}, 订单号: {}", userAddress, request.getOrderId(), e);
            throw new BusinessException("支付确认失败: " + e.getMessage());
        }
    }
}