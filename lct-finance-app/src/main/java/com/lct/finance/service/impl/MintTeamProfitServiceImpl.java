package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.MintTeamProfitMapper;
import com.lct.finance.model.entity.MintTeamProfit;
import com.lct.finance.model.entity.User;
import com.lct.finance.service.IMintTeamProfitService;
import com.lct.finance.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 每日铸造团队收益服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class MintTeamProfitServiceImpl extends ServiceImpl<MintTeamProfitMapper, MintTeamProfit>
        implements IMintTeamProfitService {

    @Autowired
    private IUserService userService;

    @Override
    public BigDecimal getTodayTeamProfit(Long userId, LocalDate profitDate) {
        log.debug("查询用户今日团队收益: userId={}, profitDate={}", userId, profitDate);

        List<MintTeamProfit> teamProfits = this.list(
                new LambdaQueryWrapper<MintTeamProfit>()
                        .eq(MintTeamProfit::getUserId, userId.intValue())
                        .eq(MintTeamProfit::getProfitDate, profitDate));

        BigDecimal totalTeamProfit = teamProfits.stream()
                .map(MintTeamProfit::getActualAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.debug("用户今日团队收益: userId={}, profitDate={}, totalTeamProfit={}", userId, profitDate, totalTeamProfit);
        return totalTeamProfit;
    }

    @Override
    public IPage<MintTeamProfit> selectTeamProfitHistoryOptimized(Page<MintTeamProfit> page, Long userId, String date) {
        try {
            // 使用LambdaQueryWrapper构建查询条件
            LambdaQueryWrapper<MintTeamProfit> queryWrapper = new LambdaQueryWrapper<MintTeamProfit>()
                    .eq(MintTeamProfit::getUserId, userId)
                    .orderByDesc(MintTeamProfit::getId);

            // 处理日期查询条件
            if (date != null && !date.isEmpty()) {
                if (date.contains("-")) {
                    // 标准格式 yyyy-MM-dd
                    queryWrapper.eq(MintTeamProfit::getProfitDate, date);
                } else if (date.length() == 8) {
                    // 紧凑格式 yyyyMMdd
                    queryWrapper.apply("DATE_FORMAT(profit_date, '%Y%m%d') = {0}", date);
                }
            }

            // 直接使用page方法进行分页查询
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("查询用户团队收益记录失败: userId={}, date={}", userId, date, e);
            throw e;
        }
    }

    @Override
    public IPage<Map<String, Object>> selectTeamProfitHistoryWithSource(Page<Map<String, Object>> page, Long userId,
            String date) {
        try {
            log.debug("查询用户团队收益记录（含源用户地址）: userId={}, date={}, page={}", userId, date, page.getCurrent());

            // 查询团队收益记录
            LambdaQueryWrapper<MintTeamProfit> queryWrapper = new LambdaQueryWrapper<MintTeamProfit>()
                    .eq(MintTeamProfit::getUserId, userId)
                    .orderByDesc(MintTeamProfit::getId);

            // 处理日期查询条件
            if (date != null && !date.isEmpty()) {
                if (date.contains("-")) {
                    // 标准格式 yyyy-MM-dd
                    queryWrapper.eq(MintTeamProfit::getProfitDate, date);
                } else if (date.length() == 8) {
                    // 紧凑格式 yyyyMMdd
                    queryWrapper.apply("DATE_FORMAT(profit_date, '%Y%m%d') = {0}", date);
                }
            }

            // 进行分页查询
            Page<MintTeamProfit> profitPage = new Page<>(page.getCurrent(), page.getSize());
            IPage<MintTeamProfit> profitResult = this.page(profitPage, queryWrapper);

            // 提取所有源用户ID
            List<Integer> sourceUserIds = profitResult.getRecords().stream()
                    .map(MintTeamProfit::getSourceUserId)
                    .collect(Collectors.toList());

            // 批量查询用户地址
            final Map<Integer, String> userAddressMap = new HashMap<>();
            if (!sourceUserIds.isEmpty()) {
                List<User> users = userService.list(new LambdaQueryWrapper<User>()
                        .in(User::getId, sourceUserIds)
                        .eq(User::getState, 1)
                        .select(User::getId, User::getUserAddress));

                userAddressMap.putAll(users.stream()
                        .collect(Collectors.toMap(
                                user -> user.getId().intValue(),
                                User::getUserAddress,
                                (addr1, addr2) -> addr1)));
            }

            // 转换为带源用户地址的Map列表
            List<Map<String, Object>> resultList = profitResult.getRecords().stream()
                    .map(profit -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", profit.getId());
                        map.put("userId", profit.getUserId());
                        map.put("sourceUserId", profit.getSourceUserId());
                        map.put("amount", profit.getAmount());
                        map.put("actualAmount", profit.getActualAmount());
                        map.put("profitDate", profit.getProfitDate());
                        map.put("createdAt", profit.getCreatedAt());
                        map.put("updatedAt", profit.getUpdatedAt());
                        map.put("sourceUserAddress", userAddressMap.getOrDefault(profit.getSourceUserId(), ""));
                        return map;
                    })
                    .collect(Collectors.toList());

            // 构建最终分页结果
            Page<Map<String, Object>> resultPage = new Page<>(page.getCurrent(), page.getSize(),
                    profitResult.getTotal());
            resultPage.setRecords(resultList);

            return resultPage;
        } catch (Exception e) {
            log.error("查询用户团队收益记录（含源用户地址）失败: userId={}, date={}", userId, date, e);
            throw e;
        }
    }

    @Override
    public List<Map<String, Object>> selectUserAddressBatch(List<Integer> userIds) {
        try {
            log.debug("批量查询用户地址映射: userIds={}", userIds);

            if (userIds == null || userIds.isEmpty()) {
                return new ArrayList<>();
            }

            // 转换为Long类型的用户ID列表
            List<Long> longUserIds = userIds.stream()
                    .map(Integer::longValue)
                    .collect(Collectors.toList());

            // 查询用户信息，只选择id和user_address字段
            List<User> users = userService.list(new LambdaQueryWrapper<User>()
                    .in(User::getId, longUserIds)
                    .eq(User::getState, 1)
                    .select(User::getId, User::getUserAddress));

            // 转换为Map列表
            return users.stream()
                    .map(user -> {
                        Map<String, Object> map = new HashMap<>(2);
                        map.put("id", user.getId());
                        map.put("user_address", user.getUserAddress());
                        return map;
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("批量查询用户地址映射失败: userIds={}", userIds, e);
            throw e;
        }
    }
}