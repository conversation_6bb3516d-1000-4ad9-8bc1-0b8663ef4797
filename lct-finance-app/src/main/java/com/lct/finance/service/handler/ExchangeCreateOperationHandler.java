package com.lct.finance.service.handler;

import com.lct.finance.context.UserContext;
import com.lct.finance.model.dto.ExchangeCreateRequest;
import com.lct.finance.model.dto.ExchangeCreateResponse;
import com.lct.finance.model.request.OperationValidationRequest;
import com.lct.finance.model.response.OperationValidationResult;
import com.lct.finance.service.IExchangeService;
import com.lct.finance.service.IRiskAssessmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 兑换创建订单操作处理器
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class ExchangeCreateOperationHandler
        extends AbstractOperationHandler<ExchangeCreateRequest, ExchangeCreateResponse> {

    @Autowired
    private IExchangeService exchangeService;

    @Autowired
    private IRiskAssessmentService riskAssessmentService;

    @Override
    protected OperationValidationRequest buildValidationRequest(ExchangeCreateRequest request, String clientIp) {
        return OperationValidationRequest.builder()
                .address(UserContext.getCurrentUser().getUserAddress())
                .operation("EXCHANGE_CREATE")
//                .message(request.getMessage())
//                .signature(request.getSign())
//                .nonce(request.getNonce())
                .clientIp(clientIp)
                .deviceFingerprint(request.getDeviceFingerprint())
                .amount(request.getDepositAmount().toString())
                .performRiskAssessment(true)
                .recordOperationLog(true)
                .build();
    }

    @Override
    protected ExchangeCreateResponse executeBusinessLogic(ExchangeCreateRequest request, String clientIp,
            OperationValidationResult validationResult) {

        log.info("执行兑换创建业务逻辑: {}, depositAmount={}, depositCoinType={} -> receiveCoinType={}",
                UserContext.getCurrentUserInfo(), request.getDepositAmount(),
                request.getDepositCoinType(), request.getReceiveCoinType());

        // 调用原有的兑换创建逻辑，不需要重复验证
        return exchangeService.createExchange(request, UserContext.getCurrentUser().getUserAddress());
    }

    @Override
    protected String getOperationName() {
        return "兑换创建";
    }

    @Override
    protected void onHighRiskDetected(OperationValidationRequest request, OperationValidationResult result) {
        // 兑换创建的高风险处理
        log.warn("高风险兑换创建操作被检测到: address={}, riskLevel={}",
                request.getAddress(), result.getRiskLevel());

        // TODO: 可以在这里实现额外的安全措施
        // 1. 限制创建订单数量
        // 2. 要求额外验证
        // 3. 发送风控告警
    }
}