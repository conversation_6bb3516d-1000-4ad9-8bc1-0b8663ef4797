package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.NoticeModalMapper;
import com.lct.finance.model.entity.NoticeModal;
import com.lct.finance.service.INoticeModalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 公告弹窗服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class NoticeModalServiceImpl extends ServiceImpl<NoticeModalMapper, NoticeModal> implements INoticeModalService {

    // 继承ServiceImpl，提供基础的CRUD操作
    // 如需要复杂业务逻辑，可以在此添加自定义方法实现
}