package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.UserOperationLogMapper;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.entity.UserOperationLog;
import com.lct.finance.service.IUserOperationLogService;
import com.lct.finance.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户操作日志服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Slf4j
@Service
public class UserOperationLogServiceImpl extends ServiceImpl<UserOperationLogMapper, UserOperationLog>
        implements IUserOperationLogService {

    @Autowired
    private IUserService userService;

    @Override
    public int countRecentOperations(String userAddress, String operationType, int minutes) {
        // 复杂查询：涉及时间计算，保留Mapper方法
        return baseMapper.countRecentOperations(userAddress, operationType, minutes);
    }

    @Override
    public int getRecentOperationCount(String userAddress, String operation, int minutes) {
        // 调用已有方法，避免重复实现
        return countRecentOperations(userAddress, operation, minutes);
    }

    @Override
    public BigDecimal getAverageOperationAmount(String userAddress, String operationType) {
        // 复杂查询：聚合计算，保留Mapper方法
        return baseMapper.getAverageOperationAmount(userAddress, operationType);
    }

    @Override
    public String getLastDeviceFingerprint(String userAddress) {
        // 使用LambdaQueryWrapper替代原有的Mapper方法
        UserOperationLog log = this.lambdaQuery()
                .eq(UserOperationLog::getUserAddress, userAddress)
                .isNotNull(UserOperationLog::getDeviceFingerprint)
                .orderByDesc(UserOperationLog::getOperationTime)
                .last("LIMIT 1")
                .one();
        return log != null ? log.getDeviceFingerprint() : null;
    }

    @Override
    public List<UserOperationLog> getRecentOperations(String userAddress, int limit) {
        // 使用LambdaQueryWrapper替代原有的Mapper方法
        return this.lambdaQuery()
                .eq(UserOperationLog::getUserAddress, userAddress)
                .orderByDesc(UserOperationLog::getOperationTime)
                .last("LIMIT " + limit)
                .list();
    }

    @Override
    public void recordOperationLog(String address, String operation, String amount,
            String clientIp, int riskLevel, int status) {
        // 调用扩展版本的方法，不包含评分详情
        recordOperationLog(address, operation, amount, clientIp, null, riskLevel, status, null);
    }

    @Override
    public void recordOperationLog(String address, String operation, String amount,
            String clientIp, String deviceFingerprint, int riskLevel, int status, String riskScoreDetail) {
        try {
            log.info(
                    "记录用户操作日志: address={}, operation={}, amount={}, clientIp={}, deviceFingerprint={}, riskLevel={}, status={}",
                    address, operation, amount, clientIp, deviceFingerprint, riskLevel, status);

            // 详细调试deviceFingerprint
            if (deviceFingerprint == null) {
                log.warn("⚠️  操作日志deviceFingerprint为null: address={}, operation={}", address, operation);
            } else if (deviceFingerprint.trim().isEmpty()) {
                log.warn("⚠️  操作日志deviceFingerprint为空字符串: address={}, operation={}", address, operation);
            } else {
                log.info("✅ 操作日志deviceFingerprint正常: address={}, operation={}, fingerprint={}...(前20字符)",
                        address, operation,
                        deviceFingerprint.length() > 20 ? deviceFingerprint.substring(0, 20) : deviceFingerprint);
            }

            UserOperationLog operationLog = new UserOperationLog();
            operationLog.setUserAddress(address);
            operationLog.setOperationType(operation);
            operationLog.setClientIp(clientIp);
            operationLog.setDeviceFingerprint(deviceFingerprint);
            operationLog.setRiskLevel(riskLevel);
            operationLog.setStatus(status);
            operationLog.setOperationTime(LocalDateTime.now());

            // 设置金额（如果有）
            if (amount != null && !amount.isEmpty()) {
                try {
                    operationLog.setAmount(new BigDecimal(amount));
                } catch (NumberFormatException e) {
                    log.warn("操作金额格式异常: amount={}", amount, e);
                    operationLog.setAmount(BigDecimal.ZERO);
                }
            }

            // 设置风险评分详情
            if (riskScoreDetail != null && !riskScoreDetail.trim().isEmpty()) {
                operationLog.setRiskScoreDetail(riskScoreDetail);
                log.debug("保存操作风险评分详情: address={}, operation={}, detail={}", address, operation, riskScoreDetail);
            }

            // 获取IP地理位置
            String location = com.lct.finance.utils.ClientIpUtils.getIpLocation(clientIp);
            operationLog.setIpLocation(location);

            // 根据用户地址查找用户ID
            User user = findUserByAddress(address);
            if (user != null) {
                operationLog.setUserId(user.getId());
                log.info("设置操作日志用户ID: address={}, operation={}, userId={}", address, operation, user.getId());
            } else {
                log.warn("未找到用户记录，无法设置用户ID: address={}, operation={}", address, operation);
            }

            // 保存前再次检查
            log.info("保存前最终检查: address={}, operation={}, deviceFingerprint={}, hasRiskDetail={}",
                    address, operation, operationLog.getDeviceFingerprint(), riskScoreDetail != null);

            // 保存日志
            save(operationLog);

            // 保存后验证
            log.info(
                    "用户操作日志记录成功: id={}, address={}, operation={}, location={}, userId={}, deviceFingerprint={}, hasRiskDetail={}",
                    operationLog.getId(), address, operation, location, operationLog.getUserId(),
                    operationLog.getDeviceFingerprint(), riskScoreDetail != null);
        } catch (Exception e) {
            log.error("记录用户操作日志失败: address={}, operation={}, clientIp={}, deviceFingerprint={}",
                    address, operation, clientIp, deviceFingerprint, e);
        }
    }

    /**
     * 根据用户地址查找用户信息
     */
    private User findUserByAddress(String address) {
        try {
            // 使用IUserService的findByAddress方法
            return userService.findByAddress(address);
        } catch (Exception e) {
            log.warn("查找用户失败: address={}", address, e);
            return null;
        }
    }
}