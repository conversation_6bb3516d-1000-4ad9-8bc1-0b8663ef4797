package com.lct.finance.service.impl;

import com.lct.finance.model.dto.RiskAssessmentRequest;
import com.lct.finance.model.dto.RiskAssessmentResult;
import com.lct.finance.model.dto.RiskScoreDetail;
import com.lct.finance.service.IRiskAssessmentService;
import com.lct.finance.service.IUserLoginLogService;
import com.lct.finance.service.IUserOperationLogService;
import com.lct.finance.utils.ClientIpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 风险评估服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Slf4j
@Service
public class RiskAssessmentServiceImpl implements IRiskAssessmentService {

    @Autowired
    private IUserLoginLogService userLoginLogService;

    @Autowired
    private IUserOperationLogService userOperationLogService;

    @Override
    public RiskAssessmentResult evaluateRisk(RiskAssessmentRequest request) {
        try {
            log.info("开始评估风险: operation={}, address={}", request.getOperation(), request.getAddress());

            List<String> riskFactors = new ArrayList<>();

            // 执行详细的风险评估，获取评分详情
            RiskScoreDetail scoreDetail = evaluateRiskLevelWithDetail(
                    request.getOperation(),
                    request.getAddress(),
                    request.getClientIp(),
                    request.getAmount(),
                    request.getDeviceFingerprint(),
                    riskFactors);

            // 计算最终总分
            scoreDetail.calculateTotalScore();
            int finalRiskLevel = scoreDetail.getTotalScore();

            // 判断是否需要额外验证（风险级别大于70）
            boolean requiresAdditionalVerification = finalRiskLevel > 70;

            log.info("风险评估完成: operation={}, address={}, riskLevel={}, requiresAdditionalVerification={}, detail={}",
                    request.getOperation(), request.getAddress(), finalRiskLevel,
                    requiresAdditionalVerification, scoreDetail.getFormattedDetail());

            RiskAssessmentResult result = new RiskAssessmentResult();
            result.setRiskLevel(finalRiskLevel);
            result.setRequiresAdditionalVerification(requiresAdditionalVerification);
            result.setRiskFactors(riskFactors);
            result.setScoreDetail(scoreDetail); // 设置详细评分信息

            return result;

        } catch (Exception e) {
            log.error("风险评估异常: address={}, operation={}",
                    request.getAddress(), request.getOperation(), e);

            // 发生异常时返回中等风险结果
            List<String> factors = new ArrayList<>();
            factors.add("风险评估过程发生异常: " + e.getMessage());

            RiskScoreDetail errorScoreDetail = RiskScoreDetail.builder()
                    .baseScore(50)
                    .totalScore(50)
                    .build();
            errorScoreDetail.addScoreItem("异常处理", 50, "评估过程发生异常，使用默认风险分数");

            RiskAssessmentResult result = new RiskAssessmentResult();
            result.setRiskLevel(50);
            result.setRequiresAdditionalVerification(true);
            result.setRiskFactors(factors);
            result.setScoreDetail(errorScoreDetail);
            return result;
        }
    }

    @Override
    public void recordLoginLog(String address, String chainType, String clientIp,
            String deviceFingerprint, int riskLevel, int status) {
        // 调用默认方法，不包含评分详情
        recordLoginLogWithDetail(address, chainType, clientIp, deviceFingerprint, riskLevel, status, null);
    }

    /**
     * 记录登录日志（包含评分详情）
     */
    public void recordLoginLogWithDetail(String address, String chainType, String clientIp,
            String deviceFingerprint, int riskLevel, int status, String riskScoreDetail) {
        try {
            // 调用UserLoginLogServiceImpl的扩展方法
            if (userLoginLogService instanceof com.lct.finance.service.impl.UserLoginLogServiceImpl) {
                ((com.lct.finance.service.impl.UserLoginLogServiceImpl) userLoginLogService)
                        .recordLoginLog(address, chainType, clientIp, deviceFingerprint, riskLevel, status,
                                riskScoreDetail);
            } else {
                // 降级到原有方法
                userLoginLogService.recordLoginLog(address, chainType, clientIp, deviceFingerprint, riskLevel, status);
            }
        } catch (Exception e) {
            log.error("记录登录日志失败: address={}, chainType={}", address, chainType, e);
            // 降级到原有方法
            userLoginLogService.recordLoginLog(address, chainType, clientIp, deviceFingerprint, riskLevel, status);
        }
    }

    @Override
    public void recordOperationLog(String address, String operation, String amount,
            String clientIp, int riskLevel, int status) {
        // 调用默认方法，不包含评分详情
        recordOperationLogWithDetail(address, operation, amount, clientIp, riskLevel, status, null, null);
    }

    /**
     * 记录操作日志（包含评分详情）
     */
    public void recordOperationLogWithDetail(String address, String operation, String amount,
            String clientIp, int riskLevel, int status, String deviceFingerprint, String riskScoreDetail) {
        try {
            // 调用UserOperationLogServiceImpl的扩展方法
            if (userOperationLogService instanceof com.lct.finance.service.impl.UserOperationLogServiceImpl) {
                ((com.lct.finance.service.impl.UserOperationLogServiceImpl) userOperationLogService)
                        .recordOperationLog(address, operation, amount, clientIp, deviceFingerprint, riskLevel, status,
                                riskScoreDetail);
            } else {
                // 降级到原有方法
                userOperationLogService.recordOperationLog(address, operation, amount, clientIp, riskLevel, status);
            }
        } catch (Exception e) {
            log.error("记录操作日志失败: address={}, operation={}", address, operation, e);
            // 降级到原有方法
            userOperationLogService.recordOperationLog(address, operation, amount, clientIp, riskLevel, status);
        }
    }

    /**
     * 执行详细风险评估，返回评分详情
     */
    private RiskScoreDetail evaluateRiskLevelWithDetail(String operation, String address, String clientIp,
            String amount, String deviceFingerprint, List<String> riskFactors) {

        // 创建评分详情对象
        RiskScoreDetail scoreDetail = RiskScoreDetail.builder().build();

        // 基于操作类型的基础风险（这是起始分数，不算在额外风险项中）
        int baseRisk = getBaseRiskByOperationType(operation);
        scoreDetail.setBaseScore(baseRisk);
        riskFactors.add(String.format("操作类型[%s]基础风险: %d", operation, baseRisk));

        // 以下是额外的风险调整项目

        // 1. IP变化检测：比较用户历史IP与当前IP
        int ipChangeRisk = detectIpChange(address, clientIp);
        if (ipChangeRisk > 0) {
            scoreDetail.addScoreItem("IP变化", ipChangeRisk, "检测到IP地址变化");
            riskFactors.add(String.format("IP变化风险: +%d", ipChangeRisk));
        } else {
            scoreDetail.addScoreItem("IP变化", 0, "IP地址未变化");
            riskFactors.add("IP地址未变化");
        }

        // 2. 地理位置异常检测
        int geolocationRisk = detectGeolocationAnomaly(address, clientIp);
        if (geolocationRisk > 0) {
            scoreDetail.addScoreItem("地理位置异常", geolocationRisk, "检测到地理位置异常变化");
            riskFactors.add(String.format("地理位置异常风险: +%d", geolocationRisk));
        } else {
            scoreDetail.addScoreItem("地理位置异常", 0, "地理位置未变化");
            riskFactors.add("地理位置未变化");
        }

        // 3. 操作频率分析：检查用户短时间内的操作频率
        int frequencyRisk = analyzeOperationFrequency(address, operation);
        if (frequencyRisk > 0) {
            scoreDetail.addScoreItem("操作频率", frequencyRisk, "检测到高频操作");
            riskFactors.add(String.format("操作频率风险: +%d", frequencyRisk));
        } else {
            scoreDetail.addScoreItem("操作频率", 0, "操作频率正常");
            riskFactors.add("操作频率正常");
        }

        // 4. 操作金额评估：提现/兑换金额异常大时增加风险分数
        if (StringUtils.hasText(amount)) {
            int amountRisk = evaluateOperationAmount(address, operation, amount);
            if (amountRisk > 0) {
                scoreDetail.addScoreItem("操作金额", amountRisk, "检测到大额操作");
                riskFactors.add(String.format("操作金额风险: +%d", amountRisk));
            } else {
                scoreDetail.addScoreItem("操作金额", 0, "操作金额正常");
                riskFactors.add("操作金额正常");
            }
        } else {
            scoreDetail.addScoreItem("操作金额", 0, "操作金额正常");
            riskFactors.add("操作金额正常");
        }

        // 5. 设备指纹变化检测
        if (StringUtils.hasText(deviceFingerprint)) {
            int deviceRisk = detectDeviceFingerprintChange(address, deviceFingerprint);
            if (deviceRisk > 0) {
                scoreDetail.addScoreItem("设备变化", deviceRisk, "检测到设备指纹变化");
                riskFactors.add(String.format("设备变化风险: +%d", deviceRisk));
            } else {
                scoreDetail.addScoreItem("设备变化", 0, "设备指纹未变化");
                riskFactors.add("设备指纹未变化");
            }
        } else {
            scoreDetail.addScoreItem("设备变化", 0, "无设备指纹信息");
            riskFactors.add("无设备指纹信息");
        }

        return scoreDetail;
    }

    /**
     * 根据操作类型获取基础风险分数
     */
    private int getBaseRiskByOperationType(String operation) {
        switch (operation.toUpperCase()) {
            case "LOGIN":
                return 10; // 登录操作基础风险较低
            case "WITHDRAWAL":
                return 25; // 提现操作有一定基础风险
            case "EXCHANGE":
                return 20; // 兑换操作基础风险
            case "BURN":
                return 15; // 销毁操作基础风险
            default:
                return 20; // 未知操作类型默认中等风险
        }
    }

    /**
     * 检测IP变化
     */
    private int detectIpChange(String address, String clientIp) {
        try {
            if (!StringUtils.hasText(clientIp)) {
                return 0;
            }

            // 获取用户上次登录IP
            String lastIp = userLoginLogService.getLastLoginIp(address);

            if (lastIp == null || lastIp.isEmpty()) {
                return 5; // 没有历史IP记录，返回默认风险增量
            }

            if (!lastIp.equals(clientIp)) {
                log.info("检测到IP变化: address={}, lastIp={}, currentIp={}", address, lastIp, clientIp);
                return 10; // IP变化风险增量
            }

            return 0; // IP未变化，不增加风险
        } catch (Exception e) {
            log.warn("IP变化检测异常: address={}, clientIp={}", address, clientIp, e);
            return 5; // 检测异常时返回低风险增量
        }
    }

    /**
     * 检测地理位置异常
     */
    private int detectGeolocationAnomaly(String address, String clientIp) {
        try {
            if (!StringUtils.hasText(clientIp)) {
                return 0;
            }

            // 获取当前IP的地理位置
            String currentLocation = ClientIpUtils.getIpLocation(clientIp);

            // 获取用户上次登录IP的地理位置
            String lastLocation = userLoginLogService.getLastLoginLocation(address);

            if (lastLocation == null || lastLocation.isEmpty()) {
                return 5; // 没有历史地理位置记录，返回默认风险增量
            }

            if (!lastLocation.equals(currentLocation)) {
                // 地理位置发生变化
                log.info("检测到地理位置变化: address={}, lastLocation={}, currentLocation={}",
                        address, lastLocation, currentLocation);

                // 判断地理位置变化的距离级别
                int distanceLevel = calculateLocationDistanceLevel(lastLocation, currentLocation);
                if (distanceLevel > 2) { // 跨国家或洲际变化
                    return 20; // 地理位置剧烈变化风险增量
                } else if (distanceLevel > 1) { // 跨省/州变化
                    return 10; // 地理位置中等变化风险增量
                } else { // 同一省/州内变化
                    return 5; // 地理位置轻微变化风险增量
                }
            }

            return 0; // 地理位置未变化，不增加风险
        } catch (Exception e) {
            log.warn("地理位置异常检测异常: address={}, clientIp={}", address, clientIp, e);
            return 5; // 检测异常时返回低风险增量
        }
    }

    /**
     * 检测设备指纹变化
     */
    private int detectDeviceFingerprintChange(String address, String deviceFingerprint) {
        try {
            if (!StringUtils.hasText(deviceFingerprint)) {
                return 0;
            }

            // 获取用户上次登录的设备指纹
            String lastDeviceFingerprint = userLoginLogService.getLastDeviceFingerprint(address);

            if (lastDeviceFingerprint == null || lastDeviceFingerprint.isEmpty()) {
                return 5; // 没有历史设备指纹记录，返回默认风险增量
            }

            if (!lastDeviceFingerprint.equals(deviceFingerprint)) {
                // 设备指纹发生变化
                log.info("检测到设备指纹变化: address={}", address);
                return 15; // 设备变化风险增量
            }

            return 0; // 设备未变化，不增加风险
        } catch (Exception e) {
            log.warn("设备指纹变化检测异常: address={}", address, e);
            return 5; // 检测异常时返回低风险增量
        }
    }

    /**
     * 分析操作频率
     */
    private int analyzeOperationFrequency(String address, String operation) {
        try {
            // 实际应该查询最近时间内的操作次数
            log.debug("分析操作频率: address={}, operation={}", address, operation);

            // 例如：检查用户在过去1小时内的操作次数
            int operationCount = userOperationLogService.getRecentOperationCount(address, operation, 60);
            if (operationCount > 5) {
                return 10; // 高频操作风险增量
            }

            return 0; // 暂时返回0，待实现具体逻辑
        } catch (Exception e) {
            log.warn("操作频率分析异常: address={}, operation={}", address, operation, e);
            return 5; // 检测异常时返回低风险增量
        }
    }

    /**
     * 评估操作金额
     */
    private int evaluateOperationAmount(String address, String operation, String amount) {
        try {
            BigDecimal operationAmount = new BigDecimal(amount);

            // 根据操作类型设置不同的大额阈值
            BigDecimal threshold;
            switch (operation.toUpperCase()) {
                case "WITHDRAWAL":
                    threshold = new BigDecimal("10000"); // 提现大额阈值
                    break;
                case "EXCHANGE":
                    threshold = new BigDecimal("5000"); // 兑换大额阈值
                    break;
                default:
                    threshold = new BigDecimal("1000"); // 默认大额阈值
                    break;
            }

            if (operationAmount.compareTo(threshold) > 0) {
                // 计算超出阈值的倍数
                BigDecimal ratio = operationAmount.divide(threshold, 2, RoundingMode.HALF_UP);
                if (ratio.compareTo(new BigDecimal("10")) > 0) {
                    return 25; // 超出阈值10倍以上，高风险
                } else if (ratio.compareTo(new BigDecimal("5")) > 0) {
                    return 15; // 超出阈值5倍以上，中等风险
                } else {
                    return 8; // 超出阈值但在5倍以内，低风险
                }
            }

            return 0; // 金额正常，不增加风险
        } catch (Exception e) {
            log.warn("操作金额评估异常: address={}, operation={}, amount={}", address, operation, amount, e);
            return 5; // 评估异常时返回低风险增量
        }
    }

    /**
     * 计算两个地理位置之间的距离级别
     * 0: 同一地区, 1: 同省不同市, 2: 同国不同省, 3: 不同国家
     */
    private int calculateLocationDistanceLevel(String location1, String location2) {
        // 实际项目中应该根据地理坐标计算距离
        // 这里简化实现，仅根据位置字符串比较
        if (location1.equals(location2)) {
            return 0;
        }

        // 简单的字符串比较实现
        String[] parts1 = location1.split(",");
        String[] parts2 = location2.split(",");

        if (parts1.length > 0 && parts2.length > 0) {
            String country1 = parts1[0].trim();
            String country2 = parts2[0].trim();

            if (!country1.equals(country2)) {
                return 3; // 不同国家
            }

            if (parts1.length > 1 && parts2.length > 1) {
                String region1 = parts1[1].trim();
                String region2 = parts2[1].trim();

                if (!region1.equals(region2)) {
                    return 2; // 同国不同省
                }

                return 1; // 同省不同市
            }
        }

        return 3; // 无法判断时默认为不同国家
    }
}