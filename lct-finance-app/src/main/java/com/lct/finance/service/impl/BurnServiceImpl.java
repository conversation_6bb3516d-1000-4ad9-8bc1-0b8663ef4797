package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.context.UserContext;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.mapper.BurnMapper;
import com.lct.finance.model.constants.BurnConstants;
import com.lct.finance.model.constants.CommonConstants;
import com.lct.finance.model.dto.*;
import com.lct.finance.model.entity.*;
import com.lct.finance.model.enums.ErrorCode;
import com.lct.finance.service.*;
import com.lct.finance.service.handler.BurnOperationHandler;
import com.lct.finance.utils.RedisLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销毁服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class BurnServiceImpl extends ServiceImpl<BurnMapper, Burn> implements IBurnService {

    // 改为注入Service接口，而不是直接注入Mapper
    @Autowired
    private IMintBaseService mintBaseService;
    @Autowired
    private IMintProfitService mintProfitService;
    @Autowired
    private IMintTeamProfitService mintTeamProfitService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IWalletService walletService;
    @Autowired
    private IOptionService optionService;
    @Autowired
    private IExchangeStatisticsService exchangeStatisticsService;
    @Autowired
    private RedisLockUtil redisLockUtil;
    @Autowired
    private IOperationValidationService operationValidationService;

    @Autowired
    private BurnOperationHandler burnOperationHandler;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public BurnHistoryResponse getBurnHistory(String address, String date, Integer page, Integer pageSize) {
        // 获取当前登录用户
        User user = UserContext.getCurrentUser();

        // 🔧 如果提供了address参数，验证是否为当前用户（与PHP保持兼容）
        if (StringUtils.hasText(address) && !address.equalsIgnoreCase(user.getUserAddress())) {
            throw new BusinessException("只能查询自己的记录");
        }

        // 设置分页参数
        Page<Burn> pageParam = new Page<>(page, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                .eq(Burn::getUserId, user.getId())
                .orderByDesc(Burn::getId);

        // 处理日期过滤条件
        if (StringUtils.hasText(date)) {
            queryWrapper.apply("DATE(created_at) = {0}", date);
        }

        IPage<Burn> burnPage = this.page(pageParam, queryWrapper);

        // 转换为响应格式
        List<BurnHistoryResponse.BurnHistoryItem> items = burnPage.getRecords().stream()
                .map(this::convertToBurnHistoryItem)
                .collect(Collectors.toList());

        return BurnHistoryResponse.builder()
                .list(items)
                .build();
    }

    @Override
    public BurnProfitResponse getBurnProfits(String address, String date, Integer page, Integer pageSize) {
        // 获取当前登录用户
        User user = UserContext.getCurrentUser();

        // 🔧 如果提供了address参数，验证是否为当前用户（与PHP保持兼容）
        if (StringUtils.hasText(address) && !address.equalsIgnoreCase(user.getUserAddress())) {
            throw new BusinessException("只能查询自己的记录");
        }

        // 设置分页参数和查询条件
        LambdaQueryWrapper<MintProfit> queryWrapper = new LambdaQueryWrapper<MintProfit>()
                .eq(MintProfit::getUserId, user.getId());
        if (StringUtils.hasText(date)) {
            queryWrapper.apply("DATE(created_at) = {0}", date);
        }
        queryWrapper.orderByDesc(MintProfit::getCreatedAt);

        // 分页查询收益记录
        Page<MintProfit> pageParam = new Page<>(page, pageSize);
        IPage<MintProfit> profitPage = mintProfitService.page(pageParam, queryWrapper);

        // 转换为响应格式
        List<BurnProfitResponse.BurnProfitItem> items = profitPage.getRecords().stream()
                .map(this::convertToBurnProfitItem)
                .collect(Collectors.toList());

        return BurnProfitResponse.builder()
                .list(items)
                .build();
    }

    /**
     * 转换销毁记录为响应项
     */
    private BurnHistoryResponse.BurnHistoryItem convertToBurnHistoryItem(Burn burn) {
        return BurnHistoryResponse.BurnHistoryItem.builder()
                .amount(formatAmount(burn.getAmount()))
                .actualAmount(formatAmount(burn.getActualAmount()))
                .effectiveTime(burn.getEffectiveTime() != null ? burn.getEffectiveTime().toString() : "")
                .effectiveAt(burn.getEffectiveAt() != null ? burn.getEffectiveAt().format(DATE_TIME_FORMATTER) : "")
                .txhash(burn.getTxhash())
                .blockNumber(burn.getBlockNumber())
                .blockTime(burn.getBlockTime() != null ? burn.getBlockTime().toString() : "")
                .txStatus(burn.getTxStatus())
                .status(burn.getStatus())
                .createdAt(burn.getCreatedAt() != null ? burn.getCreatedAt().format(DATE_TIME_FORMATTER) : "")
                .build();
    }

    /**
     * 转换收益记录为响应项
     */
    private BurnProfitResponse.BurnProfitItem convertToBurnProfitItem(MintProfit profit) {
        return BurnProfitResponse.BurnProfitItem.builder()
                .amount(formatAmount(profit.getAmount()))
                .actualAmount(formatAmount(profit.getActualAmount()))
                .createdAt(profit.getCreatedAt() != null ? profit.getCreatedAt().format(DATE_TIME_FORMATTER) : "")
                .build();
    }

    @Override
    public TeamProfitResponse getTeamProfits(String address, String date, Integer page, Integer pageSize) {
        return getTeamProfitsOptimized(address, date, page, pageSize);
    }

    public TeamProfitResponse getTeamProfitsOptimized(String address, String date, Integer page, Integer pageSize) {
        // 获取当前登录用户
        User user = UserContext.getCurrentUser();

        // 🔧 如果提供了address参数，验证是否为当前用户（与PHP保持兼容）
        if (StringUtils.hasText(address) && !address.equalsIgnoreCase(user.getUserAddress())) {
            throw new BusinessException("只能查询自己的记录");
        }

        // 设置分页参数
        Page<MintTeamProfit> pageParam = new Page<>(page, pageSize);

        // 查询团队收益记录（不连表）
        IPage<MintTeamProfit> profitPage = mintTeamProfitService.selectTeamProfitHistoryOptimized(
                pageParam,
                user.getId(),
                StringUtils.hasText(date) ? date : null);

        // 处理结果
        List<TeamProfitResponse.TeamProfitItem> items = new ArrayList<>();
        if (profitPage.getRecords() != null && !profitPage.getRecords().isEmpty()) {
            // 批量查询用户地址
            List<Integer> sourceUserIds = profitPage.getRecords().stream()
                    .map(MintTeamProfit::getSourceUserId)
                    .filter(id -> id != null && id > 0)
                    .distinct()
                    .collect(Collectors.toList());

            Map<Long, String> userAddressMap = new HashMap<>();
            if (!sourceUserIds.isEmpty()) {
                // 批量查询用户地址
                List<Map<String, Object>> userMaps = mintTeamProfitService.selectUserAddressBatch(sourceUserIds);
                for (Map<String, Object> userMap : userMaps) {
                    Long userId = (Long) userMap.get("id");
                    String userAddress = (String) userMap.get("user_address");
                    if (userId != null && userAddress != null) {
                        userAddressMap.put(userId, userAddress);
                    }
                }
            }

            // 转换为响应格式
            final Map<Long, String> finalUserAddressMap = userAddressMap;
            items = profitPage.getRecords().stream()
                    .map(profit -> convertToTeamProfitItemOptimized(profit, finalUserAddressMap))
                    .collect(Collectors.toList());
        }

        return TeamProfitResponse.builder()
                .list(items)
                .build();
    }

    /**
     * 转换团队收益记录为响应项
     */
    private TeamProfitResponse.TeamProfitItem convertToTeamProfitItem(Map<String, Object> profit) {
        return TeamProfitResponse.TeamProfitItem.builder()
                .amount(formatAmount((BigDecimal) profit.get("amount")))
                .actualAmount(formatAmount((BigDecimal) profit.get("actualAmount")))
                .sourceUserAddress((String) profit.get("sourceUserAddress"))
                .createdAt(profit.get("createdAt") != null ? profit.get("createdAt").toString() : "")
                .build();
    }

    /**
     * 转换团队收益记录为响应项（优化版本）
     */
    private TeamProfitResponse.TeamProfitItem convertToTeamProfitItemOptimized(MintTeamProfit profit,
            Map<Long, String> userAddressMap) {
        String sourceUserAddress = "";
        if (profit.getSourceUserId() != null) {
            sourceUserAddress = userAddressMap.getOrDefault(profit.getSourceUserId(), "");
        }

        return TeamProfitResponse.TeamProfitItem.builder()
                .amount(formatAmount(profit.getAmount()))
                .actualAmount(formatAmount(profit.getActualAmount()))
                .sourceUserAddress(sourceUserAddress)
                .createdAt(profit.getCreatedAt() != null ? profit.getCreatedAt().format(DATE_TIME_FORMATTER) : "")
                .build();
    }

    /**
     * 格式化金额，保留小数点后有效位数
     * 🔧 修正：与PHP的processDecimals方法保持一致，保持6位小数格式
     */
    private String formatAmount(BigDecimal amount) {
        if (amount == null) {
            return "0.000000"; // 🔧 与PHP保持一致，返回6位小数的零值
        }

        // 🔧 保持6位小数，与PHP的processDecimals默认行为一致
        // PHP: number_format($amount, 6, '.', '')
        return String.format("%.6f", amount);
    }

    /**
     * 生成订单号
     * 格式：BR + 年月日时分秒 + 4位随机数
     * 例如：BR202401010101231234
     * 
     * @return 订单号
     */
    private String generateOrderNo() {
        // 获取当前时间，格式化为年月日时分秒
        String timestamp = java.time.LocalDateTime.now().format(
                java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

        // 生成4位随机数
        String randomNum = String.format("%04d", (int) (Math.random() * 10000));

        // 组合成订单号
        return "BR" + timestamp + randomNum;
    }

    @Override
    @Transactional
    public void createBurn(BurnCreateRequest request, Long userId, String clientIp) {
        log.info("创建销毁订单 - userId: {}, amount: {}, txhash: {}",
                userId, request.getAmount(), request.getTxhash());

        // 获取分布式锁
        String lockKey = redisLockUtil.getBurnLockKey(userId);
        String lockValue = redisLockUtil.tryLock(lockKey, 10);

        if (lockValue == null) {
            throw new BusinessException(ErrorCode.BURN_SYSTEM_BUSY);
        }

        try {
            // 🔧 1. 获取配置和验证参数（与PHP顺序一致）
            String minBurnStr = optionService.getMinBurnAmount();
            BigDecimal minBurnAmount = new BigDecimal(minBurnStr);
            if (request.getAmount().compareTo(minBurnAmount) < 0) {
                throw new BusinessException(ErrorCode.BURN_AMOUNT_TOO_SMALL);
            }

            // 🔧 2. 重复检查1 - Redis缓存
            if (redisLockUtil.isTxHashExists(request.getTxhash())) {
                throw new BusinessException(ErrorCode.BURN_TXHASH_DUPLICATE);
            }

            // 🔧 3. 验证交易哈希格式
            if (!validateTxHash(request.getTxhash())) {
                throw new BusinessException(ErrorCode.BURN_TXHASH_FORMAT_ERROR);
            }

            // 🔧 4. 重复检查2 - 数据库（与PHP顺序一致，并在发现重复时设置缓存）
            Long count = baseMapper.selectCount(
                    new LambdaQueryWrapper<Burn>()
                            .eq(Burn::getTxhash, request.getTxhash()));
            if (count > 0) {
                // 🔧 与PHP一致：在数据库检查发现重复时设置缓存
                redisLockUtil.setTxHashCache(request.getTxhash(), 3600); // 1小时
                throw new BusinessException(ErrorCode.BURN_TXHASH_DUPLICATE);
            }

            // 5. 获取用户信息
            User user = userService.getById(userId);
            if (user == null || user.getState() != 1) {
                throw new BusinessException(ErrorCode.BURN_USER_STATUS_ERROR);
            }

            // 6. 检查兑换逻辑
            ExchangeStatistics exchangeStat = null;
            BigDecimal needExchangeUAmount = BigDecimal.ZERO;

            if (user.getBurnNeedExchangeRate() != null &&
                    user.getBurnNeedExchangeRate().compareTo(BigDecimal.ZERO) > 0) {

                // 计算需要扣除的USDT数量
                needExchangeUAmount = calculateExchangeFee(request.getAmount(), user.getBurnNeedExchangeRate());

                // 获取用户兑换统计
                exchangeStat = exchangeStatisticsService.getUserExchangeStat(userId,
                        CommonConstants.ExchangeType.U2LCT);

                // 检查余额是否足够
                if (exchangeStat == null ||
                        exchangeStat.getBurnNeedExchangeRemaining().compareTo(needExchangeUAmount) < 0) {
                    throw new BusinessException(ErrorCode.BURN_EXCHANGE_BALANCE_INSUFFICIENT);
                }
            }

            // 7. 创建销毁记录
            // 生成订单号
            String orderNo = generateOrderNo();

            Burn burn = Burn.builder()
                    .userId(userId)
                    .type(1) // 🔧 修复：固定为1，与PHP保持一致
                    .orderNo(orderNo)
                    .amount(request.getAmount())
                    .actualAmount(request.getAmount())
                    // 🔧 修复：添加PHP中设置的字段
                    .usdtAmount(BigDecimal.ZERO) // PHP: $burn->usdt_amount = 0
                    .payAddress("") // PHP: $burn->pay_address = ''
                    .payAmount(BigDecimal.ZERO) // PHP: $burn->pay_amount = ''
                    .payCoin("") // PHP: $burn->pay_coin = ''
                    .swftOrderId("") // PHP: $burn->swft_order_id = ''
                    .txhash(request.getTxhash())
                    .status(BurnConstants.STATUS_WAITING_CONFIRM) // PHP: BurnConst::StatusProcessing = 2
                    .txStatus(BurnConstants.TX_STATUS_CONFIRMING) // PHP: BurnConst::TxStatusConfirming = 2
                    .ip(clientIp)
                    .inviterReward(0)
                    .version(2) // 🔧 修复：改为2，与PHP保持一致
                    .isOut(2)
                    .build();

            baseMapper.insert(burn);

            // 8. 在同一事务中更新兑换统计
            if (exchangeStat != null && needExchangeUAmount.compareTo(BigDecimal.ZERO) > 0) {
                boolean updated = exchangeStatisticsService.deductBurnExchangeRemaining(
                        exchangeStat.getId(), needExchangeUAmount);
                if (!updated) {
                    throw new BusinessException(ErrorCode.BURN_EXCHANGE_BALANCE_INSUFFICIENT);
                }
                log.info("扣除兑换余额成功 - userId: {}, statId: {}, amount: {}",
                        userId, exchangeStat.getId(), needExchangeUAmount);
            }

            // 9. 设置交易哈希缓存，防止重复提交（与PHP一致）
            redisLockUtil.setTxHashCache(request.getTxhash(), 86400); // 24小时

            log.info("销毁订单创建成功 - burnId: {}, userId: {}, amount: {}",
                    burn.getId(), userId, request.getAmount());

            // 10. 异步更新用户等级和临时族谱
            try {
                updateUserLevelAndTemporaryPids(userId);
            } catch (Exception e) {
                // 捕获异常但不影响主流程
                log.error("更新用户等级和临时族谱失败（非阻塞）: userId={}, error={}",
                        userId, e.getMessage(), e);
            }

        } catch (BusinessException e) {
            log.error("创建销毁订单失败 - userId: {}, error: {}", userId, e.getMessage());
            throw new BusinessException("创建销毁订单失败 - userId: " + userId + ", error: " + e.getMessage());
        } catch (Exception e) {
            log.error("创建销毁订单失败 - userId: {}, error: {}", userId, e.getMessage(), e);
            throw new BusinessException(ErrorCode.BURN_CREATE_FAILED);
        } finally {
            // 释放分布式锁
            redisLockUtil.releaseLock(lockKey, lockValue);
        }
    }

    @Override
    @Transactional
    public ApiResponse<String> burn(BurnCreateRequest request, String clientIp) {
        // 使用模板方法模式的操作处理器，大大简化代码
        return burnOperationHandler.handleOperation(request, clientIp);
    }

    @Override
    public BurnRequirementsResponse getBurnRequirements(Long userId) {
        log.info("获取销毁要求 - userId: {}", userId);

        try {
            // 获取用户信息
            User user = userService.getById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            // 从配置中获取最低销毁数量
            String minBurnStr = optionService.getMinBurnAmount();
            BigDecimal minBurnAmount = new BigDecimal(minBurnStr);

            // 🔧 修复：实际查询兑换余额
            BigDecimal exchangeRemaining = BigDecimal.ZERO;
            if (user.getBurnNeedExchangeRate() != null &&
                    user.getBurnNeedExchangeRate().compareTo(BigDecimal.ZERO) > 0) {
                exchangeRemaining = exchangeStatisticsService
                        .getBurnExchangeRemaining(userId, CommonConstants.ExchangeType.U2LCT);
            }

            return BurnRequirementsResponse.builder()
                    .burnNeedExchangeRate(user.getBurnNeedExchangeRate())
                    .burnNeedExchangeRemaining(exchangeRemaining) // 🔧 返回实际余额
                    .minBurnAmount(minBurnAmount)
                    .userStatus(user.getState())
                    .message("满足销毁要求")
                    .build();

        } catch (Exception e) {
            log.error("获取销毁要求失败 - userId: {}, error: {}", userId, e.getMessage(), e);

            // 返回默认值
            return BurnRequirementsResponse.builder()
                    .burnNeedExchangeRate(BigDecimal.ZERO)
                    .burnNeedExchangeRemaining(BigDecimal.ZERO)
                    .minBurnAmount(new BigDecimal("100"))
                    .userStatus(1)
                    .message("获取销毁要求失败")
                    .build();
        }
    }

    @Override
    public BurnPreValidateResponse preValidateBurn(BurnPreValidateRequest request, Long userId) {
        log.info("销毁预校验 - userId: {}, amount: {}", userId, request.getAmount());

        try {
            // 1. 获取用户信息
            User user = userService.getById(userId);
            if (user == null) {
                return BurnPreValidateResponse.error("用户不存在");
            }

            if (user.getState() != 1) {
                return BurnPreValidateResponse.error("用户状态异常，无法进行销毁操作");
            }

            // 2. 获取最小销毁数量配置
            String minBurnStr = optionService.getMinBurnAmount();
            BigDecimal minBurnAmount = new BigDecimal(minBurnStr);

            // 3. 基本数量校验
            if (request.getAmount().compareTo(minBurnAmount) < 0) {
                return BurnPreValidateResponse.error(
                        "销毁数量不能小于 " + minBurnAmount,
                        BigDecimal.ZERO,
                        BigDecimal.ZERO,
                        minBurnAmount,
                        user.getBurnNeedExchangeRate() != null ? user.getBurnNeedExchangeRate() : BigDecimal.ZERO);
            }

            // 4. 兑换余额校验（核心校验逻辑）
            BigDecimal needExchangeAmount = BigDecimal.ZERO;
            BigDecimal currentExchangeRemaining = BigDecimal.ZERO;

            if (user.getBurnNeedExchangeRate() != null &&
                    user.getBurnNeedExchangeRate().compareTo(BigDecimal.ZERO) > 0) {

                // 计算需要消耗的兑换余额
                needExchangeAmount = calculateExchangeFee(request.getAmount(), user.getBurnNeedExchangeRate());

                // 获取用户当前兑换余额（会自动创建记录如果不存在）
                ExchangeStatistics exchangeStat = exchangeStatisticsService
                        .getUserExchangeStat(userId, CommonConstants.ExchangeType.U2LCT);

                currentExchangeRemaining = exchangeStat.getBurnNeedExchangeRemaining();

                // 检查余额是否充足
                if (currentExchangeRemaining.compareTo(needExchangeAmount) < 0) {
                    return BurnPreValidateResponse.error(
                            String.format("兑换余额不足。需要: %s, 当前余额: %s, 缺少: %s",
                                    needExchangeAmount,
                                    currentExchangeRemaining,
                                    needExchangeAmount.subtract(currentExchangeRemaining)),
                            needExchangeAmount,
                            currentExchangeRemaining,
                            minBurnAmount,
                            user.getBurnNeedExchangeRate());
                }
            } else {
                // 不需要兑换余额的情况
                currentExchangeRemaining = exchangeStatisticsService
                        .getBurnExchangeRemaining(userId, CommonConstants.ExchangeType.U2LCT);
            }

            // 5. 所有校验通过
            log.info("销毁预校验通过 - userId: {}, amount: {}, needExchange: {}, currentRemaining: {}",
                    userId, request.getAmount(), needExchangeAmount, currentExchangeRemaining);

            return BurnPreValidateResponse.success(
                    needExchangeAmount,
                    currentExchangeRemaining,
                    minBurnAmount,
                    user.getBurnNeedExchangeRate() != null ? user.getBurnNeedExchangeRate() : BigDecimal.ZERO);

        } catch (Exception e) {
            log.error("销毁预校验失败 - userId: {}, error: {}", userId, e.getMessage(), e);
            return BurnPreValidateResponse.error("系统错误，请稍后重试");
        }
    }

    /**
     * 计算兑换手续费
     * 与Php版本的getFee方法保持一致
     * 
     * @param amount 销毁数量
     * @param rate   兑换比例（百分比）
     * @return 需要扣除的USDT数量
     */
    private BigDecimal calculateExchangeFee(BigDecimal amount, BigDecimal rate) {
        if (amount == null || rate == null ||
                amount.compareTo(BigDecimal.ZERO) <= 0 ||
                rate.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 计算公式：amount * rate / 100
        // 使用8位小数精度，向下取整（与Php保持一致）
        return amount.multiply(rate)
                .divide(BigDecimal.valueOf(100), 18, RoundingMode.DOWN);
    }

    @Override
    public boolean validateTxHash(String txhash) {
        if (txhash == null || txhash.trim().isEmpty()) {
            return false;
        }

        // 🔧 修复：实现与PHP一致的验证逻辑
        // PHP: strlen(Utils::stripZero($txhash)) != 64 || !Utils::isHex($txhash)
        String strippedHash = stripZero(txhash);
        return strippedHash.length() == 64 && isHex(txhash);
    }

    /**
     * 移除0x前缀（与PHP的Utils::stripZero功能一致）
     */
    private String stripZero(String value) {
        if (value == null) {
            return "";
        }
        // PHP的remove0x逻辑：if (str_starts_with($value, '0x')) return substr($value, 2);
        if (value.toLowerCase().startsWith("0x")) {
            return value.substring(2);
        }
        return value;
    }

    /**
     * 检查是否为十六进制（与PHP的isHex功能一致）
     */
    private boolean isHex(String value) {
        if (value == null || value.trim().isEmpty()) {
            return false;
        }
        // PHP: preg_match('/^(0x|0X)?[a-f0-9]*$/', $value) === 1
        String pattern = "^(0x|0X)?[a-fA-F0-9]*$";
        return value.matches(pattern);
    }

    @Override
    public boolean checkDuplicate(String txhash) {
        if (txhash == null || txhash.trim().isEmpty()) {
            return false;
        }

        // 检查Redis缓存
        if (redisLockUtil.isTxHashExists(txhash)) {
            return true;
        }

        // 检查数据库
        Long count = baseMapper.selectCount(
                new LambdaQueryWrapper<Burn>()
                        .eq(Burn::getTxhash, txhash));

        return count > 0;
    }

    @Override
    @Cacheable(value = "userBurnTotalAmount", key = "#userId", unless = "#result == null")
    public BigDecimal getTotalAmount(Long userId) {
        log.debug("计算用户总销毁量 - userId: {}", userId);

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .eq(Burn::getUserId, userId)
                    .eq(Burn::getTxStatus, 1);

            // 使用selectList获取所有符合条件的记录，然后计算总和
            return this.list(queryWrapper)
                    .stream()
                    .map(Burn::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.error("计算用户总销毁量失败 - userId: {}", userId, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getTotalAmountWithTimeLimit(Long userId, String endTime) {
        log.debug("计算用户总销毁量（支持时间限制） - userId: {}, endTime: {}", userId, endTime);

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .eq(Burn::getUserId, userId)
                    .eq(Burn::getTxStatus, 1);

            // 如果有时间限制，则添加时间条件
            // 对应PHP代码：->when(!empty($end_time), function ($query) use ($end_time) {
            // return $query->where('created_at', '<=', $end_time);
            // })
            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    LocalDateTime endDateTime = LocalDateTime.parse(endTime.trim(),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    queryWrapper.le(Burn::getCreatedAt, endDateTime);
                    log.debug("应用时间限制: userId={}, endTime={}", userId, endTime);
                } catch (Exception e) {
                    log.warn("时间格式解析失败，忽略时间限制: userId={}, endTime={}, error={}",
                            userId, endTime, e.getMessage());
                }
            }

            // 使用selectList获取所有符合条件的记录，然后计算总和
            return this.list(queryWrapper)
                    .stream()
                    .map(Burn::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.error("计算用户总销毁量失败（支持时间限制） - userId: {}, endTime: {}", userId, endTime, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getTotalAmountByUserIds(List<Long> userIds, String startDate, String endDate) {
        log.debug("计算多个用户的总销毁量 - userIds: {}, startDate: {}, endDate: {}",
                userIds, startDate, endDate);

        if (userIds == null || userIds.isEmpty()) {
            return BigDecimal.ZERO;
        }

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .in(Burn::getUserId, userIds)
                    .eq(Burn::getTxStatus, 1);

            // 处理开始日期
            if (StringUtils.hasText(startDate)) {
                LocalDateTime startTime = LocalDateTime.parse(startDate + " 00:00:00",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                queryWrapper.ge(Burn::getCreatedAt, startTime);
            }

            // 处理结束日期
            if (StringUtils.hasText(endDate)) {
                LocalDateTime endTime = LocalDateTime.parse(endDate + " 23:59:59",
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                queryWrapper.le(Burn::getCreatedAt, endTime);
            }

            // 使用selectList获取所有符合条件的记录，然后计算总和
            return this.list(queryWrapper)
                    .stream()
                    .map(Burn::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.error("计算多个用户的总销毁量失败 - userIds: {}", userIds, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getTodayAmountByUserIds(List<Long> userIds, String date) {
        log.debug("计算多个用户指定日期的销毁量 - userIds: {}, date: {}", userIds, date);

        if (userIds == null || userIds.isEmpty() || !StringUtils.hasText(date)) {
            return BigDecimal.ZERO;
        }

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .in(Burn::getUserId, userIds)
                    .eq(Burn::getTxStatus, 1);

            // 根据日期格式添加不同的条件
            if (date.contains("-")) {
                // 标准格式 yyyy-MM-dd
                queryWrapper.apply("DATE(created_at) = {0}", date);
            } else if (date.length() == 8) {
                // 紧凑格式 yyyyMMdd
                queryWrapper.apply("DATE_FORMAT(created_at, '%Y%m%d') = {0}", date);
            } else {
                log.warn("日期格式不正确: {}", date);
                return BigDecimal.ZERO;
            }

            // 使用selectList获取所有符合条件的记录，然后计算总和
            return this.list(queryWrapper)
                    .stream()
                    .map(Burn::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.error("计算多个用户指定日期的销毁量失败 - userIds: {}, date: {}", userIds, date, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取用户在指定时间范围内的总销毁数量
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 总销毁数量
     */
    public BigDecimal getTotalAmountByTimeRange(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("计算用户时间范围内的总销毁量 - userId: {}, startTime: {}, endTime: {}",
                userId, startTime, endTime);

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .eq(Burn::getUserId, userId)
                    .eq(Burn::getTxStatus, 1);

            // 添加时间范围条件
            if (startTime != null) {
                queryWrapper.ge(Burn::getCreatedAt, startTime);
            }
            if (endTime != null) {
                queryWrapper.le(Burn::getCreatedAt, endTime);
            }

            // 使用selectList获取所有符合条件的记录，然后计算总和
            return this.list(queryWrapper)
                    .stream()
                    .map(Burn::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.error("计算用户时间范围内的总销毁量失败 - userId: {}", userId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取用户未生效的销毁数量
     * 对应PHP代码：Burn::getUneffectiveAmount方法
     * 
     * @param userId 用户ID
     * @return 未生效销毁数量
     */
    public BigDecimal getUneffectiveAmount(Long userId) {
        log.debug("计算用户未生效的销毁量 - userId: {}", userId);

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .eq(Burn::getUserId, userId)
                    .eq(Burn::getTxStatus, 1)
                    .eq(Burn::getStatus, 3); // status=3表示未生效

            // 使用selectList获取所有符合条件的记录，然后计算总和
            return this.list(queryWrapper)
                    .stream()
                    .map(Burn::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.error("计算用户未生效的销毁量失败 - userId: {}", userId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取用户今日销毁数量
     * 
     * @param userId 用户ID
     * @param date   日期（YYYY-MM-DD格式）
     * @return 今日销毁数量
     */
    public BigDecimal getTodayAmount(Long userId, String date) {
        log.debug("计算用户指定日期的销毁量 - userId: {}, date: {}", userId, date);

        if (!StringUtils.hasText(date)) {
            log.warn("日期参数为空，无法查询");
            return BigDecimal.ZERO;
        }

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .eq(Burn::getUserId, userId)
                    .eq(Burn::getTxStatus, 1);

            // 根据日期格式添加不同的条件
            if (date.contains("-")) {
                // 标准格式 yyyy-MM-dd
                queryWrapper.apply("DATE(created_at) = {0}", date);
            } else if (date.length() == 8) {
                // 紧凑格式 yyyyMMdd
                queryWrapper.apply("DATE_FORMAT(created_at, '%Y%m%d') = {0}", date);
            } else {
                log.warn("日期格式不正确: {}", date);
                return BigDecimal.ZERO;
            }

            // 使用selectList获取所有符合条件的记录，然后计算总和
            return this.list(queryWrapper)
                    .stream()
                    .map(Burn::getActualAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

        } catch (Exception e) {
            log.error("计算用户指定日期的销毁量失败 - userId: {}, date: {}", userId, date, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Object[] calculateOutStatus(Long userId) {
        if (userId == null) {
            return new Object[] { 0, BigDecimal.ZERO };
        }

        try {
            // 获取用户已获得总收益
            BigDecimal totalProfit = getUserTotalProfit(userId);

            // 获取用户预估总收益
            BigDecimal estimateTotalProfit = getEstimateTotalProfit(userId);

            // 计算剩余收益出局数量
            BigDecimal remainingProfitOut = estimateTotalProfit.subtract(totalProfit);

            // 判断是否出局
            int isOut = 0;
            if (remainingProfitOut.compareTo(BigDecimal.ZERO) <= 0 && totalProfit.compareTo(BigDecimal.ZERO) > 0) {
                remainingProfitOut = BigDecimal.ZERO;
                isOut = 1;
            }

            return new Object[] { isOut, remainingProfitOut };
        } catch (Exception e) {
            log.error("计算用户出局状态失败: userId={}, error={}", userId, e.getMessage(), e);
            return new Object[] { 0, BigDecimal.ZERO };
        }
    }

    @Override
    public BigDecimal getEstimateTotalProfit(Long userId) {
        if (userId == null) {
            return BigDecimal.ZERO;
        }

        try {
            // 从MintBase表查询用户的预估总收益（profit_max_amount总和）
            BigDecimal estimateTotal = mintBaseService.getEstimateTotalProfit(userId);
            return estimateTotal != null ? estimateTotal : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取用户预估总收益失败: userId={}, error={}", userId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getUserTotalProfit(Long userId) {
        if (userId == null) {
            return BigDecimal.ZERO;
        }

        try {
            // 从钱包表获取用户已获得的总收益：profit + team_profit + direct_invite
            Wallet wallet = walletService.getOne(
                    new LambdaQueryWrapper<Wallet>()
                            .eq(Wallet::getUserId, userId));

            if (wallet == null) {
                return BigDecimal.ZERO;
            }

            BigDecimal totalProfit = BigDecimal.ZERO;
            if (wallet.getProfit() != null) {
                totalProfit = totalProfit.add(wallet.getProfit());
            }
            if (wallet.getTeamProfit() != null) {
                totalProfit = totalProfit.add(wallet.getTeamProfit());
            }
            // 注意：根据旧PHP代码，出局计算不包含direct_invite，只计算profit + team_profit

            return totalProfit;
        } catch (Exception e) {
            log.error("获取用户已获得总收益失败: userId={}, error={}", userId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Map<Long, BigDecimal> getBatchTotalAmountMap(List<Long> userIds) {
        log.debug("批量获取多个用户的销毁量映射 - userIds: {}", userIds);

        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .in(Burn::getUserId, userIds)
                    .eq(Burn::getTxStatus, 1);

            // 查询符合条件的所有记录
            List<Burn> burnList = this.list(queryWrapper);

            // 按userId分组，并计算每个用户的总销毁量
            return burnList.stream()
                    .collect(Collectors.groupingBy(
                            Burn::getUserId,
                            Collectors.mapping(
                                    Burn::getActualAmount,
                                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        } catch (Exception e) {
            log.error("批量获取多个用户的销毁量映射失败 - userIds: {}", userIds, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<Long, BigDecimal> getBatchTotalAmountMapWithTimeLimit(List<Long> userIds, String endTime) {
        log.debug("批量获取多个用户的销毁量映射（支持时间限制） - userIds: {}, endTime: {}", userIds, endTime);

        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // 使用MyBatis-Plus的LambdaQueryWrapper
            LambdaQueryWrapper<Burn> queryWrapper = new LambdaQueryWrapper<Burn>()
                    .in(Burn::getUserId, userIds)
                    .eq(Burn::getTxStatus, 1);

            // 如果有时间限制，则添加时间条件
            // 对应PHP代码：->when(!empty($end_time), function ($query) use ($end_time) {
            // return $query->where('created_at', '<=', $end_time);
            // })
            if (endTime != null && !endTime.trim().isEmpty()) {
                try {
                    LocalDateTime endDateTime = LocalDateTime.parse(endTime.trim(),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    queryWrapper.le(Burn::getCreatedAt, endDateTime);
                    log.debug("批量查询应用时间限制: userIds={}, endTime={}", userIds, endTime);
                } catch (Exception e) {
                    log.warn("批量查询时间格式解析失败，忽略时间限制: userIds={}, endTime={}, error={}",
                            userIds, endTime, e.getMessage());
                }
            }

            // 查询符合条件的所有记录
            List<Burn> burnList = this.list(queryWrapper);

            // 按userId分组，并计算每个用户的总销毁量
            return burnList.stream()
                    .collect(Collectors.groupingBy(
                            Burn::getUserId,
                            Collectors.mapping(
                                    Burn::getActualAmount,
                                    Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));

        } catch (Exception e) {
            log.error("批量获取多个用户的销毁量映射失败（支持时间限制） - userIds: {}, endTime: {}", userIds, endTime, e);
            return Collections.emptyMap();
        }
    }

    @Override
    @Transactional
    public boolean updateUserLevelAndTemporaryPids(Long userId) {
        log.info("开始更新用户等级和临时族谱: userId={}", userId);

        if (userId == null) {
            log.warn("无效的用户ID");
            return false;
        }

        // 获取分布式锁
        String lockKey = "update_user_level_lock:" + userId;
        String lockValue = redisLockUtil.tryLock(lockKey, 30);
        if (lockValue == null) {
            log.warn("获取锁失败，其他进程可能正在更新用户等级: userId={}", userId);
            return false;
        }

        try {
            // 1. 获取用户信息
            User user = userService.getById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return false;
            }

            // 2. 获取所有用户的钱包数据，按销毁量降序排序
            // 直接调用Mapper中的方法，不再使用Service中的方法
            List<Map<String, Object>> wallets = baseMapper.getUserWalletsByBurn();
            if (wallets == null || wallets.isEmpty()) {
                log.warn("没有获取到任何钱包数据");
                return false;
            }

            // 3. 找到当前用户在排序中的位置
            int userIndex = -1;
            long lastUserId = 0; // 最后一个用户ID
            for (int i = 0; i < wallets.size(); i++) {
                Map<String, Object> wallet = wallets.get(i);
                Long walletUserId = ((Number) wallet.get("user_id")).longValue();

                if (i == wallets.size() - 1) {
                    lastUserId = walletUserId;
                }

                if (walletUserId.equals(userId)) {
                    userIndex = i;
                    break;
                }
            }

            if (userIndex == -1) {
                log.warn("在钱包列表中未找到用户: userId={}", userId);
                return false;
            }

            // 如果用户已经是最后一名，没有可超越的人
            if (lastUserId == userId) {
                log.info("用户已经是最后一名，没有可超越的用户: userId={}", userId);
                return true;
            }

            // 4. 找到用户可以超越的上级
            Long exceedsPid = 0L;
            if (userIndex < wallets.size() - 1) {
                exceedsPid = ((Number) wallets.get(userIndex + 1).get("user_id")).longValue();
            }

            if (exceedsPid <= 0) {
                log.info("没有找到用户可以超越的上级: userId={}", userId);
                return true;
            }

            // 5. 构建新的族谱排序
            List<Long> newPids = new ArrayList<>();
            for (Map<String, Object> wallet : wallets) {
                Long walletUserId = ((Number) wallet.get("user_id")).longValue();
                newPids.add(walletUserId);

                // 构建临时族谱字符串
                StringBuilder resultString = new StringBuilder();
                for (int i = 0; i < newPids.size(); i++) {
                    if (i > 0) {
                        resultString.append(",");
                    }
                    resultString.append("-").append(newPids.get(i)).append("-");
                }

                // 如果当前是用户本人，检查是否需要更新
                if (walletUserId.equals(userId)) {
                    String newTemporaryPids = resultString.toString();

                    if (newTemporaryPids.equals(user.getTemporaryPids())) {
                        log.info("用户族谱无需更新: userId={}, temporaryPids={}", userId, newTemporaryPids);
                        return true;
                    }

                    // 6. 查找所有需要更新的下级用户
                    List<User> othersToUpdate = userService.list(new LambdaQueryWrapper<User>()
                            .like(User::getTemporaryPids, "%-" + exceedsPid + "-%")
                            .select(User::getId, User::getTemporaryPids));

                    // 7. 更新族谱
                    for (User u : othersToUpdate) {
                        // 跳过用户自己
                        if (u.getId().equals(userId)) {
                            user.setTemporaryPids(newTemporaryPids);
                            userService.updateById(user);
                            continue;
                        }

                        // 更新下级用户的族谱
                        String tempPids = u.getTemporaryPids();

                        // 先移除当前用户
                        tempPids = tempPids.replace(",-" + userId + "-", "");

                        // 吞并上级
                        String[] parts = tempPids.split("-" + exceedsPid + "-", 2);
                        if (parts.length > 1) {
                            tempPids = newTemporaryPids + "," + "-" + exceedsPid + "-" + parts[1];

                            u.setTemporaryPids(tempPids);
                            userService.updateById(u);
                            log.debug("更新下级用户族谱: userId={}, newTemporaryPids={}", u.getId(), tempPids);
                        }
                    }

                    log.info("成功更新用户族谱: userId={}, oldTemporaryPids={}, newTemporaryPids={}",
                            userId, user.getTemporaryPids(), newTemporaryPids);

                    // 8. 更新用户等级
                    updateUserLevels(wallets);

                    return true;
                }
            }

            return true;

        } catch (Exception e) {
            log.error("更新用户等级和临时族谱失败: userId={}, error={}", userId, e.getMessage(), e);
            return false;
        } finally {
            // 释放分布式锁
            redisLockUtil.releaseLock(lockKey, lockValue);
        }
    }

    /**
     * 根据销毁量分配用户等级
     * 
     * @param wallets 按销毁量排序的钱包列表
     */
    private void updateUserLevels(List<Map<String, Object>> wallets) {
        try {
            // 根据配置获取等级分配参数
            BigDecimal limit = new BigDecimal("500"); // 差值阈值
            int maxLevel = 10; // 最大等级
            int nextLevel = 10; // 下一轮分配等级

            List<User> usersToUpdate = new ArrayList<>();

            for (int i = 0; i < wallets.size(); i++) {
                Map<String, Object> wallet = wallets.get(i);
                Long userId = ((Number) wallet.get("user_id")).longValue();
                BigDecimal burnAmount = (BigDecimal) wallet.get("burn");

                // 没有下一个用户，不分配等级
                if (i + 1 >= wallets.size()) {
                    continue;
                }

                BigDecimal nextBurnAmount = (BigDecimal) wallets.get(i + 1).get("burn");

                // 计算与下一用户的差值
                BigDecimal diff = burnAmount.subtract(nextBurnAmount);

                // 如果当前销毁量小于阈值或差值小于阈值，保持原等级
                if (burnAmount.compareTo(limit) < 0 || diff.compareTo(limit) < 0) {
                    continue;
                }

                // 获取用户并更新等级
                User user = userService.getById(userId);
                if (user != null) {
                    user.setLevel(nextLevel);
                    usersToUpdate.add(user);

                    log.debug("分配用户等级: userId={}, level={}, burnAmount={}", userId, nextLevel, burnAmount);
                }
            }

            // 批量更新用户等级
            if (!usersToUpdate.isEmpty()) {
                userService.updateBatchById(usersToUpdate);
                log.info("批量更新用户等级完成，更新了{}个用户", usersToUpdate.size());
            }

        } catch (Exception e) {
            log.error("分配用户等级失败: error={}", e.getMessage(), e);
        }
    }
}