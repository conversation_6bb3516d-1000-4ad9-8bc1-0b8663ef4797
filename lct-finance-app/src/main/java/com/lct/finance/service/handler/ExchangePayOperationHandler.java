package com.lct.finance.service.handler;

import com.lct.finance.context.UserContext;
import com.lct.finance.model.dto.ExchangePayRequest;
import com.lct.finance.model.request.OperationValidationRequest;
import com.lct.finance.model.response.OperationValidationResult;
import com.lct.finance.service.IExchangeService;
import com.lct.finance.service.IRiskAssessmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 兑换支付操作处理器
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class ExchangePayOperationHandler extends AbstractOperationHandler<ExchangePayRequest, Boolean> {

        @Autowired
        private IExchangeService exchangeService;

        @Autowired
        private IRiskAssessmentService riskAssessmentService;

        @Override
        protected OperationValidationRequest buildValidationRequest(ExchangePayRequest request, String clientIp) {
                return OperationValidationRequest.builder()
                                .address(UserContext.getCurrentUser().getUserAddress())
                                .operation("EXCHANGE_PAY")
//                                .message(request.getMessage())
//                                .signature(request.getSign())
//                                .nonce(request.getNonce())
                                .clientIp(clientIp)
                                .deviceFingerprint(request.getDeviceFingerprint())
                                .performRiskAssessment(true)
                                .recordOperationLog(false) // 支付结果日志单独处理
                                .build();
        }

        @Override
        protected Boolean executeBusinessLogic(ExchangePayRequest request, String clientIp,
                        OperationValidationResult validationResult) {

                long startTime = System.currentTimeMillis();
                String userAddress = UserContext.getCurrentUser().getUserAddress();

                log.info("执行兑换支付业务逻辑: 用户={}, 订单号={}, 交易哈希={}",
                                UserContext.getCurrentUserInfo(), request.getOrderId(), request.getTxhash());

                // 调用兑换服务处理支付确认
                boolean result = exchangeService.payExchange(request, userAddress);

                long duration = System.currentTimeMillis() - startTime;

                // 根据结果记录不同的日志
                if (result) {
                        log.info("兑换支付确认成功 - 用户: {}, 订单号: {}, 耗时: {}ms",
                                        userAddress, request.getOrderId(), duration);

                        riskAssessmentService.recordOperationLog(
                                        userAddress,
                                        "EXCHANGE_PAY",
                                        "", // 金额未知
                                        clientIp,
                                        validationResult.getRiskLevel(),
                                        1 // 成功状态
                        );
                } else {
                        log.warn("兑换支付确认失败 - 用户: {}, 订单号: {}, 耗时: {}ms",
                                        userAddress, request.getOrderId(), duration);

                        riskAssessmentService.recordOperationLog(
                                        userAddress,
                                        "EXCHANGE_PAY",
                                        "", // 金额未知
                                        clientIp,
                                        validationResult.getRiskLevel(),
                                        0 // 失败状态
                        );
                }

                return result;
        }

        @Override
        protected String getOperationName() {
                return "兑换支付";
        }

        @Override
        protected void onHighRiskDetected(OperationValidationRequest request, OperationValidationResult result) {
                // 兑换支付的高风险处理
                log.warn("高风险兑换支付操作被检测到: address={}, riskLevel={}",
                                request.getAddress(), result.getRiskLevel());

                // TODO: 可以在这里实现额外的安全措施
                // 1. 要求交易所确认
                // 2. 延迟处理订单
                // 3. 人工审核
        }

}