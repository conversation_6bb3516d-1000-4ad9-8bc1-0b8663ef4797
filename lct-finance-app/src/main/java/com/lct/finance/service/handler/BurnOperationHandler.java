package com.lct.finance.service.handler;

import com.lct.finance.context.UserContext;
import com.lct.finance.model.dto.BurnCreateRequest;
import com.lct.finance.model.request.OperationValidationRequest;
import com.lct.finance.model.response.OperationValidationResult;
import com.lct.finance.service.IBurnService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 销毁操作处理器
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class BurnOperationHandler extends AbstractOperationHandler<BurnCreateRequest, String> {

    @Autowired
    private IBurnService burnService;

    @Override
    protected OperationValidationRequest buildValidationRequest(BurnCreateRequest request, String clientIp) {
        return OperationValidationRequest.builder()
                .address(request.getAddress())
                .operation("BURN")
//                .message(request.getMessage())
//                .signature(request.getSign())
//                .nonce(request.getNonce())
                .clientIp(clientIp)
                .deviceFingerprint(request.getDeviceFingerprint())
                .amount(request.getAmount().toString())
                .performRiskAssessment(true)
                .recordOperationLog(true)
                .build();
    }

    @Override
    protected String executeBusinessLogic(BurnCreateRequest request, String clientIp,
            OperationValidationResult validationResult) {

        log.info("执行销毁业务逻辑: {}, amount={}, txhash={}",
                UserContext.getCurrentUserInfo(), request.getAmount(), request.getTxhash());

        // 调用销毁服务处理销毁，不需要重复验证
        burnService.createBurn(request, UserContext.getCurrentUser().getId(), clientIp);

        return "销毁订单创建成功";
    }

    @Override
    protected String getOperationName() {
        return "销毁";
    }

    @Override
    protected void onHighRiskDetected(OperationValidationRequest request, OperationValidationResult result) {
        // 销毁的高风险处理
        log.warn("高风险销毁操作被检测到: address={}, riskLevel={}",
                request.getAddress(), result.getRiskLevel());

        // TODO: 可以在这里实现额外的安全措施
        // 1. 要求额外的确认步骤
        // 2. 限制单日销毁数量
        // 3. 发送警报给风控团队
    }
}