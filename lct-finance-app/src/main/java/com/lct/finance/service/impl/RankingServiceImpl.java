package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.lct.finance.model.dto.ProfitRankingResponse;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.entity.Wallet;
import com.lct.finance.service.IRankingService;
import com.lct.finance.service.IUserService;
import com.lct.finance.service.IWalletService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 排行榜服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service

public class RankingServiceImpl implements IRankingService {

    @Autowired
    private IWalletService walletService;
    @Autowired
    private IUserService userService;

    @Override
    @Cacheable(value = "personalProfitRanking", key = "#limit", unless = "#result.isEmpty()")
    public List<ProfitRankingResponse> getPersonalProfitRanking(Integer limit) {
        log.info("获取个人收益排行榜: limit={}", limit);

        int rankingLimit = limit != null && limit > 0 ? Math.min(limit, 50) : 10; // 限制最大50条

        // 查询收益排行榜 - 使用LambdaQueryWrapper
        List<Wallet> wallets = walletService.list(new LambdaQueryWrapper<Wallet>()
                .orderByDesc(Wallet::getProfit)
                .last("LIMIT " + rankingLimit));

        List<ProfitRankingResponse> rankings = new ArrayList<>();

        for (int i = 0; i < wallets.size(); i++) {
            Wallet wallet = wallets.get(i);

            // 查询用户信息
            User user = userService.getById(wallet.getUserId());
            if (user == null) {
                log.warn("用户不存在: userId={}", wallet.getUserId());
                continue;
            }

            // 构建排行榜项
            ProfitRankingResponse ranking = ProfitRankingResponse.builder()
                    .rank(i + 1)
                    .userAddress(maskAddress(user.getUserAddress()))
                    .profit(formatProfit(wallet.getProfit()))
                    .build();

            rankings.add(ranking);
        }

        log.info("个人收益排行榜查询完成: 返回{}条记录", rankings.size());
        return rankings;
    }

    /**
     * 地址脱敏处理：显示前6位...后6位
     */
    private String maskAddress(String address) {
        if (address == null || address.length() <= 12) {
            return address;
        }

        return address.substring(0, 6) + "..." + address.substring(address.length() - 6);
    }

    /**
     * 格式化收益金额：保留2位小数
     */
    private String formatProfit(BigDecimal profit) {
        if (profit == null) {
            return "0.00";
        }

        return profit.setScale(2, RoundingMode.DOWN).toString();
    }
}