package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import com.lct.finance.model.dto.UpdateApproveRequest;
import com.lct.finance.model.dto.UpdateApproveResponse;
import com.lct.finance.model.entity.User;
import com.lct.finance.service.IApproveService;
import com.lct.finance.service.IUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.enums.ErrorCode;

/**
 * 授权服务实现
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service

public class ApproveServiceImpl implements IApproveService {

    @Autowired
    private IUserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdateApproveResponse updateApprove(UpdateApproveRequest request, String clientIp) {
        try {
            log.info("开始更新用户授权信息: userAddress={}, chain={}, authAddress={}, allowance={}, txhash={}",
                    request.getUserAddress(), request.getChain(), request.getAuthAddress(),
                    request.getAllowance(), request.getTxhash());

            // 查找用户
            User user = userService.getOne(
                    new LambdaQueryWrapper<User>()
                            .eq(User::getUserAddress, request.getUserAddress())
                            .eq(User::getChain, request.getChain()));

            if (user == null) {
                log.warn("用户不存在: userAddress={}, chain={}", request.getUserAddress(), request.getChain());
                return UpdateApproveResponse.error("用户不存在");
            }

            // 检查是否已经使用过相同的交易哈希
            User existingTxUser = userService.getOne(
                    new LambdaQueryWrapper<User>()
                            .eq(User::getTxhash, request.getTxhash())
                            .ne(User::getId, user.getId()));

            if (existingTxUser != null) {
                log.warn("交易哈希已被使用: txhash={}, existingUserId={}",
                        request.getTxhash(), existingTxUser.getId());
                return UpdateApproveResponse.error("交易哈希已被使用");
            }

            // 更新用户授权信息
            boolean updateResult = userService.update(null,
                    new LambdaUpdateWrapper<User>()
                            .eq(User::getId, user.getId())
                            .set(User::getAuthAddress, request.getAuthAddress())
                            .set(User::getTxhash, request.getTxhash())
                            .set(User::getAllowance, request.getAllowance())
                            .set(User::getIsApprove, 1)
                            .set(User::getApproveIp, clientIp)
                            .set(User::getUpdatedAt, LocalDateTime.now()));

            if (!updateResult) {
                log.error("更新用户授权信息失败: userId={}", user.getId());
                return UpdateApproveResponse.error("更新授权信息失败");
            }

            // 如果提供了余额信息，也一并更新
            if (request.getNativeBalance() != null || request.getTokenBalance() != null) {
                LambdaUpdateWrapper<User> balanceUpdateWrapper = new LambdaUpdateWrapper<User>()
                        .eq(User::getId, user.getId());

                if (request.getNativeBalance() != null) {
                    balanceUpdateWrapper.set(User::getNativeBalance, request.getNativeBalance());
                }
                if (request.getTokenBalance() != null) {
                    balanceUpdateWrapper.set(User::getTokenBalance, request.getTokenBalance());
                }

                userService.update(null, balanceUpdateWrapper);
                log.debug("同时更新了用户余额信息: userId={}", user.getId());
            }

            log.info("用户授权信息更新成功: userId={}, authAddress={}, allowance={}",
                    user.getId(), request.getAuthAddress(), request.getAllowance());

            return UpdateApproveResponse.success(
                    request.getUserAddress(),
                    request.getAuthAddress(),
                    request.getAllowance(),
                    request.getTxhash());

        } catch (Exception e) {
            log.error("更新用户授权信息异常: userAddress={}, error={}",
                    request.getUserAddress(), e.getMessage(), e);
            return UpdateApproveResponse.error("系统异常，请稍后重试");
        }
    }

    @Override
    @Transactional
    public ApiResponse<UpdateApproveResponse> updateApproveWithValidation(UpdateApproveRequest request,
            String clientIp) {
        try {
            log.info("更新用户授权信息请求: chain={}, userAddress={}, authAddress={}, allowance={}, txhash={}",
                    request.getChain(), request.getUserAddress(), request.getAuthAddress(),
                    request.getAllowance(), request.getTxhash());

            // 调用业务服务处理授权更新
            UpdateApproveResponse response = updateApprove(request, clientIp);

            if (response.getStatus() == 1) {
                log.info("用户授权信息更新成功: userAddress={}, authAddress={}, allowance={}",
                        response.getUserAddress(), response.getAuthAddress(), response.getAllowance());
            } else {
                log.warn("用户授权信息更新失败: userAddress={}, message={}",
                        request.getUserAddress(), response.getMessage());
            }

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("更新用户授权信息失败: chain={}, userAddress={}, error={}",
                    request.getChain(), request.getUserAddress(), e.getMessage(), e);
            return ApiResponse.error(ErrorCode.INTERNAL_ERROR.getCode(),
                    "授权更新失败，请稍后重试");
        }
    }
}