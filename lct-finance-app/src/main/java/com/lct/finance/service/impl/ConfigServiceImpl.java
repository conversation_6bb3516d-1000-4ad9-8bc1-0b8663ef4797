package com.lct.finance.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lct.finance.model.constants.WithdrawalConstants;
import com.lct.finance.service.IConfigService;
import com.lct.finance.service.IOptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置管理服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Slf4j
@Service
public class ConfigServiceImpl implements IConfigService{

    @Autowired
    private IOptionService optionService;
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public IConfigService.WithdrawalConfig getWithdrawalConfig() {
        try {
            String configJson = optionService.getOption(WithdrawalConstants.ConfigKey.WITHDRAWAL_CONFIG);
            if (!StringUtils.hasText(configJson)) {
                log.warn("提现配置为空，使用默认配置");
                return getDefaultWithdrawalConfig();
            }

            Map<String, Object> configMap = objectMapper.readValue(configJson, new TypeReference<Map<String, Object>>() {});
            
            WithdrawalConfig config = new WithdrawalConfig();
            
            // 解析时间配置
            String timeStart = (String) configMap.get("time_start");
            String timeEnd = (String) configMap.get("time_end");
            if (StringUtils.hasText(timeStart)) {
                config.setTimeStart(LocalTime.parse(timeStart, DateTimeFormatter.ofPattern("HH:mm:ss")));
            }
            if (StringUtils.hasText(timeEnd)) {
                config.setTimeEnd(LocalTime.parse(timeEnd, DateTimeFormatter.ofPattern("HH:mm:ss")));
            }
            
            // 解析手续费配置
            config.setFeeType(getIntegerValue(configMap, "fee_type", 2));
            config.setFee(getBigDecimalValue(configMap, "fee", new BigDecimal("0.01")));
            config.setUsdtFeeType(getIntegerValue(configMap, "usdt_fee_type", 2));
            config.setUsdtFee(getBigDecimalValue(configMap, "usdt_fee", new BigDecimal("0.01")));
            
            // 解析每日限额配置
            config.setDailyMaxCountLct(getIntegerValue(configMap, "daily_max_count_lct", WithdrawalConstants.DefaultValue.DEFAULT_DAILY_MAX_COUNT));
            config.setDailyMaxCountUsdt(getIntegerValue(configMap, "daily_max_count_usdt", WithdrawalConstants.DefaultValue.DEFAULT_DAILY_MAX_COUNT));
            
            return config;
            
        } catch (Exception e) {
            log.error("解析提现配置失败", e);
            return getDefaultWithdrawalConfig();
        }
    }

    @Override
    public PriceConfig getPriceConfig() {
        try {
            String configJson = optionService.getOption(WithdrawalConstants.ConfigKey.PRICE_CONFIG);
            if (!StringUtils.hasText(configJson)) {
                log.warn("价格配置为空，使用默认配置");
                return getDefaultPriceConfig();
            }

            Map<String, Object> configMap = objectMapper.readValue(configJson, new TypeReference<Map<String, Object>>() {});
            
            PriceConfig config = new PriceConfig();
            config.setLctUsdt(getBigDecimalValue(configMap, "lct_usdt", BigDecimal.ONE));
            config.setUsdtLct(getBigDecimalValue(configMap, "usdt_lct", BigDecimal.ONE));
            
            return config;
            
        } catch (Exception e) {
            log.error("解析价格配置失败", e);
            return getDefaultPriceConfig();
        }
    }



    /**
     * 获取默认提现配置
     */
    private WithdrawalConfig getDefaultWithdrawalConfig() {
        WithdrawalConfig config = new WithdrawalConfig();
        config.setTimeStart(LocalTime.of(9, 0, 0));
        config.setTimeEnd(LocalTime.of(18, 0, 0));
        config.setFeeType(2); // 百分比
        config.setFee(new BigDecimal("0.01")); // 1%
        config.setUsdtFeeType(2); // 百分比
        config.setUsdtFee(new BigDecimal("0.01")); // 1%
        config.setDailyMaxCountLct(WithdrawalConstants.DefaultValue.DEFAULT_DAILY_MAX_COUNT);
        config.setDailyMaxCountUsdt(WithdrawalConstants.DefaultValue.DEFAULT_DAILY_MAX_COUNT);
        return config;
    }

    /**
     * 获取默认价格配置
     */
    private PriceConfig getDefaultPriceConfig() {
        PriceConfig config = new PriceConfig();
        config.setLctUsdt(BigDecimal.ONE);
        config.setUsdtLct(BigDecimal.ONE);
        return config;
    }

    /**
     * 从配置Map中获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> configMap, String key, Integer defaultValue) {
        Object value = configMap.get(key);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof String) {
            try {
                return Integer.valueOf((String) value);
            } catch (NumberFormatException e) {
                log.warn("配置值转换失败，key: {}, value: {}", key, value);
                return defaultValue;
            }
        }
        return defaultValue;
    }

    /**
     * 从配置Map中获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> configMap, String key, BigDecimal defaultValue) {
        Object value = configMap.get(key);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof String) {
            try {
                return new BigDecimal((String) value);
            } catch (NumberFormatException e) {
                log.warn("配置值转换失败，key: {}, value: {}", key, value);
                return defaultValue;
            }
        }
        if (value instanceof Number) {
            return BigDecimal.valueOf(((Number) value).doubleValue());
        }
        return defaultValue;
    }

    @Override
    public Map<String, Object> getInviteConfig() {
        try {
            // 从数据库读取邀请配置
            String configJson = optionService.getOption("invite", "{}");
            
            if (StringUtils.hasText(configJson) && !configJson.equals("{}")) {
                Map<String, Object> config = objectMapper.readValue(configJson, new TypeReference<Map<String, Object>>() {});
                log.debug("从数据库读取邀请配置成功: {}", config);
                return config;
            }
            
            // 如果数据库中没有配置，返回默认配置
            log.warn("数据库中未找到邀请配置，使用默认配置");
            return getDefaultInviteConfig();
            
        } catch (Exception e) {
            log.error("解析邀请配置失败，使用默认配置: {}", e.getMessage(), e);
            return getDefaultInviteConfig();
        }
    }
    
    /**
     * 获取默认邀请配置
     */
    private Map<String, Object> getDefaultInviteConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("effective_direct_user_count", 4); // 每4个用户为一组
        config.put("rate", 0.2); // 20%直推奖励比例
        log.info("使用默认邀请配置: {}", config);
        return config;
    }
    
    @Override
    public Integer getEffectiveDirectUserCount() {
        try {
            Map<String, Object> config = getInviteConfig();
            return getIntegerValue(config, "effective_direct_user_count", -1);
        } catch (Exception e) {
            log.error("获取有效直推用户数配置失败，使用默认值: {}", e.getMessage(), e);
            return -1; // 默认不限制
        }
    }
    


} 