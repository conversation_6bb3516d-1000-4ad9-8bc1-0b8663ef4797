package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lct.finance.mapper.OptionMapper;
import com.lct.finance.model.entity.Option;
import com.lct.finance.service.IOptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统配置服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class OptionServiceImpl extends ServiceImpl<OptionMapper, Option> implements IOptionService {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public String getOption(String key) {
        return getOption(key, null);
    }

    @Override
    public String getOption(String key, String defaultValue) {
        log.debug("获取配置: key={}", key);

        try {
            Option option = this.getOne(new LambdaQueryWrapper<Option>()
                    .eq(Option::getOptionName, key));
            if (option != null && option.getOptionValue() != null) {
                log.debug("找到配置: key={}, value={}", key, option.getOptionValue());
                return option.getOptionValue();
            }

            log.debug("配置不存在，使用默认值: key={}, defaultValue={}", key, defaultValue);
            return defaultValue;

        } catch (Exception e) {
            log.error("获取配置失败: key={}, error={}", key, e.getMessage(), e);
            return defaultValue;
        }
    }

    @Override
    public Map<String, Object> getBurnConfig() {
        log.info("获取销毁配置");

        String configJson = getOption("burn_profit", "{}");

        try {
            Map<String, Object> config = objectMapper.readValue(configJson, new TypeReference<Map<String, Object>>() {
            });
            log.info("销毁配置解析成功: {}", config);
            return config;
        } catch (Exception e) {
            log.error("解析销毁配置失败: {}", e.getMessage(), e);
            // 返回默认配置
            Map<String, Object> defaultConfig = new HashMap<>();
            defaultConfig.put("min_burn", "50");
            return defaultConfig;
        }
    }

    @Override
    public String getMinBurnAmount() {
        log.info("获取最低销毁数量");

        Map<String, Object> burnConfig = getBurnConfig();
        Object minBurn = burnConfig.get("min_burn");

        if (minBurn != null) {
            String minBurnStr = minBurn.toString();
            log.info("最低销毁数量: {}", minBurnStr);
            return minBurnStr;
        }

        log.info("使用默认最低销毁数量: 50");
        return "50";
    }

}