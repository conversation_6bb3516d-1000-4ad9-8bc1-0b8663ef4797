package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.mapper.NewUserGiftMapper;

import com.lct.finance.model.dto.ClaimNewUserGiftResponse;
import com.lct.finance.model.dto.NewUserGiftStatusResponse;
import com.lct.finance.model.entity.NewUserGift;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.entity.Wallet;
import com.lct.finance.model.enums.ErrorCode;
import com.lct.finance.service.INewUserGiftService;
import com.lct.finance.service.IUserService;
import com.lct.finance.service.IWalletService;

import com.lct.finance.service.IRiskAssessmentService;

import com.lct.finance.model.dto.ClaimNewUserGiftRequest;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 新用户礼包服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2025-01-17
 */
@Slf4j
@Service
public class NewUserGiftServiceImpl extends ServiceImpl<NewUserGiftMapper, NewUserGift> implements INewUserGiftService {

    @Autowired
    private IUserService userService;

    @Autowired
    private IWalletService walletService;

    @Autowired
    private IRiskAssessmentService riskAssessmentService;

    /**
     * 新用户礼包默认金额
     */
    private static final BigDecimal DEFAULT_GIFT_AMOUNT = new BigDecimal("5.000000000000000000");

    @Override
    public NewUserGiftStatusResponse checkGiftStatus(Long userId) {
        log.info("检查用户礼包状态: userId={}", userId);
        try {
            // 查询用户信息
            User user = userService.getById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                throw new BusinessException("用户不存在");
            }

            // 如果不是新用户，直接返回老用户状态
            if (!user.isNewUser()) {
                log.info("用户不是新用户: userId={}", userId);
                return NewUserGiftStatusResponse.oldUser();
            }

            // 查询礼包记录
            NewUserGift gift = getOne(
                    new LambdaQueryWrapper<NewUserGift>()
                            .eq(NewUserGift::getUserId, userId)
                            .eq(NewUserGift::getGiftType, 1));

            if (gift == null) {
                // 新用户且没有礼包记录，返回未领取状态
                log.info("新用户未创建礼包记录: userId={}", userId);
                return NewUserGiftStatusResponse.newUserNotClaimed();
            }

            if (gift.isclaimed()) {
                // 已领取
                log.info("用户已领取礼包: userId={}, giftId={}", userId, gift.getId());
                return NewUserGiftStatusResponse.newUserClaimed(gift.getAmount());
            } else {
                // 未领取
                log.info("用户未领取礼包: userId={}, giftId={}", userId, gift.getId());
                return NewUserGiftStatusResponse.newUserNotClaimed();
            }

        } catch (Exception e) {
            log.error("检查用户礼包状态失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException("检查礼包状态失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClaimNewUserGiftResponse claimGift(Long userId) {
        log.info("用户领取礼包: userId={}", userId);

        try {
            // 检查用户是否存在且为新用户
            User user = userService.getById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            if (!user.isNewUser()) {
                log.warn("用户不是新用户: userId={}", userId);
                throw new BusinessException(ErrorCode.GIFT_NOT_NEW_USER);
            }

            // 查询或创建礼包记录
            NewUserGift gift = getOne(
                    new LambdaQueryWrapper<NewUserGift>()
                            .eq(NewUserGift::getUserId, userId)
                            .eq(NewUserGift::getGiftType, 1));
            if (gift == null) {
                gift = createGiftRecord(userId);
                log.info("创建礼包记录: userId={}, giftId={}", userId, gift.getId());
            } else {
                // 检查是否已领取
                if (gift.isclaimed()) {
                    log.warn("用户已领取过礼包: userId={}, giftId={}", userId, gift.getId());
                    throw new BusinessException(ErrorCode.GIFT_ALREADY_CLAIMED);
                }
            }

            // 更新用户钱包余额
            Wallet wallet = walletService.findByUserIdWithLock(userId);
            if (wallet == null) {
                log.error("用户钱包不存在: userId={}", userId);
                throw new BusinessException(ErrorCode.GIFT_CLAIM_FAILED);
            }

            BigDecimal oldBurn = wallet.getBurn();
            BigDecimal newBurn = oldBurn.add(gift.getAmount());
            wallet.setBurn(newBurn);
            walletService.updateById(wallet);

            // 标记礼包为已领取
            gift.markAsClaimed();
            updateById(gift);

            // 更新用户状态：将新用户标识设置为0（不再是新用户）
            if (user.getIsNewUser() == 1) {
                user.setIsNewUser(0);
                userService.updateById(user);
                log.info("用户状态已更新为老用户: userId={}", userId);
            }

            log.info("用户礼包领取成功: userId={}, giftId={}, amount={}, oldBurn={}, newBurn={}",
                    userId, gift.getId(), gift.getAmount(), oldBurn, newBurn);

            return ClaimNewUserGiftResponse.success(gift.getAmount(), newBurn, gift.getClaimedAt());

        } catch (BusinessException e) {
            // 直接抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("用户礼包领取失败: userId={}, error={}", userId, e.getMessage(), e);
            throw new BusinessException(ErrorCode.GIFT_CLAIM_FAILED, e.getMessage());
        }
    }

    @Override
    public NewUserGift createGiftRecord(Long userId) {
        NewUserGift gift = new NewUserGift();
        gift.setUserId(userId);
        gift.setGiftType(1);
        gift.setAmount(DEFAULT_GIFT_AMOUNT);
        gift.setStatus(0);
        gift.setCreatedAt(LocalDateTime.now());
        gift.setUpdatedAt(LocalDateTime.now());

        save(gift);
        return gift;
    }

    @Override
    public boolean canClaimGift(Long userId) {
        try {
            // 检查用户是否存在且为新用户
            User user = userService.getById(userId);
            if (user == null || !user.isNewUser()) {
                return false;
            }

            // 检查是否已领取过礼包
            long claimedCount = count(
                    new LambdaQueryWrapper<NewUserGift>()
                            .eq(NewUserGift::getUserId, userId)
                            .eq(NewUserGift::getGiftType, 1)
                            .eq(NewUserGift::getStatus, 1));
            return claimedCount == 0;

        } catch (Exception e) {
            log.error("检查用户礼包领取条件失败: userId={}, error={}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public NewUserGift markGiftAsClaimed(Long giftId) {
        NewUserGift gift = getById(giftId);
        if (gift != null) {
            gift.markAsClaimed();
            updateById(gift);
        }
        return gift;
    }

    @Override
    @Transactional
    public ApiResponse<ClaimNewUserGiftResponse> claimGiftWithValidation(ClaimNewUserGiftRequest request,
            String clientIp) {
        try {
            User currentUser = UserContext.getCurrentUser();
            log.info("领取新用户礼包请求: {}, nonce={}",
                    UserContext.getCurrentUserInfo(), request.getNonce());

            // // 统一验证（签名验证 + 风险评估 + 日志记录）
            // OperationValidationRequest validationRequest =
            // OperationValidationRequest.builder()
            // .address(currentUser.getUserAddress())
            // .operation("CLAIM_GIFT")
            // .message(request.getMessage())
            // .signature(request.getSign())
            // .nonce(request.getNonce())
            // .clientIp(clientIp)
            // .deviceFingerprint(request.getDeviceFingerprint())
            // .performRiskAssessment(true)
            // .recordOperationLog(false) // 领取结果日志单独处理
            // .build();
            //
            // OperationValidationResult validationResult = operationValidationService
            // .validateOperation(validationRequest);
            //
            // if (!validationResult.isSuccess()) {
            // log.warn("礼包领取签名验证失败: address={}, reason={}",
            // currentUser.getUserAddress(), validationResult.getFailureReason());
            // throw new BusinessException(validationResult.getFailureReason());
            // }

            // 处理礼包领取流程
            ClaimNewUserGiftResponse response = claimGift(currentUser.getId());

            // // 记录操作日志
            // riskAssessmentService.recordOperationLog(
            // currentUser.getUserAddress(),
            // "CLAIM_GIFT",
            // response.getGiftAmount().toString(),
            // clientIp,
            // validationResult.getRiskLevel(),
            // 1 // 成功状态
            // );
            //
            // // 如果风险级别高，记录警告日志
            // if (validationResult.isRequiresAdditionalVerification()) {
            // log.warn("检测到高风险礼包领取: address={}, clientIp={}, riskLevel={}, riskFactors={}",
            // currentUser.getUserAddress(), clientIp, validationResult.getRiskLevel(),
            // validationResult.getRiskResult() != null
            // ? String.join(", ", validationResult.getRiskResult().getRiskFactors())
            // : "");
            // }

            log.info("成功领取新用户礼包: userId={}, giftAmount={}, newBalance={}",
                    currentUser.getId(), response.getGiftAmount(), response.getNewBalance());

            return ApiResponse.success(response);

        } catch (BusinessException e) {
            log.error("领取新用户礼包失败: userId={}, code={}, message={}",
                    UserContext.getCurrentUserId(), e.getCode(), e.getMessage());
            return ApiResponse.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("领取新用户礼包失败: userId={}", UserContext.getCurrentUserId(), e);
            return ApiResponse.error(ErrorCode.GIFT_CLAIM_FAILED.getCode(), "礼包领取失败: " + e.getMessage());
        }
    }
}