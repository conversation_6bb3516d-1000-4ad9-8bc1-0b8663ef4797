package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.BannerMapper;
import com.lct.finance.model.entity.Banner;
import com.lct.finance.service.IBannerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 轮播图服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service

public class BannerServiceImpl extends ServiceImpl<BannerMapper, Banner> implements IBannerService {

    @Override
    public List<Banner> getVisibleBanners(String language, Integer limit) {
        log.info("获取可显示的轮播图: language={}, limit={}", language, limit);
        
        LambdaQueryWrapper<Banner> queryWrapper = new LambdaQueryWrapper<Banner>()
                .eq(Banner::getShow, 1); // 只查询显示状态的轮播图
        
        // 语言过滤
        if (language != null && !language.trim().isEmpty()) {
            queryWrapper.eq(Banner::getLanguage, language);
        }
        
        // 排序：先按id降序，再按sort_id降序 (与PHP保持一致)
        queryWrapper.orderByDesc(Banner::getId)
                   .orderByDesc(Banner::getSortId);
        
        // 限制数量
        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }
        
        List<Banner> banners = list(queryWrapper);
        log.info("查询到{}条轮播图记录", banners.size());
        
        return banners;
    }


} 