package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.context.UserContext;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.mapper.WalletMapper;
import com.lct.finance.model.constants.CommonConstants;
import com.lct.finance.model.constants.WithdrawalConstants;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.dto.WithdrawalHistoryResponse;
import com.lct.finance.model.dto.WithdrawalRequest;
import com.lct.finance.model.dto.WithdrawalResponse;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.entity.Wallet;
import com.lct.finance.model.entity.Withdrawal;
import com.lct.finance.model.enums.ErrorCode;
import com.lct.finance.model.response.OperationValidationResult;
import com.lct.finance.service.*;
import com.lct.finance.service.handler.WithdrawalOperationHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 钱包服务实现类
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service

public class WalletServiceImpl extends ServiceImpl<WalletMapper, Wallet> implements IWalletService {

    @Autowired
    private IWithdrawalService withdrawalService;
    @Autowired
    private IConfigService configService;
    @Autowired
    private ISignatureService signatureService;
    @Autowired
    private IRedisLockService redisLockService;
    @Autowired
    private IUserService userService;
    @Autowired
    private INonceService nonceService;
    @Autowired
    private IOperationValidationService operationValidationService;

    @Autowired
    private IRiskAssessmentService riskAssessmentService;

    @Autowired
    private WithdrawalOperationHandler withdrawalOperationHandler;

    @Override
    public Wallet findByUserId(Long userId) {
        return getOne(new LambdaQueryWrapper<Wallet>()
                .eq(Wallet::getUserId, userId));
    }

    /**
     * 带行锁查找钱包（类似PHP版本的lockForUpdate）
     *
     * @param userId 用户ID
     * @return 钱包实体
     */
    @Transactional
    public Wallet findByUserIdWithLock(Long userId) {
        // 使用SELECT ... FOR UPDATE实现行锁
        return baseMapper.selectByUserIdWithLock(userId);
    }

    /**
     * 批量更新钱包字段（类似PHP版本的up方法）
     *
     * @param walletId 钱包ID
     * @param updates  更新字段映射
     */
    @Transactional
    public void updateWalletFields(Long walletId, Map<String, Object> updates) {
        if (updates == null || updates.isEmpty()) {
            return;
        }

        // 构建更新条件 - 使用updateById，不需要QueryWrapper

        // 创建更新实体
        Wallet updateWallet = new Wallet();
        updateWallet.setId(walletId);

        // 设置更新字段
        updates.forEach((field, value) -> {
            switch (field) {
                case "available":
                    updateWallet.setAvailable((BigDecimal) value);
                    break;
                case "frozen":
                    updateWallet.setFrozen((BigDecimal) value);
                    break;
                case "finance":
                    updateWallet.setFinance((BigDecimal) value);
                    break;
                case "burn":
                    updateWallet.setBurn((BigDecimal) value);
                    break;

                case "profit":
                    updateWallet.setProfit((BigDecimal) value);
                    break;
                case "team_profit":
                    updateWallet.setTeamProfit((BigDecimal) value);
                    break;
                case "direct_invite":
                    updateWallet.setDirectInvite((BigDecimal) value);
                    break;
                case "experience":
                    updateWallet.setExperience((BigDecimal) value);
                    break;
                case "community_chief_rebate":
                    updateWallet.setCommunityChiefRebate((BigDecimal) value);
                    break;
                case "community_chief_experience":
                    updateWallet.setCommunityChiefExperience((BigDecimal) value);
                    break;
                case "agent_available":
                    updateWallet.setAgentAvailable((BigDecimal) value);
                    break;
                case "agent_frozen":
                    updateWallet.setAgentFrozen((BigDecimal) value);
                    break;
            }
        });

        updateById(updateWallet);
        log.info("批量更新钱包字段: walletId={}, updates={}", walletId, updates);
    }

    /**
     * 获取或创建钱包（类似PHP版本的getWallet方法）
     *
     * @param userId   用户ID
     * @param withLock 是否使用行锁
     * @return 钱包实体
     */
    @Override
    @Transactional
    public Wallet getOrCreateWallet(Long userId, boolean withLock) {
        Wallet wallet;

        if (withLock) {
            // 使用行锁查询
            wallet = findByUserIdWithLock(userId);
        } else {
            // 普通查询
            wallet = findByUserId(userId);
        }

        if (wallet != null) {
            return wallet;
        }

        // 钱包不存在，创建新钱包
        return createWallet(userId);
    }

    @Override
    @Transactional
    public Wallet createWallet(Long userId) {
        // 检查是否已存在钱包
        Wallet existingWallet = findByUserId(userId);
        if (existingWallet != null) {
            log.debug("用户钱包已存在: userId={}", userId);
            return existingWallet;
        }

        // 创建新钱包，对应wallets表结构
        Wallet newWallet = new Wallet();
        newWallet.setUserId(userId);

        // 初始化所有余额为0
        newWallet.setAvailable(BigDecimal.ZERO);
        newWallet.setFrozen(BigDecimal.ZERO);
        newWallet.setFinance(BigDecimal.ZERO);
        newWallet.setBurn(BigDecimal.ZERO);
        newWallet.setProfit(BigDecimal.ZERO);
        newWallet.setTeamProfit(BigDecimal.ZERO);
        newWallet.setDirectInvite(BigDecimal.ZERO);
        newWallet.setExperience(BigDecimal.ZERO);
        newWallet.setCommunityChiefRebate(BigDecimal.ZERO);
        newWallet.setCommunityChiefExperience(BigDecimal.ZERO);
        newWallet.setAgentAvailable(BigDecimal.ZERO);
        newWallet.setAgentFrozen(BigDecimal.ZERO);

        save(newWallet);

        log.info("创建新钱包: userId={}", userId);
        return newWallet;
    }

    @Override
    public List<WithdrawalHistoryResponse> getWithdrawalHistory(String date, Integer page, Integer size,
            Integer pageSize) {
        User currentUser = UserContext.getCurrentUser();

        // 处理日期参数转换
        LocalDate localDate = null;
        if (date != null && !date.trim().isEmpty()) {
            try {
                localDate = LocalDate.parse(date);
            } catch (Exception e) {
                log.warn("日期格式错误: {}", date);
                // 继续执行，使用 null 表示无日期过滤
            }
        }

        // 处理分页参数 - 兼容前端的 pageSize 参数
        Integer actualSize = pageSize != null ? pageSize : (size != null ? size : 20);

        // 调用现有的 handleWithdrawalHistory 方法
        return handleWithdrawalHistory(currentUser.getId(), date, page, actualSize, actualSize);
    }

    /**
     * 将Withdrawal实体转换为响应DTO
     */
    private WithdrawalHistoryResponse convertToResponse(Withdrawal withdrawal) {
        // 计算实际到账数量
        String actualAmount = formatDecimal(withdrawal.getActualAmount(), 6);

        // 如果是成功状态且有SWFT桥接订单ID和最终金额，使用最终金额
        if (withdrawal.getStatus().equals(CommonConstants.STATUS_SUCCESS)
                && withdrawal.getSwftBridgersOrderId() != null
                && !withdrawal.getSwftBridgersOrderId().isEmpty()
                && withdrawal.getFinalAmount() != null
                && withdrawal.getFinalAmount().compareTo(BigDecimal.ZERO) > 0) {
            actualAmount = formatDecimal(withdrawal.getFinalAmount(), 6);
        }

        // 确定币种信息
        String coin = "LCT";
        String coinFull = "LCT(BSC)";

        if (!withdrawal.getCoinType().equals(CommonConstants.CoinType.LCT_BSC)) {
            coin = "USDT";
            if (withdrawal.getCoinType().equals(CommonConstants.CoinType.USDT_BSC)) {
                coinFull = "USDT(BSC)";
            } else if (withdrawal.getCoinType().equals(CommonConstants.CoinType.USDT_TRON)) {
                coinFull = "USDT(TRON)";
            }
        }

        // 格式化创建时间
        String createdAt = withdrawal.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        return WithdrawalHistoryResponse.builder()
                .coin(coin)
                .coinFull(coinFull)
                .orderNo(withdrawal.getOrderNo())
                .amount(formatDecimal(withdrawal.getAmount(), 6))
                .coinType(withdrawal.getCoinType())
                .actualAmount(actualAmount)
                .actualLctAmount(formatDecimal(withdrawal.getActualLctAmount(), 6))
                .fee(formatDecimal(withdrawal.getFee(), 6))
                .txhash(withdrawal.getTxhash() != null ? withdrawal.getTxhash() : "")
                .receiveAddress(withdrawal.getReceiveAddress())
                .status(withdrawal.getStatus())
                .createdAt(createdAt)
                .build();
    }

    /**
     * 格式化小数，保留指定位数
     */
    private String formatDecimal(BigDecimal value, int scale) {
        if (value == null) {
            return "0";
        }
        return value.setScale(scale, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
    }

    /**
     * 处理用户提现请求（支持预验证）
     *
     * @param userId              用户ID
     * @param request             提现请求
     * @param userAddress         用户地址
     * @param userChain           用户链类型
     * @param preValidationResult 预验证结果（可选，如果提供则跳过签名验证）
     * @return 提现响应
     */
    @Transactional(rollbackFor = Exception.class)
    public WithdrawalResponse processWithdrawal(Long userId, WithdrawalRequest request, String userAddress,
            String userChain, OperationValidationResult preValidationResult) {
        log.info("处理用户提现请求: userId={}, amount={}, coinType={}, receiveAddress={}, preValidated={}",
                userId, request.getAmount(), request.getType(), request.getReceiveAddress(),
                preValidationResult != null);

        String lockKey = WithdrawalConstants.LockKey.WITHDRAWAL_LOCK_PREFIX + userId;

        try {
            // 1. 获取分布式锁，防止并发提现
            if (!redisLockService.tryLock(lockKey, WithdrawalConstants.LockKey.LOCK_TIMEOUT, TimeUnit.SECONDS)) {
                throw new BusinessException("正在提现中，请稍后稍等，或者稍后再试");
            }

            // 2. 用户状态检查
            User user = userService.findById(userId);
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            // 检查用户是否被全面封禁
            if (userService.isBanned(user)) {
                String banEndTime = user.getBanEndAt() != null
                        ? user.getBanEndAt().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                        : "永久";
                throw new BusinessException("用户已被封禁，解封时间：" + banEndTime);
            }

            // 检查用户是否被禁止提现
            if (userService.isBannedWithdrawal(user)) {
                throw new BusinessException("用户被禁止提现");
            }

            // BSC地址验证 - BSC链要求接收地址必须等于用户地址
            if (WithdrawalConstants.ChainType.BSC.equals(userChain)) {
                if (!userAddress.equalsIgnoreCase(request.getReceiveAddress())) {
                    throw new BusinessException("BSC链提现时，接收地址必须与用户地址一致");
                }
            }

            // 3. 获取配置
            IConfigService.WithdrawalConfig withdrawalConfig = configService.getWithdrawalConfig();
            IConfigService.PriceConfig priceConfig = configService.getPriceConfig();

            // 4. 配置验证
            if (withdrawalConfig == null) {
                throw new BusinessException("提现配置未找到");
            }

            // 5. 提现时间窗口检查
            if (withdrawalConfig.getTimeStart() != null && withdrawalConfig.getTimeEnd() != null) {
                LocalTime currentTime = LocalTime.now();
                LocalTime startTime = withdrawalConfig.getTimeStart();
                LocalTime endTime = withdrawalConfig.getTimeEnd();

                if (currentTime.isBefore(startTime) || currentTime.isAfter(endTime)) {
                    throw new BusinessException("当前时间不在提现时间范围内，提现时间：" +
                            startTime.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")) + "-" +
                            endTime.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")));
                }
            }

            // 6. 验证提现金额范围（使用默认最小金额10）
            BigDecimal minAmount = new BigDecimal(WithdrawalConstants.DefaultValue.MIN_WITHDRAWAL_AMOUNT);
            if (request.getAmount().compareTo(minAmount) < 0) {
                throw new BusinessException("提现金额不能小于" + minAmount);
            }

            // 5. 检查每日提现次数限制
            Integer maxDailyCount = request.getType().equals(WithdrawalConstants.CoinType.LCT)
                    ? withdrawalConfig.getDailyMaxCountLct()
                    : withdrawalConfig.getDailyMaxCountUsdt();
            if (checkDailyWithdrawalLimit(userId, request.getType(), maxDailyCount)) {
                throw new BusinessException("今日提现次数已超过");
            }

            // 6. 获取用户钱包（使用行锁）
            Wallet wallet = getOrCreateWallet(userId, true);
            if (wallet == null) {
                throw new BusinessException("获取用户钱包失败");
            }

            // 7. 计算实际金额和手续费
            Integer feeType = request.getType().equals(WithdrawalConstants.CoinType.LCT) ? withdrawalConfig.getFeeType()
                    : withdrawalConfig.getUsdtFeeType();
            BigDecimal feeValue = request.getType().equals(WithdrawalConstants.CoinType.LCT) ? withdrawalConfig.getFee()
                    : withdrawalConfig.getUsdtFee();
            BigDecimal fee = calculateFee(request.getAmount(), feeType, feeValue);

            // 8. 计算LCT金额和实际到账金额（修正汇率计算逻辑）
            BigDecimal actualLctAmount = BigDecimal.ZERO;
            BigDecimal actualAmount = BigDecimal.ZERO;
            BigDecimal rate = BigDecimal.ONE;

            if (request.getType().equals(WithdrawalConstants.CoinType.LCT)) {
                // LCT提现
                actualLctAmount = request.getAmount().subtract(fee); // 实际扣除的LCT = 申请金额 - 手续费
                actualAmount = actualLctAmount; // 实际到账金额 = 扣除手续费后的LCT
            } else {
                // USDT提现：修正为与PHP版本一致的逻辑
                actualLctAmount = request.getAmount().subtract(fee); // 先扣除手续费

                // 配置验证：USDT提现需要lct_usdt汇率
                if (priceConfig == null || priceConfig.getLctUsdt() == null) {
                    log.error("缺少LCT_USDT价格配置");
                    throw new BusinessException("系统配置错误，请联系管理员");
                }

                rate = priceConfig.getLctUsdt(); // 使用LCT转USDT汇率
                // 实际USDT金额 = LCT金额 * LCT_USDT汇率，保持6位精度
                actualAmount = actualLctAmount.multiply(rate).setScale(6, RoundingMode.DOWN);
            }

            // 检查LCT余额是否足够 - 修复：应该检查申请的全额，与PHP版本一致
            if (wallet.getAvailable().compareTo(request.getAmount()) < 0) {
                throw new BusinessException("可用余额不足");
            }

            // 检查可提百分比限制 - 修复：应该检查申请的全额，与PHP版本一致
            // 对应PHP: $can_withdrawal_amount = getFee($wallet->available, 1,
            // $user->can_withdrawal_percent)
            if (user != null && user.getCanWithdrawalPercent() != null) {
                BigDecimal canWithdrawalAmount = wallet.getAvailable()
                        .multiply(user.getCanWithdrawalPercent())
                        .divide(new BigDecimal("100"), 6, RoundingMode.DOWN);

                if (canWithdrawalAmount.compareTo(request.getAmount()) < 0) {
                    throw new BusinessException("超出可提现比例限制，当前可提现比例为" + user.getCanWithdrawalPercent() + "%");
                }
            }

            // 9. 生成订单号
            String orderNo = generateWithdrawalOrderNo();

            // 10. 创建提现记录
            Withdrawal withdrawal = createWithdrawalRecord(userId, request, actualAmount, actualLctAmount,
                    fee, rate, orderNo, userAddress, priceConfig);

            // 11. 更新钱包余额 - 修复：与PHP版本保持一致，扣除和冻结申请的全额
            // PHP逻辑: 'available' => Db::raw('available - ' . $amount), 'frozen' =>
            // Db::raw('frozen + ' . $amount)
            Map<String, Object> updates = new HashMap<>();
            updates.put("available", wallet.getAvailable().subtract(request.getAmount())); // 扣除申请的全额
            updates.put("frozen", wallet.getFrozen().add(request.getAmount())); // 冻结申请的全额
            updateWalletFields(wallet.getId(), updates);

            log.info("提现请求处理成功: userId={}, orderNo={}, actualAmount={}, actualLctAmount={}",
                    userId, orderNo, actualAmount, actualLctAmount);

            return WithdrawalResponse.success(orderNo, WithdrawalConstants.Status.REVIEWING,
                    request.getAmount(), actualAmount, fee, request.getType(), request.getReceiveAddress());

        } catch (BusinessException e) {
            log.warn("提现请求失败: userId={}, error={}", userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("提现请求处理异常: userId={}", userId, e);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "系统内部错误");
        } finally {
            // 释放分布式锁
            redisLockService.releaseLock(lockKey);
        }
    }

    @Override
    public BigDecimal calculateFee(BigDecimal amount, Integer feeType, BigDecimal feeValue) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        if (feeType == null || feeValue == null) {
            return BigDecimal.ZERO;
        }

        if (feeType.equals(WithdrawalConstants.FeeType.FIXED)) {
            // 固定手续费
            return feeValue;
        } else if (feeType.equals(WithdrawalConstants.FeeType.PERCENTAGE)) {
            // 百分比手续费
            return amount.multiply(feeValue).divide(new BigDecimal("100"), 6, RoundingMode.DOWN);
        }

        return BigDecimal.ZERO;
    }

    @Override
    public boolean checkDailyWithdrawalLimit(Long userId, Integer coinType, Integer maxCount) {
        if (maxCount == null || maxCount <= 0) {
            return false; // 无限制
        }

        // 查询今日提现次数 - 修复：添加状态过滤，只统计有效的提现记录
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);

        long todayCount = withdrawalService.count(new LambdaQueryWrapper<Withdrawal>()
                .eq(Withdrawal::getUserId, userId)
                .eq(Withdrawal::getCoinType, coinType)
                // 添加状态过滤，只统计有效的提现记录（成功、审核中、转账中）
                .in(Withdrawal::getStatus, Arrays.asList(
                        WithdrawalConstants.Status.SUCCESS, // 成功
                        WithdrawalConstants.Status.REVIEWING, // 审核中
                        WithdrawalConstants.Status.TRANSFERRING // 转账中
                ))
                .between(Withdrawal::getCreatedAt, startOfDay, endOfDay));

        return todayCount >= maxCount;
    }

    @Override
    public String generateWithdrawalOrderNo() {
        // 生成格式：WD + 时间戳 + 4位随机数
        String timestamp = String.valueOf(System.currentTimeMillis());
        String random = String.format("%04d", (int) (Math.random() * 10000));
        return "WD" + timestamp + random;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Withdrawal createWithdrawalRecord(Long userId, WithdrawalRequest request,
            BigDecimal actualAmount, BigDecimal actualLctAmount,
            BigDecimal fee, BigDecimal rate, String orderNo,
            String ip, IConfigService.PriceConfig priceConfig) {

        Withdrawal withdrawal = new Withdrawal();
        withdrawal.setUserId(userId);
        withdrawal.setOrderNo(orderNo);
        withdrawal.setAmount(request.getAmount());
        withdrawal.setActualAmount(actualAmount);
        withdrawal.setActualLctAmount(actualLctAmount);
        withdrawal.setFee(fee);
        withdrawal.setRate(rate);
        withdrawal.setCoinType(request.getType());
        withdrawal.setReceiveAddress(request.getReceiveAddress());
        withdrawal.setStatus(WithdrawalConstants.Status.REVIEWING);
        withdrawal.setSourceType(WithdrawalConstants.SourceType.NORMAL);
        withdrawal.setIp(ip);
        withdrawal.setCreatedAt(LocalDateTime.now());
        withdrawal.setUpdatedAt(LocalDateTime.now());

        // 设置价格配置信息
        if (priceConfig != null) {
            withdrawal.setPriceLctUsdt(priceConfig.getLctUsdt());
            withdrawal.setPriceUsdtLct(priceConfig.getUsdtLct());
        }

        withdrawalService.save(withdrawal);

        log.info("创建提现记录: userId={}, orderNo={}, amount={}, actualAmount={}",
                userId, orderNo, request.getAmount(), actualAmount);

        return withdrawal;
    }

    @Override
    public List<WithdrawalHistoryResponse> handleWithdrawalHistory(Long userId, String date, Integer page, Integer size,
            Integer pageSize) {
        log.info("处理提现历史查询: userId={}, date={}, page={}, size={}, pageSize={}",
                userId, date, page, size, pageSize);

        // 参数处理
        Integer finalSize = pageSize != null ? pageSize : (size != null ? size : 20);
        if (finalSize > 100) {
            finalSize = 100; // 限制最大每页数量
        }

        // 日期解析
        LocalDate filterDate = null;
        if (date != null && !date.trim().isEmpty()) {
            try {
                filterDate = LocalDate.parse(date, java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (java.time.format.DateTimeParseException e) {
                log.warn("日期格式错误: {}", date);
                throw new BusinessException("日期格式错误，请使用YYYY-MM-DD格式");
            }
        }

        // 获取提现历史 - 直接使用现有的查询逻辑，避免循环调用
        log.info("获取用户提现历史: userId={}, date={}, page={}, size={}", userId, filterDate, page, finalSize);

        // 构建查询条件
        LambdaQueryWrapper<Withdrawal> queryWrapper = new LambdaQueryWrapper<Withdrawal>()
                .eq(Withdrawal::getUserId, userId)
                .eq(Withdrawal::getSourceType, CommonConstants.SOURCE_TYPE_NORMAL); // 不显示LCT兑U的记录

        // 日期筛选
        if (filterDate != null) {
            queryWrapper.apply("DATE(created_at) = {0}", filterDate);
        }

        // 排序
        queryWrapper.orderByDesc(Withdrawal::getId);

        // 分页查询
        Page<Withdrawal> pageRequest = new Page<>(page, finalSize);
        Page<Withdrawal> withdrawalPage = withdrawalService.page(pageRequest, queryWrapper);

        // 转换为响应DTO
        List<WithdrawalHistoryResponse> historyList = withdrawalPage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        log.info("成功获取提现历史: userId={}, 记录数={}", userId, historyList.size());

        return historyList;
    }

    @Override
    public WithdrawalResponse handleWithdrawal(Long userId, String userAddress,
            BigDecimal amount, String receiveAddress, String type,
            String sign, String message, String nonce, String clientIp, OperationValidationResult validationResult) {
        log.info("处理提现请求: userId={}, amount={}, coinType={}, receiveAddress={}",
                userId, amount, type, receiveAddress);

        // 1. 构建请求对象
        WithdrawalRequest finalRequest = buildWithdrawalRequestFromParams(
                userAddress, String.valueOf(amount), receiveAddress, type, sign, message, nonce);

        // 2. 参数验证
        validateWithdrawalRequest(finalRequest);

        // 3. 获取用户链类型
        String userChain = determineUserChain(userAddress);

        // 4. 调用业务服务处理提现
        WithdrawalResponse response = processWithdrawal(
                userId, finalRequest, userAddress, userChain, validationResult);

        log.info("用户提现成功: userId={}, orderNo={}, actualAmount={}",
                userId, response.getOrderNo(), response.getActualAmount());

        return response;
    }

    /**
     * 构建提现请求对象
     */
    private WithdrawalRequest buildWithdrawalRequestFromParams(String address, String amount,
            String receiveAddress, String type,
            String sign, String message, String nonce) {
        WithdrawalRequest request = new WithdrawalRequest();

        // 设置金额
        if (amount != null && !amount.trim().isEmpty()) {
            try {
                request.setAmount(new java.math.BigDecimal(amount));
            } catch (NumberFormatException e) {
                log.warn("金额格式错误: {}", amount);
                throw new BusinessException("金额格式错误");
            }
        }

        // 设置币种类型
        if (type != null && !type.trim().isEmpty()) {
            try {
                request.setType(Integer.valueOf(type));
            } catch (NumberFormatException e) {
                log.warn("币种类型格式错误: {}", type);
                throw new BusinessException("币种类型格式错误");
            }
        }

        request.setReceiveAddress(receiveAddress);
        request.setSign(sign);
        request.setMessage(message);
        request.setNonce(nonce);

        return request;
    }

    /**
     * 验证提现请求参数
     */
    private void validateWithdrawalRequest(WithdrawalRequest request) {
        if (request.getAmount() == null) {
            throw new BusinessException("提现金额不能为空");
        }
        if (request.getReceiveAddress() == null || request.getReceiveAddress().trim().isEmpty()) {
            throw new BusinessException("接收地址不能为空");
        }
        if (request.getType() == null) {
            throw new BusinessException("币种类型不能为空");
        }
        if (request.getSign() == null || request.getSign().trim().isEmpty()) {
            throw new BusinessException("签名不能为空");
        }
    }

    /**
     * 确定用户链类型
     */
    private String determineUserChain(String address) {
        if (address == null || address.trim().isEmpty()) {
            return WithdrawalConstants.ChainType.BSC; // 默认BSC
        }

        // 根据地址格式判断链类型
        if (address.startsWith("0x") && address.length() == 42) {
            return WithdrawalConstants.ChainType.BSC;
        }

        return WithdrawalConstants.ChainType.BSC; // 默认BSC
    }

    @Override
    @Transactional
    public ApiResponse<WithdrawalResponse> withdrawalWithValidation(WithdrawalRequest request, String clientIp) {
        // 使用模板方法模式的操作处理器，大大简化代码
        return withdrawalOperationHandler.handleOperation(request, clientIp);
    }

}