package com.lct.finance.service.impl;

import com.lct.finance.model.dto.RiskAssessmentRequest;
import com.lct.finance.model.dto.RiskAssessmentResult;
import com.lct.finance.model.dto.SignatureVerificationRequest;
import com.lct.finance.model.dto.SignatureVerificationResult;
import com.lct.finance.model.request.OperationValidationRequest;
import com.lct.finance.model.response.OperationValidationResult;
import com.lct.finance.service.IOperationValidationService;
import com.lct.finance.service.IRiskAssessmentService;
import com.lct.finance.service.ISignatureService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 操作验证服务实现
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class OperationValidationServiceImpl implements IOperationValidationService {

    @Autowired
    private ISignatureService signatureService;

    @Autowired
    private IRiskAssessmentService riskAssessmentService;

    @Override
    public OperationValidationResult validateOperation(OperationValidationRequest request) {
        log.info("开始验证操作: operation={}, address={}, clientIp={}",
                request.getOperation(), request.getAddress(), request.getClientIp());

        try {

            SignatureVerificationResult signatureResult = null;
            if (StringUtils.isNotBlank(request.getSignature())) {
                // 1. 签名验证
                SignatureVerificationRequest verificationRequest = SignatureVerificationRequest.builder()
                        .message(request.getMessage())
                        .signature(request.getSignature())
                        .address(request.getAddress())
                        .operation(request.getOperation())
                        .nonce(request.getNonce())
                        .clientIp(request.getClientIp())
                        .performRiskAssessment(request.isPerformRiskAssessment())
                        .build();

                signatureResult = signatureService.verifySignature(verificationRequest);

                if (!signatureResult.isSuccess()) {
                    log.warn("操作签名验证失败: operation={}, address={}, reason={}",
                            request.getOperation(), request.getAddress(), signatureResult.getFailureReason());

                    // 记录失败日志
                    if (request.isRecordOperationLog()) {
                        recordFailureLog(request, 0);
                    }

                    return OperationValidationResult.builder()
                            .success(false)
                            .failureReason("签名验证失败: " + signatureResult.getFailureReason())
                            .signatureResult(signatureResult)
                            .riskLevel(0)
                            .requiresAdditionalVerification(false)
                            .build();
                }
            }


            // 2. 风险评估（如果需要）
            RiskAssessmentResult riskResult = null;
            if (request.isPerformRiskAssessment()) {
                RiskAssessmentRequest riskRequest = RiskAssessmentRequest.builder()
                        .operation(request.getOperation())
                        .address(request.getAddress())
                        .clientIp(request.getClientIp())
                        .deviceFingerprint(request.getDeviceFingerprint())
                        .amount(request.getAmount())
                        .build();

                riskResult = riskAssessmentService.evaluateRisk(riskRequest);
            }

            // 3. 构建验证结果
            OperationValidationResult validationResult = OperationValidationResult.builder()
                    .success(true)
                    .signatureResult(signatureResult)
                    .riskResult(riskResult)
                    .riskLevel(riskResult != null ? riskResult.getRiskLevel() : 0)
                    .requiresAdditionalVerification(riskResult != null && riskResult.isRequiresAdditionalVerification())
                    .build();

            // 4. 记录成功日志（如果需要）
            if (request.isRecordOperationLog()) {
                recordSuccessLog(request, validationResult);
            }

            // 5. 检查是否需要额外验证
            if (validationResult.isRequiresAdditionalVerification()) {
                log.warn("检测到高风险操作: operation={}, address={}, clientIp={}, riskLevel={}, riskFactors={}",
                        request.getOperation(), request.getAddress(), request.getClientIp(),
                        validationResult.getRiskLevel(),
                        riskResult != null ? String.join(", ", riskResult.getRiskFactors()) : "");
            }

            log.info("操作验证成功: operation={}, address={}, riskLevel={}",
                    request.getOperation(), request.getAddress(), validationResult.getRiskLevel());

            return validationResult;

        } catch (Exception e) {
            log.error("操作验证异常: operation={}, address={}", request.getOperation(), request.getAddress(), e);

            // 记录失败日志
            if (request.isRecordOperationLog()) {
                recordFailureLog(request, 0);
            }

            return OperationValidationResult.builder()
                    .success(false)
                    .failureReason("验证过程异常: " + e.getMessage())
                    .riskLevel(0)
                    .requiresAdditionalVerification(false)
                    .build();
        }
    }

    /**
     * 记录成功日志
     * 使用新事务确保日志记录不受主业务事务影响
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void recordSuccessLog(OperationValidationRequest request, OperationValidationResult validationResult) {
        try {
            int riskLevel = validationResult.getRiskLevel();

            if ("LOGIN".equals(request.getOperation())) {
                // 获取评分详情并转换为JSON字符串
                String riskScoreDetailJson = null;
                if (validationResult.getRiskResult() != null
                        && validationResult.getRiskResult().getScoreDetail() != null) {
                    riskScoreDetailJson = validationResult.getRiskResult().getScoreDetail().toJsonString();
                }

                // 调用带评分详情的登录日志记录方法
                if (riskAssessmentService instanceof com.lct.finance.service.impl.RiskAssessmentServiceImpl) {
                    ((com.lct.finance.service.impl.RiskAssessmentServiceImpl) riskAssessmentService)
                            .recordLoginLogWithDetail(
                                    request.getAddress(),
                                    request.getChain(),
                                    request.getClientIp(),
                                    request.getDeviceFingerprint(),
                                    riskLevel,
                                    1, // 成功状态
                                    riskScoreDetailJson);
                } else {
                    // 降级到原有方法
                    riskAssessmentService.recordLoginLog(
                            request.getAddress(),
                            request.getChain(),
                            request.getClientIp(),
                            request.getDeviceFingerprint(),
                            riskLevel,
                            1 // 成功状态
                    );
                }
            } else {
                // 获取评分详情并转换为JSON字符串
                String riskScoreDetailJson = null;
                if (validationResult.getRiskResult() != null
                        && validationResult.getRiskResult().getScoreDetail() != null) {
                    riskScoreDetailJson = validationResult.getRiskResult().getScoreDetail().toJsonString();
                }

                // 调用带评分详情的操作日志记录方法
                if (riskAssessmentService instanceof com.lct.finance.service.impl.RiskAssessmentServiceImpl) {
                    ((com.lct.finance.service.impl.RiskAssessmentServiceImpl) riskAssessmentService)
                            .recordOperationLogWithDetail(
                                    request.getAddress(),
                                    request.getOperation(),
                                    request.getAmount() != null ? request.getAmount() : "",
                                    request.getClientIp(),
                                    riskLevel,
                                    1, // 成功状态
                                    request.getDeviceFingerprint(),
                                    riskScoreDetailJson);
                } else {
                    // 降级到原有方法
                    riskAssessmentService.recordOperationLog(
                            request.getAddress(),
                            request.getOperation(),
                            request.getAmount() != null ? request.getAmount() : "",
                            request.getClientIp(),
                            riskLevel,
                            1 // 成功状态
                    );
                }
            }
        } catch (Exception e) {
            log.error("记录成功操作日志异常", e);
        }
    }

    /**
     * 记录失败日志
     * 使用新事务确保日志记录不受主业务事务影响
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private void recordFailureLog(OperationValidationRequest request, int riskLevel) {
        try {
            if ("LOGIN".equals(request.getOperation())) {
                riskAssessmentService.recordLoginLog(
                        request.getAddress(),
                        request.getChain(),
                        request.getClientIp(),
                        request.getDeviceFingerprint(),
                        riskLevel,
                        0 // 失败状态
                );
            } else {
                riskAssessmentService.recordOperationLog(
                        request.getAddress(),
                        request.getOperation(),
                        request.getAmount() != null ? request.getAmount() : "",
                        request.getClientIp(),
                        riskLevel,
                        0 // 失败状态
                );
            }
        } catch (Exception e) {
            log.error("记录失败操作日志异常", e);
        }
    }
}