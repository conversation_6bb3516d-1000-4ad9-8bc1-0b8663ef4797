package com.lct.finance.service.impl;

import com.lct.finance.exception.BusinessException;
import com.lct.finance.model.dto.NonceResponse;
import com.lct.finance.service.INonceService;
import com.lct.finance.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.Random;
import java.util.Set;

/**
 * Nonce服务实现类
 * 基于Redis实现nonce的存储、验证和清理功能
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service

public class NonceServiceImpl implements INonceService {

    @Autowired
    private RedisUtils redisUtils;

    /**
     * nonce缓存key前缀
     */
    @Value("${app.nonce.key-prefix:lct:nonce:}")
    private String nonceKeyPrefix;

    /**
     * nonce默认过期时间（秒）
     */
    @Value("${app.nonce.default-expire:600}")
    private long defaultExpireSeconds;

    /**
     * 默认时间窗口（秒）
     */
    @Value("${app.nonce.default-time-window:600}")
    private long defaultTimeWindow;

    /**
     * 单个用户最大nonce数量
     */
    @Value("${app.nonce.max-per-user:100}")
    private long maxNoncePerUser;

    @Override
    public boolean validateAndStoreNonce(String userAddress, String nonce, long timeWindow) {
        long startTime = System.currentTimeMillis();
        log.info("开始验证并存储nonce: userAddress={}, nonce={}, timeWindow={}秒", 
                userAddress, nonce, timeWindow);

        try {
            // 1. 验证参数
            if (userAddress == null || userAddress.trim().isEmpty()) {
                log.warn("验证nonce失败 - 用户地址为空: nonce={}", nonce);
                return false;
            }
            if (nonce == null || nonce.trim().isEmpty()) {
                log.warn("验证nonce失败 - nonce为空: userAddress={}", userAddress);
                return false;
            }

            // 2. 验证nonce时间有效性
            long timeCheckStart = System.currentTimeMillis();
            boolean timeValid = isNonceTimeValid(nonce, timeWindow);
            log.debug("nonce时间有效性检查: 耗时={}ms, 结果={}", 
                    (System.currentTimeMillis() - timeCheckStart), timeValid ? "有效" : "无效");
            
            if (!timeValid) {
                log.warn("验证nonce失败 - 时间无效: nonce={}, timeWindow={}秒", nonce, timeWindow);
                return false;
            }

            // 3. 检查nonce是否已存在
            long existsCheckStart = System.currentTimeMillis();
            boolean exists = isNonceExists(userAddress, nonce);
            log.debug("nonce存在性检查: 耗时={}ms, 结果={}", 
                    (System.currentTimeMillis() - existsCheckStart), exists ? "已存在" : "不存在");
            
            if (exists) {
                log.warn("验证nonce失败 - nonce已存在，可能是重放攻击: userAddress={}, nonce={}", 
                        userAddress, nonce);
                return false;
            }

            // 4. 检查用户nonce数量限制
            long countCheckStart = System.currentTimeMillis();
            long currentCount = getUserNonceCount(userAddress);
            log.debug("用户nonce数量检查: userAddress={}, currentCount={}, maxAllowed={}, 耗时={}ms", 
                    userAddress, currentCount, maxNoncePerUser, 
                    (System.currentTimeMillis() - countCheckStart));
            
            if (currentCount >= maxNoncePerUser) {
                log.warn("用户nonce数量超限，尝试清理: userAddress={}, currentCount={}, maxCount={}", 
                        userAddress, currentCount, maxNoncePerUser);
                
                // 清理过期的nonce
                long cleanStart = System.currentTimeMillis();
                long cleanedCount = cleanExpiredNonces(userAddress);
                log.info("清理过期nonce: userAddress={}, cleanedCount={}, 耗时={}ms", 
                        userAddress, cleanedCount, (System.currentTimeMillis() - cleanStart));
                
                // 重新检查
                currentCount = getUserNonceCount(userAddress);
                if (currentCount >= maxNoncePerUser) {
                    log.error("用户nonce数量仍然超限，拒绝请求: userAddress={}, currentCount={}, maxCount={}", 
                            userAddress, currentCount, maxNoncePerUser);
                    return false;
                }
            }

            // 5. 存储nonce
            long storeStart = System.currentTimeMillis();
            boolean stored = storeNonce(userAddress, nonce, defaultExpireSeconds);
            log.debug("存储nonce: 结果={}, 耗时={}ms", 
                    stored ? "成功" : "失败", (System.currentTimeMillis() - storeStart));
            
            if (stored) {
                long endTime = System.currentTimeMillis();
                log.info("nonce验证并存储成功: userAddress={}, nonce={}, 总耗时={}ms", 
                        userAddress, nonce, (endTime - startTime));
            } else {
                log.error("nonce存储失败: userAddress={}, nonce={}", userAddress, nonce);
            }
            
            return stored;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("验证并存储nonce异常: userAddress={}, nonce={}, 总耗时={}ms, 错误信息={}", 
                    userAddress, nonce, (endTime - startTime), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean isNonceExists(String userAddress, String nonce) {
        try {
            String key = buildNonceKey(userAddress, nonce);
            return redisUtils.hasKey(key);
        } catch (Exception e) {
            log.error("检查nonce是否存在异常: userAddress={}, nonce={}", userAddress, nonce, e);
            return true; // 异常情况下返回true，拒绝请求
        }
    }

    @Override
    public boolean storeNonce(String userAddress, String nonce, long expireSeconds) {
        try {
            String key = buildNonceKey(userAddress, nonce);
            String value = String.valueOf(System.currentTimeMillis());
            
            boolean result = redisUtils.set(key, value, expireSeconds);
            if (result) {
                log.debug("nonce存储成功: key={}, expireSeconds={}", key, expireSeconds);
            } else {
                log.error("nonce存储失败: key={}", key);
            }
            
            return result;
        } catch (Exception e) {
            log.error("存储nonce异常: userAddress={}, nonce={}", userAddress, nonce, e);
            return false;
        }
    }



    @Override
    public long cleanExpiredNonces(String userAddress) {
        try {
            String pattern = buildUserNoncePattern(userAddress);
            Set<String> keys = redisUtils.getKeysByPattern(pattern);
            
            long cleanedCount = 0;
            for (String key : keys) {
                // Redis会自动清理过期的key，这里主要是为了统计
                if (!redisUtils.hasKey(key)) {
                    cleanedCount++;
                }
            }
            
            log.debug("清理用户过期nonce: userAddress={}, cleanedCount={}", userAddress, cleanedCount);
            return cleanedCount;
        } catch (Exception e) {
            log.error("清理用户过期nonce异常: userAddress={}", userAddress, e);
            return 0;
        }
    }



    @Override
    public long getUserNonceCount(String userAddress) {
        try {
            String pattern = buildUserNoncePattern(userAddress);
            Set<String> keys = redisUtils.getKeysByPattern(pattern);
            
            // 过滤掉已过期的key
            long validCount = 0;
            for (String key : keys) {
                if (redisUtils.hasKey(key)) {
                    validCount++;
                }
            }
            
            return validCount;
        } catch (Exception e) {
            log.error("获取用户nonce数量异常: userAddress={}", userAddress, e);
            return 0;
        }
    }

    @Override
    public boolean isNonceTimeValid(String nonce, long timeWindow) {
        try {
            // 解析nonce为时间戳
            long nonceTime = Long.parseLong(nonce);
            long currentTime = System.currentTimeMillis() / 1000; // 转换为秒
            
            // 检查时间窗口
            long timeDiff = Math.abs(currentTime - nonceTime);
            boolean isValid = timeDiff <= timeWindow;
            
            if (!isValid) {
                log.debug("nonce时间无效: nonce={}, nonceTime={}, currentTime={}, timeDiff={}, timeWindow={}", 
                         nonce, nonceTime, currentTime, timeDiff, timeWindow);
            }
            
            return isValid;
        } catch (NumberFormatException e) {
            log.warn("nonce格式无效，无法解析为时间戳: nonce={}", nonce);
            return false;
        } catch (Exception e) {
            log.error("验证nonce时间有效性异常: nonce={}", nonce, e);
            return false;
        }
    }

    /**
     * 构建nonce的Redis key
     * 
     * @param userAddress 用户地址
     * @param nonce nonce值
     * @return Redis key
     */
    private String buildNonceKey(String userAddress, String nonce) {
        return nonceKeyPrefix + userAddress.toLowerCase() + ":" + nonce;
    }

    /**
     * 构建用户nonce的匹配模式
     * 
     * @param userAddress 用户地址
     * @return 匹配模式
     */
    private String buildUserNoncePattern(String userAddress) {
        return nonceKeyPrefix + userAddress.toLowerCase() + ":*";
    }

    /**
     * 获取nonce配置信息（用于监控和调试）
     * 
     * @return 配置信息
     */
    public String getNonceConfig() {
        return String.format("NonceConfig{keyPrefix='%s', defaultExpire=%d, defaultTimeWindow=%d, maxPerUser=%d}", 
                           nonceKeyPrefix, defaultExpireSeconds, defaultTimeWindow, maxNoncePerUser);
    }

    @Override
    public NonceResponse generateNonce(String userAddress, String operation, String chain) {
        long startTime = System.currentTimeMillis();
        log.info("开始生成nonce: userAddress={}, operation={}, chain={}", userAddress, operation, chain);
        
        try {
            // 1. 验证参数
            if (userAddress == null || userAddress.trim().isEmpty()) {
                log.error("生成nonce失败 - 用户地址为空: operation={}", operation);
                throw new IllegalArgumentException("用户地址不能为空");
            }
            if (operation == null || operation.trim().isEmpty()) {
                log.error("生成nonce失败 - 操作类型为空: userAddress={}", userAddress);
                throw new IllegalArgumentException("操作类型不能为空");
            }
            
            // 2. 生成nonce
            long timestamp = System.currentTimeMillis() / 1000;
            String randomPart = generateRandomString(8);
            String addressPrefix = userAddress.substring(0, Math.min(10, userAddress.length()));
            String nonceValue = timestamp + ":" + randomPart + ":" + operation + ":" + addressPrefix;
            log.debug("生成nonce值: timestamp={}, randomPart={}, addressPrefix={}, nonceValue={}", 
                    timestamp, randomPart, addressPrefix, nonceValue);
            
            // 3. 存储nonce到Redis，设置过期时间
            String key = buildOperationNonceKey(userAddress, operation, nonceValue);
            boolean stored = redisUtils.set(key, String.valueOf(timestamp), defaultExpireSeconds);
            if (!stored) {
                log.error("nonce存储到Redis失败: key={}", key);
            } else {
                log.debug("nonce存储到Redis成功: key={}, expireSeconds={}", key, defaultExpireSeconds);
            }
            
            // 获取当前用户的nonce数量，用于监控
            long currentNonceCount = getUserNonceCount(userAddress);
            log.info("用户当前nonce数量: userAddress={}, count={}, maxAllowed={}", 
                    userAddress, currentNonceCount, maxNoncePerUser);
            
            // 4. 构建响应对象
            NonceResponse response = NonceResponse.builder()
                    .nonce(nonceValue)
                    .userAddress(userAddress)
                    .operation(operation)
                    .timestamp(timestamp)
                    .expireIn((int)defaultExpireSeconds)
                    .chain(chain)
                    .build();
            
            long endTime = System.currentTimeMillis();
            log.info("nonce生成完成: userAddress={}, operation={}, nonce={}, 耗时={}ms", 
                    userAddress, operation, nonceValue, (endTime - startTime));
            
            return response;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("生成nonce异常: userAddress={}, operation={}, 耗时={}ms, 错误信息={}", 
                    userAddress, operation, (endTime - startTime), e.getMessage(), e);
            throw new BusinessException("生成nonce失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建操作特定的nonce Redis key
     * 
     * @param userAddress 用户地址
     * @param operation 操作类型
     * @param nonce nonce值
     * @return Redis key
     */
    private String buildOperationNonceKey(String userAddress, String operation, String nonce) {
        return nonceKeyPrefix + userAddress.toLowerCase() + ":" + operation + ":" + nonce;
    }
    
    /**
     * 生成指定长度的随机字符串
     * 
     * @param length 字符串长度
     * @return 随机字符串
     */
    private String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        Random random = new SecureRandom();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }
}