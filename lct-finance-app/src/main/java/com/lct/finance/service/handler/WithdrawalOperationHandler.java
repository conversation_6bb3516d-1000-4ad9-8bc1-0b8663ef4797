package com.lct.finance.service.handler;

import com.lct.finance.context.UserContext;
import com.lct.finance.model.dto.WithdrawalRequest;
import com.lct.finance.model.dto.WithdrawalResponse;
import com.lct.finance.model.request.OperationValidationRequest;
import com.lct.finance.model.response.OperationValidationResult;
import com.lct.finance.service.IWalletService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 提现操作处理器
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class WithdrawalOperationHandler extends AbstractOperationHandler<WithdrawalRequest, WithdrawalResponse> {

    @Autowired
    private IWalletService walletService;

    @Override
    protected OperationValidationRequest buildValidationRequest(WithdrawalRequest request, String clientIp) {
        return OperationValidationRequest.builder()
                .address(UserContext.getCurrentUser().getUserAddress())
                .operation("WITHDRAWAL")
                .message(request.getMessage())
                .signature(request.getSign())
                .nonce(request.getNonce())
                .clientIp(clientIp)
                .deviceFingerprint(request.getDeviceFingerprint())
                .amount(request.getAmount().toString())
                .performRiskAssessment(true)
                .recordOperationLog(false) // 提现结果日志单独处理
                .build();
    }

    @Override
    protected WithdrawalResponse executeBusinessLogic(WithdrawalRequest request, String clientIp,
            OperationValidationResult validationResult) {

        log.info("执行提现业务逻辑: {}, amount={}, coinType={}, receiveAddress={}",
                UserContext.getCurrentUserInfo(), request.getAmount(),
                request.getType(), request.getReceiveAddress());

        // * @param userId 用户ID
        // * @param userAddress 用户地址
        // * @param amount 提现金额字符串
        // * @param receiveAddress 接收地址
        // * @param type 币种类型字符串
        // * @param sign 签名
        // * @param message 签名消息
        // * @param nonce 随机数
        // * @param clientIp 客户端IP
        // * @return 提现响应
        return walletService.handleWithdrawal(
                UserContext.getCurrentUser().getId(),
                UserContext.getCurrentUser().getUserAddress(),
                request.getAmount(),
                request.getReceiveAddress(),
                String.valueOf(request.getType()),
                request.getSign(),
                request.getMessage(),
                request.getNonce(),
                clientIp,
                validationResult);
    }

    @Override
    protected String getOperationName() {
        return "提现";
    }

    @Override
    protected void onHighRiskDetected(OperationValidationRequest request, OperationValidationResult result) {
        // 提现的高风险处理：可以实现额外的安全措施
        log.warn("高风险提现操作被检测到，建议进行额外验证: address={}, riskLevel={}",
                request.getAddress(), result.getRiskLevel());

        // TODO: 可以在这里实现：
        // 1. 发送短信验证码
        // 2. 要求重新登录
        // 3. 暂时冻结账户
        // 4. 发送警报给管理员
    }

    /**
     * 确定用户链类型
     */
    private String determineUserChain(String address) {
        // TODO: 实现更精确的链类型判断逻辑
        return "BSC"; // 默认BSC
    }
}