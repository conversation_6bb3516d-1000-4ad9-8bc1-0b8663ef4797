package com.lct.finance.service.impl;

import com.lct.finance.model.constants.WithdrawalConstants;
import com.lct.finance.model.dto.SignatureVerificationRequest;
import com.lct.finance.model.dto.SignatureVerificationResult;
import com.lct.finance.service.INonceService;
import com.lct.finance.service.ISignatureService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.web3j.crypto.ECDSASignature;
import org.web3j.crypto.Hash;
import org.web3j.crypto.Keys;
import org.web3j.crypto.Sign;
import org.web3j.utils.Numeric;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数字签名验证服务实现类
 * 专注于签名验证功能，不包含风险评估
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Slf4j
@Service
public class SignatureServiceImpl implements ISignatureService {

    private static final String ETH_MESSAGE_PREFIX = "\u0019Ethereum Signed Message:\n";

    @Autowired
    private INonceService nonceService;

    /**
     * 验证签名
     */
    @Override
    public SignatureVerificationResult verifySignature(SignatureVerificationRequest request) {
        if (!WithdrawalConstants.ChainType.BSC.equalsIgnoreCase(request.getChainType())) {
            log.warn("不支持的链类型: {}", request.getChainType());
            return null;
        }
        try {
            if (request == null) {
                return SignatureVerificationResult.failure("请求参数为空");
            }

            // 从请求中获取参数
            String message = request.getMessage();
            String signature = request.getSignature();
            String address = request.getAddress();
            String operation = request.getOperation();
            String nonce = request.getNonce();
            String chainType = request.getChainType();

            log.info("开始验证签名(兼容方法): address={}", address);

            // 如果有操作类型和nonce，调用操作签名验证
            if (StringUtils.hasText(operation) && StringUtils.hasText(nonce)) {
                return verifyOperationSignature(
                        message,
                        signature,
                        address,
                        operation,
                        nonce,
                        chainType);
            }

            // 否则调用普通签名验证
            boolean success = verifySignature(
                    message,
                    signature,
                    address,
                    chainType);

            return success ? SignatureVerificationResult.success()
                    : com.lct.finance.model.dto.SignatureVerificationResult.failure("签名验证失败");
        } catch (Exception e) {
            log.error("签名验证异常: address={}", request.getAddress(), e);
            return SignatureVerificationResult.failure("签名验证过程发生异常: " + e.getMessage());
        }
    }

    @Override
    public String generateMessageTemplate(String operation, Map<String, String> params) {
        if (operation == null || params == null) {
            return "";
        }

        switch (operation.toUpperCase()) {
            case "LOGIN":
                return "You are Login. Nonce:" + params.getOrDefault("nonce", "");
            case "WITHDRAWAL":
                return "You are withdrawing, quantity " + params.getOrDefault("amount", "") + ". Nonce:"
                        + params.getOrDefault("nonce", "");
            case "EXCHANGE":
                return "You are exchanging, quantity " + params.getOrDefault("amount", "") + ". Nonce:"
                        + params.getOrDefault("nonce", "");
            case "BURN":
                return "You are burning, quantity " + params.getOrDefault("amount", "") + ". Nonce:"
                        + params.getOrDefault("nonce", "");
            case "EXCHANGE_PAY":
                return "You are exchanging, quantity " + params.getOrDefault("amount", "unknown") + ". Nonce:"
                        + params.getOrDefault("nonce", "");
            case "EXCHANGE_CREATE":
                return "You are creating exchange order, quantity " + params.getOrDefault("amount", "") + ". Nonce:"
                        + params.getOrDefault("nonce", "");
            default:
                return "";
        }
    }

    private SignatureVerificationResult verifyOperationSignature(String message, String signature,
            String address, String operation,
            String nonce, String chainType) {
        try {
            log.info("开始验证操作签名: operation={}, address={}", operation, address);

            // 1. 参数检查
            if (!StringUtils.hasText(message) ||
                    !StringUtils.hasText(signature) ||
                    !StringUtils.hasText(address) ||
                    !StringUtils.hasText(operation)) {
                log.warn("签名验证参数为空");
                return SignatureVerificationResult.failure("验证参数不完整");
            }

            // 2. 地址格式检查
            if (!isValidEthAddress(address)) {
                return SignatureVerificationResult.failure("无效的地址格式");
            }

            // 3. 如果指定了nonce，验证nonce
            if (StringUtils.hasText(nonce)) {
                boolean nonceValid = nonceService.validateAndStoreNonce(
                        address,
                        nonce,
                        600); // 10分钟时间窗口

                if (!nonceValid) {
                    log.warn("操作签名验证失败 - nonce无效: address={}, nonce={}",
                            address, nonce);
                    return SignatureVerificationResult.failure("无效的nonce，请重新获取");
                }

                // 4. 验证消息格式
                boolean isMessageValid = validateMessageFormat(operation, message, nonce, null);

                if (!isMessageValid) {
                    log.warn("操作签名验证失败 - 消息格式不匹配: message={}", message);
                    return SignatureVerificationResult.failure("签名消息格式不正确");
                }
            }

            // 5. 验证签名
            boolean signatureValid = verifySignature(message, signature, address,
                    StringUtils.hasText(chainType) ? chainType : WithdrawalConstants.ChainType.BSC);

            if (!signatureValid) {
                log.warn("签名验证失败: address={}", address);
                return SignatureVerificationResult.failure("签名验证失败");
            }

            // 6. 返回成功结果
            return SignatureVerificationResult.success();

        } catch (Exception e) {
            log.error("签名验证异常: address={}", address, e);
            return SignatureVerificationResult.failure("签名验证过程发生异常: " + e.getMessage());
        }
    }

    // 验证消息格式
    private boolean validateMessageFormat(String operation, String message, String nonce, String amount) {
        Map<String, String> params = new HashMap<>();

        switch (operation.toUpperCase()) {
            case "LOGIN":
                params.put("nonce", nonce);
                String loginMessage = "You are Login. Nonce:" + nonce;
                return loginMessage.equals(message);

            case "WITHDRAWAL":
                // 从消息中提取金额
                String amountPattern = "You are withdrawing, quantity (.*?)\\.\\s+Nonce:";
                Pattern pattern = Pattern.compile(amountPattern);
                Matcher matcher = pattern.matcher(message);

                if (matcher.find()) {
                    String extractedAmount = matcher.group(1);
                    params.put("amount", extractedAmount);
                    params.put("nonce", nonce);

                    String expectedMessage = generateMessageTemplate(operation, params);
                    return expectedMessage.equals(message);
                }
                return false;

            case "EXCHANGE":
                // 从消息中提取金额
                String exchangePattern = "You are exchanging, quantity (.*?)\\.\\s+Nonce:";
                Pattern exchangePatternObj = Pattern.compile(exchangePattern);
                Matcher exchangeMatcher = exchangePatternObj.matcher(message);

                if (exchangeMatcher.find()) {
                    String extractedAmount = exchangeMatcher.group(1);
                    params.put("amount", extractedAmount);
                    params.put("nonce", nonce);

                    String expectedMessage = generateMessageTemplate(operation, params);
                    return expectedMessage.equals(message);
                }
                return false;

            case "BURN":
                // 从消息中提取金额
                String burnPattern = "You are burning, quantity (.*?)\\.\\s+Nonce:";
                Pattern burnPatternObj = Pattern.compile(burnPattern);
                Matcher burnMatcher = burnPatternObj.matcher(message);

                if (burnMatcher.find()) {
                    String extractedAmount = burnMatcher.group(1);
                    params.put("amount", extractedAmount);
                    params.put("nonce", nonce);

                    String expectedMessage = generateMessageTemplate(operation, params);
                    return expectedMessage.equals(message);
                }
                return false;

            case "EXCHANGE_PAY":
                // 从消息中提取金额（支持unknown金额）
                String exchangePayPattern = "You are exchanging, quantity (.*?)\\.\\s+Nonce:";
                Pattern exchangePayPatternObj = Pattern.compile(exchangePayPattern);
                Matcher exchangePayMatcher = exchangePayPatternObj.matcher(message);

                if (exchangePayMatcher.find()) {
                    String extractedAmount = exchangePayMatcher.group(1);
                    params.put("amount", extractedAmount);
                    params.put("nonce", nonce);

                    String expectedMessage = generateMessageTemplate(operation, params);
                    return expectedMessage.equals(message);
                }
                return false;

            case "EXCHANGE_CREATE":
                // 从消息中提取金额
                String exchangeCreatePattern = "You are creating exchange order, quantity (.*?)\\.\\s+Nonce:";
                Pattern exchangeCreatePatternObj = Pattern.compile(exchangeCreatePattern);
                Matcher exchangeCreateMatcher = exchangeCreatePatternObj.matcher(message);

                if (exchangeCreateMatcher.find()) {
                    String extractedAmount = exchangeCreateMatcher.group(1);
                    params.put("amount", extractedAmount);
                    params.put("nonce", nonce);

                    String expectedMessage = generateMessageTemplate(operation, params);
                    return expectedMessage.equals(message);
                }
                return false;

            default:
                log.warn("不支持的操作类型: {}", operation);
                return false;
        }
    }

    @Override
    public boolean verifySignature(String message, String signature, String address, String chainType) {
        return verifyBscSignature(message, signature, address);
    }

    // 内部方法：验证BSC签名
    private boolean verifyBscSignature(String message, String signature, String address) {
        try {
            if (!StringUtils.hasText(message) || !StringUtils.hasText(signature) || !StringUtils.hasText(address)) {
                log.warn("BSC签名验证参数为空");
                return false;
            }

            // 移除0x前缀
            String cleanSignature = Numeric.cleanHexPrefix(signature);

            // 解析签名
            byte v = (byte) Integer.parseInt(cleanSignature.substring(128, 130), 16);
            byte[] r = Numeric.hexStringToByteArray(cleanSignature.substring(0, 64));
            byte[] s = Numeric.hexStringToByteArray(cleanSignature.substring(64, 128));

            ECDSASignature ecdsaSignature = new ECDSASignature(new BigInteger(1, r), new BigInteger(1, s));

            // 构建以太坊消息前缀
            byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
            byte[] prefix = (ETH_MESSAGE_PREFIX + messageBytes.length).getBytes(StandardCharsets.UTF_8);
            byte[] prefixedMessage = new byte[prefix.length + messageBytes.length];
            System.arraycopy(prefix, 0, prefixedMessage, 0, prefix.length);
            System.arraycopy(messageBytes, 0, prefixedMessage, prefix.length, messageBytes.length);

            // 计算消息哈希
            byte[] messageHash = Hash.sha3(prefixedMessage);

            // 恢复公钥
            BigInteger publicKey = Sign.recoverFromSignature(
                    v - 27,
                    ecdsaSignature,
                    messageHash);

            if (publicKey == null) {
                log.warn("无法从签名恢复公钥");
                return false;
            }

            // 从公钥计算地址
            String recoveredAddress = "0x" + Keys.getAddress(publicKey);

            // 比较地址（不区分大小写）
            boolean matches = recoveredAddress.equalsIgnoreCase(address);
            if (!matches) {
                log.warn("地址不匹配: expected={}, actual={}", address, recoveredAddress);
            }

            return matches;

        } catch (Exception e) {
            log.error("验证BSC签名异常: {}", e.getMessage(), e);
            return false;
        }
    }

    private boolean isValidEthAddress(String address) {
        if (address == null || address.isEmpty()) {
            return false;
        }

        // 以太坊地址格式检查：0x开头，后跟40个16进制字符
        String pattern = "^0x[0-9a-fA-F]{40}$";
        return address.matches(pattern);
    }
}