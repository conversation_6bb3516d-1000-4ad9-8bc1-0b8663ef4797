package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lct.finance.mapper.NoticeMapper;

import com.lct.finance.model.entity.Notice;
import com.lct.finance.model.entity.NoticeModal;
import com.lct.finance.service.INoticeService;
import com.lct.finance.service.INoticeModalService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.lct.finance.model.dto.NoticeListResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.stream.Collectors;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 公告服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class NoticeServiceImpl extends ServiceImpl<NoticeMapper, Notice> implements INoticeService {

    @Autowired
    private INoticeModalService noticeModalService;

    @Override
    public List<Notice> getNoticesList(String language, Integer page, Integer size, String date) {
        log.info("获取公告列表: language={}, page={}, size={}, date={}", language, page, size, date);

        // 构建查询条件
        LambdaQueryWrapper<Notice> queryWrapper = new LambdaQueryWrapper<>();

        // 语言筛选
        if (language != null && !language.trim().isEmpty()) {
            queryWrapper.eq(Notice::getLanguage, language);
        }

        // 显示状态筛选 (show = 1) - 使用反引号包围SQL关键字
        queryWrapper.eq(Notice::getShow, 1);

        // 日期筛选
        if (date != null && !date.trim().isEmpty()) {
            try {
                LocalDate filterDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                queryWrapper.apply("DATE(created_at) = {0}", filterDate);
            } catch (Exception e) {
                log.warn("日期格式错误: {}", date, e);
            }
        }

        // 排序：按ID降序
        queryWrapper.orderByDesc(Notice::getId);

        // 分页处理
        int pageNum = page != null && page > 0 ? page : 1;
        int pageSize = size != null && size > 0 ? Math.min(size, 20) : 20; // 限制最大20条
        int offset = (pageNum - 1) * pageSize;

        queryWrapper.last("LIMIT " + pageSize + " OFFSET " + offset);

        List<Notice> notices = this.list(queryWrapper);
        log.info("查询到公告数量: {}", notices.size());

        return notices;
    }

    @Override
    public List<Notice> getFlaggedNotices(String language, Integer limit) {
        log.info("获取首页推荐公告: language={}, limit={}", language, limit);

        LambdaQueryWrapper<Notice> queryWrapper = new LambdaQueryWrapper<Notice>()
                .eq(Notice::getShow, 1)
                .eq(Notice::getFlag, 1);

        // 语言筛选
        if (language != null && !language.trim().isEmpty()) {
            queryWrapper.eq(Notice::getLanguage, language);
        }

        // 显示状态和推荐标志筛选
        queryWrapper.orderByDesc(Notice::getSortId)
                .orderByDesc(Notice::getId);

        // 限制数量
        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }

        List<Notice> notices = this.list(queryWrapper);
        log.info("查询到推荐公告数量: {}", notices.size());

        return notices;
    }

    @Override
    public NoticeModal getNoticeModal(String language) {
        log.info("获取公告弹框: language={}", language);

        LambdaQueryWrapper<NoticeModal> queryWrapper = new LambdaQueryWrapper<NoticeModal>()
                .eq(NoticeModal::getShow, 1);

        // 语言筛选
        if (language != null && !language.trim().isEmpty()) {
            queryWrapper.eq(NoticeModal::getLanguage, language);
        }

        // 显示状态筛选
        queryWrapper.orderByDesc(NoticeModal::getId)
                .last("LIMIT 1");

        NoticeModal noticeModal = noticeModalService.getOne(queryWrapper);

        if (noticeModal == null) {
            // 返回空的NoticeModal对象
            noticeModal = new NoticeModal();
            noticeModal.setTitle("");
            noticeModal.setContent("");
        }

        log.info("获取公告弹框完成: title={}", noticeModal.getTitle());
        return noticeModal;
    }

    @Override
    public List<NoticeListResponse> getNotices(Integer page, Integer size, String date, String language) {
        try {
            log.info("获取公告列表请求: page={}, size={}, date={}, language={}", page, size, date, language);

            // 参数验证
            if (page == null || page < 1) {
                page = 1;
            }
            if (size == null || size < 1) {
                size = 20;
            }
            if (size > 20) {
                size = 20; // 限制最大20条，与旧接口保持一致
            }

            // 调用服务获取数据
            List<Notice> notices = getNoticesList(language, page, size, date);

            // 转换为响应DTO - 只返回title, content, created_at字段
            List<NoticeListResponse> responseList = notices.stream()
                    .map(notice -> NoticeListResponse.builder()
                            .title(notice.getTitle())
                            .content(notice.getContent())
                            .createdAt(notice.getCreatedAt())
                            .build())
                    .collect(Collectors.toList());

            log.info("成功获取公告列表，数量: {}", responseList.size());
            return responseList;

        } catch (Exception e) {
            log.error("获取公告列表失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取公告列表失败");
        }
    }

    /**
     * 从请求中获取语言标识
     * 支持多种方式：Accept-Language头、language参数等
     */
    private String getLanguageFromRequest(HttpServletRequest request) {
        // 优先从请求参数获取
        String language = request.getParameter("language");
        if (language != null && !language.trim().isEmpty()) {
            return convertLanguageCode(language);
        }

        // 从Accept-Language头获取
        String acceptLanguage = request.getHeader("Accept-Language");
        if (acceptLanguage != null && !acceptLanguage.trim().isEmpty()) {
            // 取第一个语言标识
            String firstLang = acceptLanguage.split(",")[0].trim();
            if (firstLang.startsWith("zh")) {
                return "zh_CN";
            } else if (firstLang.startsWith("en")) {
                return "en_US";
            }
        }

        // 默认返回中文
        return "zh_CN";
    }

    /**
     * 转换语言代码
     * 前端传入: zh, en
     * 数据库存储: zh_CN, en_US
     */
    private String convertLanguageCode(String language) {
        if (language == null) {
            return "zh_CN";
        }

        switch (language.toLowerCase()) {
            case "zh":
                return "zh_CN";
            case "en":
                return "en_US";
            default:
                return "zh_CN";
        }
    }
}