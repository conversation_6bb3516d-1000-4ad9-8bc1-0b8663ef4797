package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.ExchangeStatisticsMapper;
import com.lct.finance.model.entity.ExchangeStatistics;
import com.lct.finance.service.IExchangeStatisticsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 兑换统计服务实现类
 * 使用MyBatis-Plus Lambda表达式实现类型安全查询
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-01
 */
@Service
public class ExchangeStatisticsServiceImpl extends ServiceImpl<ExchangeStatisticsMapper, ExchangeStatistics>
        implements IExchangeStatisticsService {

    @Override
    @Transactional
    public ExchangeStatistics getUserExchangeStat(Long userId, Integer exchangeType) {
        // 🎯 实现PHP的firstOrCreate逻辑
        LambdaQueryWrapper<ExchangeStatistics> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExchangeStatistics::getUserId, userId)
                .eq(ExchangeStatistics::getExchangeType, exchangeType)
                .last("LIMIT 1");

        ExchangeStatistics existing = this.getOne(wrapper);
        if (existing != null) {
            return existing;
        }

        // 不存在则自动创建，完全匹配PHP的默认值逻辑
        ExchangeStatistics newStat = ExchangeStatistics.builder()
                .userId(userId)
                .exchangeType(exchangeType)
                .totalDepositAmount(BigDecimal.ZERO) // PHP: 'total_deposit_amount' => 0
                .totalReceiveAmount(BigDecimal.ZERO) // PHP: 'total_receive_amount' => 0
                .burnNeedExchangeRemaining(BigDecimal.ZERO) // PHP: 'burn_need_exchange_remaining' => 0
                .build();

        // 保存到数据库
        this.save(newStat);

        // 🔧 PHP中有这个刷新逻辑，确保字段完整
        if (newStat.getBurnNeedExchangeRemaining() == null) {
            newStat = this.getById(newStat.getId());
        }

        return newStat;
    }

    @Override
    @Transactional
    public boolean deductBurnExchangeRemaining(Long statId, BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        int affected = baseMapper.deductBurnExchangeRemaining(statId, amount);
        return affected > 0;
    }

    @Override
    public BigDecimal getBurnExchangeRemaining(Long userId, Integer exchangeType) {
        ExchangeStatistics stat = getUserExchangeStat(userId, exchangeType);
        if (stat == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal remaining = stat.getBurnNeedExchangeRemaining();
        return remaining != null ? remaining : BigDecimal.ZERO;
    }
}