package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.UserLoginLogMapper;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.entity.UserLoginLog;
import com.lct.finance.service.IUserLoginLogService;
import com.lct.finance.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 用户登录日志服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-08-28
 */
@Slf4j
@Service
public class UserLoginLogServiceImpl extends ServiceImpl<UserLoginLogMapper, UserLoginLog>
        implements IUserLoginLogService {

    @Autowired
    private IUserService userService;

    @Override
    public UserLoginLog getLastLoginByAddress(String userAddress) {
        // 使用LambdaQueryWrapper替代原有的Mapper方法
        return this.lambdaQuery()
                .eq(UserLoginLog::getUserAddress, userAddress)
                .orderByDesc(UserLoginLog::getLoginTime)
                .last("LIMIT 1")
                .one();
    }

    @Override
    public String getLastLoginIp(String userAddress) {
        UserLoginLog lastLogin = getLastLoginByAddress(userAddress);
        return lastLogin != null ? lastLogin.getClientIp() : null;
    }

    @Override
    public String getLastLoginLocation(String userAddress) {
        UserLoginLog lastLogin = getLastLoginByAddress(userAddress);
        return lastLogin != null ? lastLogin.getIpLocation() : null;
    }

    @Override
    public String getLastDeviceFingerprint(String userAddress) {
        UserLoginLog lastLogin = getLastLoginByAddress(userAddress);
        return lastLogin != null ? lastLogin.getDeviceFingerprint() : null;
    }

    @Override
    public void recordLoginLog(String address, String chainType, String clientIp,
            String deviceFingerprint, int riskLevel, int status) {
        recordLoginLog(address, chainType, clientIp, deviceFingerprint, riskLevel, status, null);
    }

    /**
     * 记录用户登录日志（包含评分详情）
     */
    public void recordLoginLog(String address, String chainType, String clientIp,
            String deviceFingerprint, int riskLevel, int status, String riskScoreDetail) {
        try {
            log.info("记录用户登录日志: address={}, chainType={}, clientIp={}, deviceFingerprint={}, riskLevel={}, status={}",
                    address, chainType, clientIp, deviceFingerprint, riskLevel, status);

            // 详细调试deviceFingerprint
            if (deviceFingerprint == null) {
                log.warn("⚠️  deviceFingerprint为null: address={}", address);
            } else if (deviceFingerprint.trim().isEmpty()) {
                log.warn("⚠️  deviceFingerprint为空字符串: address={}", address);
            } else {
                log.info("✅ deviceFingerprint正常: address={}, fingerprint={}...(前20字符)",
                        address,
                        deviceFingerprint.length() > 20 ? deviceFingerprint.substring(0, 20) : deviceFingerprint);
            }

            UserLoginLog loginLog = new UserLoginLog();
            loginLog.setUserAddress(address);
            loginLog.setChain(chainType);
            loginLog.setClientIp(clientIp);
            loginLog.setDeviceFingerprint(deviceFingerprint);
            loginLog.setRiskLevel(riskLevel);
            loginLog.setStatus(status);
            loginLog.setLoginTime(LocalDateTime.now());

            // 获取IP地理位置 - 使用ClientIpUtils的统一方法
            String location = com.lct.finance.utils.ClientIpUtils.getIpLocation(clientIp);
            loginLog.setIpLocation(location);

            // 设置风险评分详情
            if (riskScoreDetail != null && !riskScoreDetail.trim().isEmpty()) {
                loginLog.setRiskScoreDetail(riskScoreDetail);
                log.debug("保存风险评分详情: address={}, detail={}", address, riskScoreDetail);
            }

            // 根据用户地址查找用户ID
            User user = findUserByAddress(address);
            if (user != null) {
                loginLog.setUserId(user.getId());
                log.info("设置登录日志用户ID: address={}, userId={}", address, user.getId());
            } else {
                log.warn("未找到用户记录，无法设置用户ID: address={}", address);
            }

            // 保存前再次检查deviceFingerprint
            log.info("保存前最终检查: address={}, deviceFingerprint={}, hasRiskDetail={}",
                    address,
                    loginLog.getDeviceFingerprint(),
                    riskScoreDetail != null);

            // 保存日志
            save(loginLog);

            // 保存后验证
            log.info("用户登录日志记录成功: id={}, address={}, location={}, userId={}, deviceFingerprint={}, hasRiskDetail={}",
                    loginLog.getId(), address, location, loginLog.getUserId(),
                    loginLog.getDeviceFingerprint(), riskScoreDetail != null);
        } catch (Exception e) {
            log.error("记录用户登录日志失败: address={}, clientIp={}, deviceFingerprint={}",
                    address, clientIp, deviceFingerprint, e);
        }
    }

    /**
     * 根据用户地址查找用户信息
     */
    private User findUserByAddress(String address) {
        try {
            // 使用IUserService的findByAddress方法
            return userService.findByAddress(address);
        } catch (Exception e) {
            log.warn("查找用户失败: address={}", address, e);
            return null;
        }
    }
}