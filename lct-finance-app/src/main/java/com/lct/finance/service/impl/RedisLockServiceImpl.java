package com.lct.finance.service.impl;

import com.lct.finance.model.constants.WithdrawalConstants;
import com.lct.finance.service.IRedisLockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redis分布式锁服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-12-19
 */
@Slf4j
@Service
public class RedisLockServiceImpl implements IRedisLockService {

    @Autowired
    @Qualifier("redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    
    // 当前线程持有的锁标识
    private final ThreadLocal<String> lockIdentifier = new ThreadLocal<>();

    // Lua脚本：释放锁时验证锁的所有者
    private static final String RELEASE_LOCK_SCRIPT = 
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "    return redis.call('del', KEYS[1]) " +
            "else " +
            "    return 0 " +
            "end";

    @Override
    public boolean tryLock(String key, long timeout, TimeUnit unit) {
        try {
            // 生成唯一标识符
            String identifier = UUID.randomUUID().toString();
            
            // 尝试设置锁
            Boolean success = redisTemplate.opsForValue().setIfAbsent(key, identifier, timeout, unit);
            
            if (Boolean.TRUE.equals(success)) {
                // 保存锁标识符到当前线程
                lockIdentifier.set(identifier);
                log.debug("成功获取锁: key={}, identifier={}, timeout={} {}", key, identifier, timeout, unit);
                return true;
            } else {
                log.debug("获取锁失败: key={}", key);
                return false;
            }
            
        } catch (Exception e) {
            log.error("获取锁异常: key={}", key, e);
            return false;
        }
    }



    @Override
    public boolean releaseLock(String key) {
        try {
            String identifier = lockIdentifier.get();
            if (identifier == null) {
                log.warn("当前线程未持有锁: key={}", key);
                return false;
            }

            // 使用Lua脚本原子性地验证并释放锁
            DefaultRedisScript<Long> script = new DefaultRedisScript<>();
            script.setScriptText(RELEASE_LOCK_SCRIPT);
            script.setResultType(Long.class);

            Long result = redisTemplate.execute(script, Collections.singletonList(key), identifier);
            
            if (result != null && result == 1L) {
                lockIdentifier.remove();
                log.debug("成功释放锁: key={}, identifier={}", key, identifier);
                return true;
            } else {
                log.warn("释放锁失败，锁可能已被其他线程持有: key={}, identifier={}", key, identifier);
                return false;
            }
            
        } catch (Exception e) {
            log.error("释放锁异常: key={}", key, e);
            return false;
        }
    }


} 