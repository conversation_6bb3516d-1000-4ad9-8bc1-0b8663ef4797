package com.lct.finance.service.impl;

import com.lct.finance.service.RateLimiterService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * 基于Redis的漏桶算法限流器服务实现
 * 使用Redis + Lua脚本实现原子性漏桶算法限流
 *
 * <AUTHOR> Finance Team
 * @since 2024-01-20
 */
@Slf4j
@Service
public class LeakyBucketRateLimiterService implements RateLimiterService {

    private static final Logger log = LoggerFactory.getLogger(LeakyBucketRateLimiterService.class);

    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 构造函数，注入RedisTemplate
     */
    public LeakyBucketRateLimiterService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 漏桶算法Lua脚本
     * 算法逻辑：
     * 1. 获取当前时间戳（毫秒）
     * 2. 计算从上次请求到现在漏出的水量（时间差 * 漏出速率）
     * 3. 更新当前水位 = max(0, 旧水位 - 漏出的水量) + 1（新请求）
     * 4. 如果新水位超过桶容量，返回0表示被限流
     * 5. 更新上次请求时间和当前水位
     * 
     * 参数说明:
     * - KEYS[1]: 漏桶的key
     * - ARGV[1]: 桶容量 (count)
     * - ARGV[2]: 漏水速率 (每秒处理多少请求)
     * - ARGV[3]: 过期时间 (秒)
     */
    private static final String LEAKY_BUCKET_LUA_SCRIPT = 
        "-- 获取当前时间戳（毫秒）\n" +
        "local currentTime = redis.call('TIME')\n" +
        "local currentMillis = tonumber(currentTime[1]) * 1000 + math.floor(tonumber(currentTime[2])/1000)\n" +
        "\n" +
        "-- 获取桶信息（上次更新时间和当前水位）\n" +
        "local bucketInfo = redis.call('HMGET', KEYS[1], 'last_time', 'water_level')\n" +
        "local lastTime = bucketInfo[1]\n" +
        "local waterLevel = bucketInfo[2]\n" +
        "\n" +
        "-- 如果桶不存在，初始化桶\n" +
        "if lastTime == false then\n" +
        "    redis.call('HMSET', KEYS[1], 'last_time', currentMillis, 'water_level', 1)\n" +
        "    redis.call('EXPIRE', KEYS[1], ARGV[3])\n" +
        "    return 1\n" +
        "end\n" +
        "\n" +
        "-- 将字符串转换为数字\n" +
        "lastTime = tonumber(lastTime)\n" +
        "waterLevel = tonumber(waterLevel)\n" +
        "local capacity = tonumber(ARGV[1])\n" +
        "local rate = tonumber(ARGV[2])\n" +
        "\n" +
        "-- 计算从上次请求到现在漏出的水量\n" +
        "local timeElapsed = currentMillis - lastTime  -- 经过的时间（毫秒）\n" +
        "local leakedWater = timeElapsed * rate / 1000  -- 漏出的水量 = 时间差(毫秒) * 漏出速率 / 1000\n" +
        "\n" +
        "-- 更新当前水位 = max(0, 旧水位 - 漏出的水量) + 1（新请求）\n" +
        "local newWaterLevel = math.max(0, waterLevel - leakedWater) + 1\n" +
        "\n" +
        "-- 如果新水位超过桶容量，返回0表示被限流\n" +
        "if newWaterLevel > capacity then\n" +
        "    return 0\n" +
        "end\n" +
        "\n" +
        "-- 更新上次请求时间和当前水位\n" +
        "redis.call('HMSET', KEYS[1], 'last_time', currentMillis, 'water_level', newWaterLevel)\n" +
        "redis.call('EXPIRE', KEYS[1], ARGV[3])\n" +
        "\n" +
        "-- 返回剩余容量\n" +
        "return capacity - newWaterLevel";

    /**
     * 尝试获取访问令牌
     *
     * @param key   限流的key
     * @param count 桶容量（最大请求数）
     * @param time  过期时间(秒)
     * @return 是否获取到令牌，true表示允许访问，false表示被限流
     */
    @Override
    public boolean tryAcquire(String key, int count, int time) {
        // 计算漏水速率：默认为容量的1/10，最小为1
        int rate = Math.max(1, count / 10);
        return tryAcquireWithRate(key, count, rate, time);
    }
    
    /**
     * 尝试获取访问令牌（指定漏水速率）
     *
     * @param key   限流的key
     * @param count 桶容量（最大请求数）
     * @param rate  漏水速率（每秒处理多少请求）
     * @param time  过期时间(秒)
     * @return 是否获取到令牌，true表示允许访问，false表示被限流
     */
    @Override
    public boolean tryAcquireWithRate(String key, int count, int rate, int time) {
        RedisScript<Long> redisScript = new DefaultRedisScript<>(LEAKY_BUCKET_LUA_SCRIPT, Long.class);
        
        try {
            // 执行Lua脚本
            Long result = redisTemplate.execute(
                    redisScript,
                    Collections.singletonList(key), // KEYS[1] = key
                    count,                          // ARGV[1] = count（桶容量）
                    rate,                           // ARGV[2] = rate（漏水速率）
                    time                            // ARGV[3] = time（过期时间）
            );
            
            // 返回值为0表示被限流，否则表示剩余容量
            boolean allowed = result != null && result > 0;
            
            if (!allowed) {
                log.debug("Rate limit exceeded for key: {}, capacity: {}, rate: {}/s", key, count, rate);
            }
            
            return allowed;
        } catch (Exception e) {
            // 如果Redis出现异常，为了不影响正常业务，返回true允许访问
            log.error("Leaky bucket limiter error for key: {}, capacity: {}, rate: {}/s", key, count, rate, e);
            return true;
        }
    }
} 