package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.MintProfitMapper;
import com.lct.finance.model.entity.MintProfit;
import com.lct.finance.service.IMintProfitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 每日铸造个人收益服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service

public class MintProfitServiceImpl extends ServiceImpl<MintProfitMapper, MintProfit> implements IMintProfitService {
    
    @Override
    public BigDecimal getTodayPersonalProfit(Long userId, LocalDate profitDate) {
        log.debug("查询用户今日个人收益: userId={}, profitDate={}", userId, profitDate);
        
        List<MintProfit> profits = this.list(
                new LambdaQueryWrapper<MintProfit>()
                        .eq(MintProfit::getUserId, userId.intValue())
                        .eq(MintProfit::getProfitDate, profitDate)
        );
        
        BigDecimal totalProfit = profits.stream()
                .map(MintProfit::getActualAmount)
                .filter(amount -> amount != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        log.debug("用户今日个人收益: userId={}, profitDate={}, totalProfit={}", userId, profitDate, totalProfit);
        return totalProfit;
    }
} 