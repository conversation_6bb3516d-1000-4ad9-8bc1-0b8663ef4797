package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.InviteMapper;

import com.lct.finance.model.entity.Invite;
import com.lct.finance.model.entity.User;
import com.lct.finance.service.IInviteService;
import com.lct.finance.service.IUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 邀请关系服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service

public class InviteServiceImpl extends ServiceImpl<InviteMapper, Invite> implements IInviteService {

    @Autowired
    private IUserService userService;

    @Override
    @Transactional
    public boolean createInviteRelation(User inviter, User invitee) {
        try {
            // 1. 重复关系检查
            if (isInviteRelationExists(inviter.getId(), invitee.getId())) {
                log.warn("邀请关系已存在: inviter={}, invitee={}", inviter.getId(), invitee.getId());
                return true;
            }

            // 2. 获取邀请人的邀请关系链
            List<Invite> inviterChain = list(
                    new LambdaQueryWrapper<Invite>()
                            .eq(Invite::getInviteeUserId, inviter.getId())
                            .orderByAsc(Invite::getLevel));

            // 3. 创建邀请关系记录
            List<Invite> inviteRelations = new ArrayList<>();

            // 直推关系 (level = 1)
            Invite directInvite = new Invite();
            directInvite.setInviterUserId(inviter.getId());
            directInvite.setInviteeUserId(invitee.getId());
            directInvite.setLevel(1);
            directInvite.setRemark("直推邀请");
            inviteRelations.add(directInvite);

            // 间接关系 (level > 1)
            for (Invite parentInvite : inviterChain) {
                Invite indirectInvite = new Invite();
                indirectInvite.setInviterUserId(parentInvite.getInviterUserId());
                indirectInvite.setInviteeUserId(invitee.getId());
                indirectInvite.setLevel(parentInvite.getLevel() + 1);
                indirectInvite.setRemark("间接邀请");
                inviteRelations.add(indirectInvite);
            }

            // 4. 安全批量插入邀请关系（优化性能，每批200条与PHP一致）
            safeBatchInsert(inviteRelations);

            // 5. 更新被邀请人的信息
            invitee.setPid(inviter.getId());
            
            // 构建族谱路径 pid_full_path (使用优化后的格式)
            String pidFullPath = buildPidFullPath(invitee.getId());
            invitee.setPidFullPath(pidFullPath);

            // 构建临时族谱 temporary_pids
            String temporaryPids = buildTemporaryPids(invitee.getId(), inviter);
            invitee.setTemporaryPids(temporaryPids);

            userService.updateById(invitee);

            // 6. 更新所有上级的邀请统计
            updateInviteStatsForChain(inviteRelations);

            log.info("邀请关系创建成功: inviter={}, invitee={}, relations={}, pidFullPath={}, temporaryPids={}",
                    inviter.getId(), invitee.getId(), inviteRelations.size(), pidFullPath, temporaryPids);

            return true;

        } catch (Exception e) {
            log.error("创建邀请关系失败: inviter={}, invitee={}, error={}",
                    inviter.getId(), invitee.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Long countDirectInvitees(Long userId) {
        return count(
                new LambdaQueryWrapper<Invite>()
                        .eq(Invite::getInviterUserId, userId)
                        .eq(Invite::getLevel, 1));
    }

    @Override
    public Long countAllInvitees(Long userId) {
        return count(
                new LambdaQueryWrapper<Invite>().eq(Invite::getInviterUserId, userId));
    }

    @Override
    @Transactional
    public void updateInviteStats(Long userId) {
        try {
            Long directCount = countDirectInvitees(userId);
            Long totalCount = countAllInvitees(userId);

            // 这里可以更新用户表的统计字段，如果需要的话
            log.debug("更新邀请统计: userId={}, directCount={}, totalCount={}",
                    userId, directCount, totalCount);

        } catch (Exception e) {
            log.error("更新邀请统计失败: userId={}, error={}", userId, e.getMessage(), e);
        }
    }

    @Override
    public String buildPidFullPath(Long userId) {
        try {
            List<Invite> chain = list(
                    new LambdaQueryWrapper<Invite>()
                            .eq(Invite::getInviteeUserId, userId)
                            .orderByAsc(Invite::getLevel));
            
            if (chain.isEmpty()) {
                return userId.toString();  // 修复：无邀请关系时直接返回用户ID
            }

            // 修复：构建格式 parent1,parent2,current (不带前后逗号)
            StringBuilder pathBuilder = new StringBuilder();

            // 按level倒序排列，从根节点开始
            chain.stream()
                    .sorted((a, b) -> Integer.compare(b.getLevel(), a.getLevel()))
                    .forEach(invite -> {
                        if (pathBuilder.length() > 0) {
                            pathBuilder.append(",");
                        }
                        pathBuilder.append(invite.getInviterUserId());
                    });

            // 添加当前用户ID
            if (pathBuilder.length() > 0) {
                pathBuilder.append(",");
            }
            pathBuilder.append(userId);

            return pathBuilder.toString();

        } catch (Exception e) {
            log.error("构建族谱路径失败: userId={}, error={}", userId, e.getMessage(), e);
            return userId.toString();  // 修复：错误时返回用户ID
        }
    }

    /**
     * 更新邀请关系链中所有用户的统计
     */
    private void updateInviteStatsForChain(List<Invite> inviteRelations) {
        // 更新所有涉及的邀请人的统计信息
        inviteRelations.stream()
                .map(Invite::getInviterUserId)
                .distinct()
                .forEach(this::updateInviteStats);
    }

    @Override
    public List<Invite> getInvitesByInviterUserId(Long inviterUserId) {
        if (inviterUserId == null) {
            return new ArrayList<>();
        }

        try {
            return list(new LambdaQueryWrapper<Invite>()
                    .eq(Invite::getInviterUserId, inviterUserId)
                    .orderByDesc(Invite::getCreatedAt));
        } catch (Exception e) {
            log.error("获取用户邀请关系失败: inviterUserId={}, error={}", inviterUserId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Invite> getInvitesByInviteeUserId(Long inviteeUserId) {
        if (inviteeUserId == null) {
            return new ArrayList<>();
        }

        try {
            return list(new LambdaQueryWrapper<Invite>()
                    .eq(Invite::getInviteeUserId, inviteeUserId)
                    .orderByAsc(Invite::getLevel));
        } catch (Exception e) {
            log.error("获取用户被邀请关系失败: inviteeUserId={}, error={}", inviteeUserId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Invite> getDirectInvitesByInviterUserId(Long inviterUserId, Integer level) {
        if (inviterUserId == null || level == null) {
            return new ArrayList<>();
        }

        try {
            return list(new LambdaQueryWrapper<Invite>()
                    .eq(Invite::getInviterUserId, inviterUserId)
                    .eq(Invite::getLevel, level)
                    .orderByDesc(Invite::getCreatedAt));
        } catch (Exception e) {
            log.error("获取指定层级邀请关系失败: inviterUserId={}, level={}, error={}",
                    inviterUserId, level, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Integer getTodayDirectInviteCount(Long userId, LocalDate date) {
        if (userId == null || date == null) {
            return 0;
        }

        try {
            Long count = count(new LambdaQueryWrapper<Invite>()
                    .eq(Invite::getInviterUserId, userId)
                    .eq(Invite::getLevel, 1)
                    .ge(Invite::getCreatedAt, date.atStartOfDay())
                    .lt(Invite::getCreatedAt, date.plusDays(1).atStartOfDay()));

            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("获取用户今日直推邀请人数失败: userId={}, date={}, error={}",
                    userId, date, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Integer getTodayTeamInviteCount(Long userId, LocalDate date) {
        if (userId == null || date == null) {
            return 0;
        }

        try {
            Long count = count(new LambdaQueryWrapper<Invite>()
                    .eq(Invite::getInviterUserId, userId)
                    .ge(Invite::getCreatedAt, date.atStartOfDay())
                    .lt(Invite::getCreatedAt, date.plusDays(1).atStartOfDay()));

            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("获取用户今日团队新增人数失败: userId={}, date={}, error={}",
                    userId, date, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<Long> getAllInviteeUserIds(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        try {
            // 查询所有层级的下级用户ID
            // 根据旧代码分析，邀请关系表中存储了所有层级的关系
            // inviter_user_id = userId 的所有记录就是该用户的所有下级
            List<Invite> allInvites = list(new LambdaQueryWrapper<Invite>()
                    .eq(Invite::getInviterUserId, userId)
                    .select(Invite::getInviteeUserId));

            return allInvites.stream()
                    .map(Invite::getInviteeUserId)
                    .distinct()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户所有层级下级用户ID失败: userId={}, error={}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建临时族谱
     * 参考PHP中的逻辑实现
     * 
     * @param userId  用户ID
     * @param inviter 邀请人
     * @return 临时族谱字符串
     */
    @Override
    public String buildTemporaryPids(Long userId, User inviter) {
        try {
            if (inviter == null || userId == null) {
                return String.format("-%s-", userId);
            }

            // 如果邀请人有临时族谱，则在其基础上添加当前用户
            if (inviter.getTemporaryPids() != null && !inviter.getTemporaryPids().isEmpty()) {
                return inviter.getTemporaryPids() + "," + String.format("-%s-", userId);
            } else {
                return String.format("-%s-", userId);
            }
        } catch (Exception e) {
            log.error("构建临时族谱失败: userId={}, error={}", userId, e.getMessage(), e);
            return String.format("-%s-", userId);
        }
    }

    /**
     * 检查邀请关系是否已存在
     * 
     * @param inviterUserId 邀请人ID
     * @param inviteeUserId 被邀请人ID
     * @return 是否已存在
     */
    private boolean isInviteRelationExists(Long inviterUserId, Long inviteeUserId) {
        try {
            Long count = count(new LambdaQueryWrapper<Invite>()
                    .eq(Invite::getInviterUserId, inviterUserId)
                    .eq(Invite::getInviteeUserId, inviteeUserId)
                    .eq(Invite::getLevel, 1)); // 只检查直推关系即可
            
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查邀请关系是否存在失败: inviterUserId={}, inviteeUserId={}", 
                    inviterUserId, inviteeUserId, e);
            return false;
        }
    }

    /**
     * 安全的批量插入，处理重复数据
     * 
     * @param inviteRelations 邀请关系列表
     */
    private void safeBatchInsert(List<Invite> inviteRelations) {
        if (inviteRelations.isEmpty()) {
            return;
        }
        
        try {
            // 使用批量插入，每批200条（与PHP一致）
            saveBatch(inviteRelations, 200);
            log.debug("批量插入邀请关系成功: count={}, batchSize=200", inviteRelations.size());
        } catch (Exception e) {
            // 如果批量插入失败，尝试逐条插入并忽略重复
            log.warn("批量插入邀请关系失败，尝试逐条插入: error={}", e.getMessage());
            
            for (Invite invite : inviteRelations) {
                try {
                    // 检查是否已存在相同关系
                    Long existCount = count(new LambdaQueryWrapper<Invite>()
                            .eq(Invite::getInviterUserId, invite.getInviterUserId())
                            .eq(Invite::getInviteeUserId, invite.getInviteeUserId())
                            .eq(Invite::getLevel, invite.getLevel()));
                    
                    if (existCount == null || existCount == 0) {
                        save(invite);
                    }
                } catch (Exception ex) {
                    log.warn("插入邀请关系失败，可能是重复数据: inviteLevel={}", 
                            invite.getLevel(), ex);
                }
            }
        }
    }
}