package com.lct.finance.service.handler;

import com.lct.finance.exception.BusinessException;
import com.lct.finance.model.dto.ApiResponse;
import com.lct.finance.model.request.OperationValidationRequest;
import com.lct.finance.model.response.OperationValidationResult;
import com.lct.finance.service.IOperationValidationService;
import com.lct.finance.service.IRiskAssessmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 抽象操作处理器 - 模板方法模式
 * 统一处理操作验证流程，子类实现具体业务逻辑
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
public abstract class AbstractOperationHandler<T, R> {

    @Autowired
    protected IOperationValidationService operationValidationService;

    @Autowired
    protected IRiskAssessmentService riskAssessmentService;

    /**
     * 模板方法 - 定义操作处理的标准流程
     * 
     * @param request  业务请求对象
     * @param clientIp 客户端IP
     * @return 统一的API响应
     */
    public final ApiResponse<R> handleOperation(T request, String clientIp) {
        // 1. 构建验证请求
        OperationValidationRequest validationRequest = buildValidationRequest(request, clientIp);

        // 2. 执行统一验证流程
        OperationValidationResult validationResult = executeValidation(validationRequest);

        // 3. 执行具体业务逻辑
        R response = executeBusinessLogic(request, clientIp, validationResult);

        // 4. 记录成功日志
        recordSuccessLog(validationRequest, validationResult);

        return ApiResponse.success(response);
    }

    /**
     * 执行统一验证流程
     */
    private OperationValidationResult executeValidation(OperationValidationRequest validationRequest) {
        log.debug("开始执行{}操作验证: address={}, clientIp={}",
                getOperationName(), validationRequest.getAddress(), validationRequest.getClientIp());

        // 执行验证
        OperationValidationResult validationResult = operationValidationService.validateOperation(validationRequest);

        // 验证失败处理
        if (!validationResult.isSuccess()) {
            log.warn("{}操作验证失败: address={}, reason={}",
                    getOperationName(), validationRequest.getAddress(), validationResult.getFailureReason());

            // 记录失败日志
            recordFailureLog(validationRequest);

            throw new BusinessException("签名验证失败: " + validationResult.getFailureReason());
        }

        // 高风险操作警告
        if (validationResult.isRequiresAdditionalVerification()) {
            handleHighRiskOperation(validationRequest, validationResult);
        }

        log.debug("{}操作验证成功: address={}, riskLevel={}",
                getOperationName(), validationRequest.getAddress(), validationResult.getRiskLevel());

        return validationResult;
    }

    /**
     * 处理高风险操作
     */
    private void handleHighRiskOperation(OperationValidationRequest request, OperationValidationResult result) {
        log.warn("检测到高风险{}操作: address={}, clientIp={}, riskLevel={}, riskFactors={}",
                getOperationName(), request.getAddress(), request.getClientIp(),
                result.getRiskLevel(),
                result.getRiskResult() != null
                        ? String.join(", ", result.getRiskResult().getRiskFactors())
                        : "");

        // 调用钩子方法，允许子类自定义高风险处理
        onHighRiskDetected(request, result);
    }

    /**
     * 记录失败日志
     */
    private void recordFailureLog(OperationValidationRequest request) {
        // 只有在OperationValidationService没有记录失败日志时才记录
        if (!request.isRecordOperationLog()) {
            riskAssessmentService.recordOperationLog(
                    request.getAddress(),
                    request.getOperation(),
                    request.getAmount() != null ? request.getAmount() : "",
                    request.getClientIp(),
                    0, // 默认风险级别
                    0 // 失败状态
            );
        }
    }

    /**
     * 记录成功日志 - 可被子类重写以实现自定义日志策略
     */
    protected void recordSuccessLog(OperationValidationRequest request, OperationValidationResult result) {
        // 只有在OperationValidationService没有记录成功日志时才记录
        if (!request.isRecordOperationLog()) {
            riskAssessmentService.recordOperationLog(
                    request.getAddress(),
                    request.getOperation(),
                    request.getAmount() != null ? request.getAmount() : "",
                    request.getClientIp(),
                    result.getRiskLevel(),
                    1 // 成功状态
            );
        }
    }

    // ========== 抽象方法 - 子类必须实现 ==========

    /**
     * 构建验证请求 - 子类实现具体的请求构建逻辑
     */
    protected abstract OperationValidationRequest buildValidationRequest(T request, String clientIp);

    /**
     * 执行具体业务逻辑 - 子类实现具体的业务处理
     */
    protected abstract R executeBusinessLogic(T request, String clientIp, OperationValidationResult validationResult);

    /**
     * 获取操作名称 - 用于日志记录
     */
    protected abstract String getOperationName();

    // ========== 钩子方法 - 子类可选择性重写 ==========

    /**
     * 高风险操作检测钩子 - 子类可重写实现自定义处理
     */
    protected void onHighRiskDetected(OperationValidationRequest request, OperationValidationResult result) {
        // 默认实现：什么都不做
        // 子类可以重写实现额外的安全措施，如发送警报或强制二次认证
    }
}