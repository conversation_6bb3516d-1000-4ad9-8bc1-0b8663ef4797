package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.mapper.PricesMapper;
import com.lct.finance.model.entity.Prices;
import com.lct.finance.service.IPricesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格历史数据服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class PricesServiceImpl extends ServiceImpl<PricesMapper, Prices> implements IPricesService {

    @Override
    public List<Prices> getDailyLatestPrices(String symbol, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            return baseMapper.getDailyLatestPrices(symbol, startDate, endDate);
        } catch (Exception e) {
            log.error("获取每日最新价格记录失败: symbol={}, startDate={}, endDate={}", symbol, startDate, endDate, e);
            throw e;
        }
    }
}